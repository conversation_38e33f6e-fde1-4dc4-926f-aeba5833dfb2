<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-1725" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 1535.765625 2393.072998046875" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-1725{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-1725 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-1725 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-1725 .error-icon{fill:#a44141;}#graph-1725 .error-text{fill:#ddd;stroke:#ddd;}#graph-1725 .edge-thickness-normal{stroke-width:1px;}#graph-1725 .edge-thickness-thick{stroke-width:3.5px;}#graph-1725 .edge-pattern-solid{stroke-dasharray:0;}#graph-1725 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-1725 .edge-pattern-dashed{stroke-dasharray:3;}#graph-1725 .edge-pattern-dotted{stroke-dasharray:2;}#graph-1725 .marker{fill:lightgrey;stroke:lightgrey;}#graph-1725 .marker.cross{stroke:lightgrey;}#graph-1725 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-1725 p{margin:0;}#graph-1725 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-1725 .cluster-label text{fill:#F9FFFE;}#graph-1725 .cluster-label span{color:#F9FFFE;}#graph-1725 .cluster-label span p{background-color:transparent;}#graph-1725 .label text,#graph-1725 span{fill:#ccc;color:#ccc;}#graph-1725 .node rect,#graph-1725 .node circle,#graph-1725 .node ellipse,#graph-1725 .node polygon,#graph-1725 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-1725 .rough-node .label text,#graph-1725 .node .label text,#graph-1725 .image-shape .label,#graph-1725 .icon-shape .label{text-anchor:middle;}#graph-1725 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-1725 .rough-node .label,#graph-1725 .node .label,#graph-1725 .image-shape .label,#graph-1725 .icon-shape .label{text-align:center;}#graph-1725 .node.clickable{cursor:pointer;}#graph-1725 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-1725 .arrowheadPath{fill:lightgrey;}#graph-1725 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-1725 .flowchart-link{stroke:lightgrey;fill:none;}#graph-1725 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1725 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-1725 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1725 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-1725 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-1725 .cluster text{fill:#F9FFFE;}#graph-1725 .cluster span{color:#F9FFFE;}#graph-1725 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-1725 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-1725 rect.text{fill:none;stroke-width:0;}#graph-1725 .icon-shape,#graph-1725 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1725 .icon-shape p,#graph-1725 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-1725 .icon-shape rect,#graph-1725 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1725 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-1725 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-1725 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-1725 .security&gt;*{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1725 .security span{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1725 .security tspan{fill:#000!important;}#graph-1725 .process&gt;*{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1725 .process span{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1725 .process tspan{fill:#000!important;}#graph-1725 .decision&gt;*{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1725 .decision span{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1725 .decision tspan{fill:#000!important;}#graph-1725 .success&gt;*{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1725 .success span{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1725 .success tspan{fill:#000!important;}#graph-1725 .error&gt;*{fill:#FCE4EC!important;stroke:#C2185B!important;color:#000!important;}#graph-1725 .error span{fill:#FCE4EC!important;stroke:#C2185B!important;color:#000!important;}#graph-1725 .error tspan{fill:#000!important;}</style><g><marker id="graph-1725_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1725_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1725_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1725_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1725_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1725_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M573.13,62L573.13,66.167C573.13,70.333,573.13,78.667,573.13,86.333C573.13,94,573.13,101,573.13,104.5L573.13,108" id="L_REQUEST_EXTRACT_TOKEN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M573.13,166L573.13,170.167C573.13,174.333,573.13,182.667,573.2,190.417C573.271,198.167,573.411,205.334,573.482,208.917L573.552,212.501" id="L_EXTRACT_TOKEN_VALIDATE_TOKEN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M515.777,299.511L442.986,315.237C370.195,330.962,224.613,362.414,151.822,388.806C79.031,415.198,79.031,436.531,79.031,455.865C79.031,475.198,79.031,492.531,79.031,509.865C79.031,527.198,79.031,544.531,79.031,561.865C79.031,579.198,79.031,596.531,79.031,621.786C79.031,647.042,79.031,680.219,79.031,715.396C79.031,750.573,79.031,787.75,79.031,817.005C79.031,846.26,79.031,867.594,79.031,888.927C79.031,910.26,79.031,931.594,79.031,963.388C79.031,995.182,79.031,1037.438,79.031,1079.693C79.031,1121.948,79.031,1164.203,79.031,1195.997C79.031,1227.792,79.031,1249.125,79.031,1268.458C79.031,1287.792,79.031,1305.125,79.031,1332.529C79.031,1359.932,79.031,1397.406,79.031,1436.88C79.031,1476.354,79.031,1517.828,79.031,1557.713C79.031,1597.597,79.031,1635.892,79.031,1674.188C79.031,1712.483,79.031,1750.778,79.031,1775.425C79.031,1800.073,79.031,1811.073,79.031,1816.573L79.031,1822.073" id="L_VALIDATE_TOKEN_TOKEN_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M79.031,1880.073L79.031,1884.24C79.031,1888.406,79.031,1896.74,104.942,1906.06C130.854,1915.381,182.676,1925.689,208.587,1930.843L234.499,1935.997" id="L_TOKEN_ERROR_AUDIT_VIOLATION_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M340.453,1984.073L340.453,1988.24C340.453,1992.406,340.453,2000.74,340.453,2008.406C340.453,2016.073,340.453,2023.073,340.453,2026.573L340.453,2030.073" id="L_AUDIT_VIOLATION_DENY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M611.087,319.908L625.099,332.234C639.112,344.56,667.137,369.212,681.149,387.038C695.161,404.865,695.161,415.865,695.161,421.365L695.161,426.865" id="L_VALIDATE_TOKEN_EXTRACT_USER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M695.161,484.865L695.161,489.031C695.161,493.198,695.161,501.531,695.161,509.198C695.161,516.865,695.161,523.865,695.161,527.365L695.161,530.865" id="L_EXTRACT_USER_LOAD_ROLES_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M695.161,588.865L695.161,593.031C695.161,597.198,695.161,605.531,695.232,613.281C695.302,621.032,695.443,628.198,695.513,631.782L695.583,635.365" id="L_LOAD_ROLES_CHECK_SCHOOL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M638.319,731.085L585.782,746.725C533.244,762.366,428.169,793.646,375.631,819.953C323.094,846.26,323.094,867.594,323.094,888.927C323.094,910.26,323.094,931.594,323.094,963.388C323.094,995.182,323.094,1037.438,323.094,1079.693C323.094,1121.948,323.094,1164.203,323.094,1195.997C323.094,1227.792,323.094,1249.125,323.094,1268.458C323.094,1287.792,323.094,1305.125,323.094,1332.529C323.094,1359.932,323.094,1397.406,323.094,1436.88C323.094,1476.354,323.094,1517.828,323.094,1557.713C323.094,1597.597,323.094,1635.892,323.094,1674.188C323.094,1712.483,323.094,1750.778,323.094,1775.425C323.094,1800.073,323.094,1811.073,323.094,1816.573L323.094,1822.073" id="L_CHECK_SCHOOL_SCHOOL_VIOLATION_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M323.094,1880.073L323.094,1884.24C323.094,1888.406,323.094,1896.74,324.274,1904.441C325.453,1912.142,327.813,1919.21,328.993,1922.744L330.173,1926.279" id="L_SCHOOL_VIOLATION_AUDIT_VIOLATION_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M736.343,747.745L751.82,760.609C767.298,773.472,798.252,799.2,813.729,817.563C829.206,835.927,829.206,846.927,829.206,852.427L829.206,857.927" id="L_CHECK_SCHOOL_GET_PERMISSIONS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M724.951,904.746L672.029,912.777C619.108,920.807,513.265,936.867,460.343,966.025C407.422,995.182,407.422,1037.438,407.422,1079.693C407.422,1121.948,407.422,1164.203,407.422,1195.997C407.422,1227.792,407.422,1249.125,407.422,1268.458C407.422,1287.792,407.422,1305.125,467.47,1329.441C527.518,1353.757,647.613,1385.056,707.661,1400.705L767.709,1416.354" id="L_GET_PERMISSIONS_CHECK_RESOURCE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M782.667,1464.485L750.753,1480.288C718.839,1496.091,655.011,1527.696,623.096,1562.647C591.182,1597.597,591.182,1635.892,591.182,1674.188C591.182,1712.483,591.182,1750.778,591.182,1775.425C591.182,1800.073,591.182,1811.073,591.182,1816.573L591.182,1822.073" id="L_CHECK_RESOURCE_PERMISSION_DENIED_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M591.182,1880.073L591.182,1884.24C591.182,1888.406,591.182,1896.74,567.052,1905.911C542.922,1915.082,494.661,1925.091,470.531,1930.095L446.401,1935.1" id="L_PERMISSION_DENIED_AUDIT_VIOLATION_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M885.032,1478.754L898.667,1492.179C912.301,1505.604,939.57,1532.453,953.279,1551.461C966.987,1570.469,967.136,1581.636,967.211,1587.219L967.285,1592.802" id="L_CHECK_RESOURCE_CHECK_CONTEXT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M922.966,1708.2L904.926,1721.679C886.885,1735.158,850.805,1762.115,832.764,1781.094C814.724,1800.073,814.724,1811.073,814.724,1816.573L814.724,1822.073" id="L_CHECK_CONTEXT_CONTEXT_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M814.724,1880.073L814.724,1884.24C814.724,1888.406,814.724,1896.74,753.347,1907.636C691.969,1918.532,569.215,1931.991,507.838,1938.721L446.461,1945.45" id="L_CONTEXT_ERROR_AUDIT_VIOLATION_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1017.182,1702.73L1042.825,1717.12C1068.468,1731.511,1119.755,1760.292,1145.398,1780.182C1171.042,1800.073,1171.042,1811.073,1171.042,1816.573L1171.042,1822.073" id="L_CHECK_CONTEXT_GRANT_ACCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1171.042,1880.073L1171.042,1884.24C1171.042,1888.406,1171.042,1896.74,1171.042,1904.406C1171.042,1912.073,1171.042,1919.073,1171.042,1922.573L1171.042,1926.073" id="L_GRANT_ACCESS_LOG_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1171.042,1984.073L1171.042,1988.24C1171.042,1992.406,1171.042,2000.74,1171.042,2008.406C1171.042,2016.073,1171.042,2023.073,1171.042,2026.573L1171.042,2030.073" id="L_LOG_SUCCESS_EXECUTE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1171.042,2088.073L1171.042,2092.24C1171.042,2096.406,1171.042,2104.74,1171.042,2112.406C1171.042,2120.073,1171.042,2127.073,1171.042,2130.573L1171.042,2134.073" id="L_EXECUTE_UPDATE_CACHE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1171.042,2192.073L1171.042,2196.24C1171.042,2200.406,1171.042,2208.74,1171.042,2216.406C1171.042,2224.073,1171.042,2231.073,1171.042,2234.573L1171.042,2238.073" id="L_UPDATE_CACHE_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M340.453,2088.073L340.453,2092.24C340.453,2096.406,340.453,2104.74,340.453,2113.656C340.453,2122.573,340.453,2132.073,340.453,2136.823L340.453,2141.573" id="L_DENY_END_DENY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1171.042,2296.073L1171.042,2300.24C1171.042,2304.406,1171.042,2312.74,1171.042,2320.406C1171.042,2328.073,1171.042,2335.073,1171.042,2338.573L1171.042,2342.073" id="L_SUCCESS_END_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M920.555,915.927L941.419,922.094C962.282,928.26,1004.01,940.594,1024.948,952.344C1045.886,964.094,1046.035,975.261,1046.109,980.844L1046.184,986.427" id="L_GET_PERMISSIONS_MATRIX_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M975.069,1098.791L906.001,1116.735C836.932,1134.68,698.794,1170.569,629.725,1194.014C560.656,1217.458,560.656,1228.458,560.656,1233.958L560.656,1239.458" id="L_MATRIX_CREATE_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M990.744,1114.466L965.701,1129.798C940.658,1145.13,890.571,1175.794,865.528,1196.626C840.484,1217.458,840.484,1228.458,840.484,1233.958L840.484,1239.458" id="L_MATRIX_READ_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1079.998,1136.197L1087.025,1147.907C1094.051,1159.617,1108.104,1183.038,1115.13,1200.248C1122.156,1217.458,1122.156,1228.458,1122.156,1233.958L1122.156,1239.458" id="L_MATRIX_UPDATE_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1112.828,1103.367L1162.357,1120.549C1211.886,1137.731,1310.943,1172.095,1360.471,1194.776C1410,1217.458,1410,1228.458,1410,1233.958L1410,1239.458" id="L_MATRIX_DELETE_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M560.656,1297.458L560.656,1301.625C560.656,1305.792,560.656,1314.125,596.365,1332.687C632.074,1351.249,703.491,1380.039,739.2,1394.434L774.908,1408.829" id="L_CREATE_CHECK_CHECK_RESOURCE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M840.484,1297.458L840.484,1301.625C840.484,1305.792,840.484,1314.125,840.555,1321.875C840.625,1329.625,840.765,1336.792,840.836,1340.376L840.906,1343.959" id="L_READ_CHECK_CHECK_RESOURCE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1122.156,1297.458L1122.156,1301.625C1122.156,1305.792,1122.156,1314.125,1086.327,1332.707C1050.497,1351.288,978.838,1380.119,943.008,1394.534L907.179,1408.949" id="L_UPDATE_CHECK_CHECK_RESOURCE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path><path d="M1410,1297.458L1410,1301.625C1410,1305.792,1410,1314.125,1327.986,1334.58C1245.973,1355.035,1081.945,1387.612,999.931,1403.901L917.918,1420.189" id="L_DELETE_CHECK_CHECK_RESOURCE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1725_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(79.03125, 952.9270935058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(695.161458492279, 393.8645935058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(323.09375, 1270.4583435058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(829.2057280540466, 824.9270935058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(591.1822891235352, 1674.1875076293945)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(966.8385391235352, 1559.3020935058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(814.7239532470703, 1789.0729217529297)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1171.0416641235352, 1789.0729217529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(560.6562514305115, 1206.4583435058594)"><g class="label" transform="translate(-26.59895896911621, -12)"><foreignObject width="53.19791793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>CREATE</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(840.4843764305115, 1206.4583435058594)"><g class="label" transform="translate(-18.56770896911621, -12)"><foreignObject width="37.13541793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>READ</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1122.1562514305115, 1206.4583435058594)"><g class="label" transform="translate(-27.42708396911621, -12)"><foreignObject width="54.85416793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>UPDATE</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1410.0000014305115, 1206.4583435058594)"><g class="label" transform="translate(-26.45833396911621, -12)"><foreignObject width="52.91666793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>DELETE</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default" id="flowchart-REQUEST-0" transform="translate(573.130208492279, 35)"><rect class="basic label-container" style="" x="-101.375" y="-27" width="202.75" height="54"></rect><g class="label" style="" transform="translate(-71.375, -12)"><rect></rect><foreignObject width="142.75" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>User Action Request</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-EXTRACT_TOKEN-1" transform="translate(573.130208492279, 139)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-96.42708587646484" y="-27" width="192.8541717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-66.42708587646484, -12)"><rect></rect><foreignObject width="132.8541717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Extract JWT Token</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-VALIDATE_TOKEN-3" transform="translate(573.130208492279, 286.4322967529297)"><polygon points="70.43229293823242,0 140.86458587646484,-70.43229293823242 70.43229293823242,-140.86458587646484 0,-70.43229293823242" class="label-container" transform="translate(-70.43229293823242,70.43229293823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-43.43229293823242, -12)"><rect></rect><foreignObject width="86.86458587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Token Valid?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-TOKEN_ERROR-5" transform="translate(79.03125, 1853.0729217529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-71.03125" y="-27" width="142.0625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-41.03125, -12)"><rect></rect><foreignObject width="82.0625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Token Error</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-AUDIT_VIOLATION-7" transform="translate(340.4531264305115, 1957.0729217529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-102.03125" y="-27" width="204.0625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-72.03125, -12)"><rect></rect><foreignObject width="144.0625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Log Access Violation</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-DENY-9" transform="translate(340.4531264305115, 2061.0729217529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-80.63541793823242" y="-27" width="161.27083587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-50.63541793823242, -12)"><rect></rect><foreignObject width="101.27083587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Access Denied</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-EXTRACT_USER-11" transform="translate(695.161458492279, 457.8645935058594)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-90.47916793823242" y="-27" width="180.95833587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-60.47916793823242, -12)"><rect></rect><foreignObject width="120.95833587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Extract User Info</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-LOAD_ROLES-13" transform="translate(695.161458492279, 561.8645935058594)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-86.3125" y="-27" width="172.625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-56.3125, -12)"><rect></rect><foreignObject width="112.625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Load User Roles</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-CHECK_SCHOOL-15" transform="translate(695.161458492279, 713.3958435058594)"><polygon points="74.53125,0 149.0625,-74.53125 74.53125,-149.0625 0,-74.53125" class="label-container" transform="translate(-74.53125,74.53125)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-47.53125, -12)"><rect></rect><foreignObject width="95.0625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Same School?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-SCHOOL_VIOLATION-17" transform="translate(323.09375, 1853.0729217529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-123.03125" y="-27" width="246.0625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-93.03125, -12)"><rect></rect><foreignObject width="186.0625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>School Boundary Violation</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-GET_PERMISSIONS-21" transform="translate(829.2057280540466, 888.9270935058594)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-104.25521087646484" y="-27" width="208.5104217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-74.25521087646484, -12)"><rect></rect><foreignObject width="148.5104217529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Get Role Permissions</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-CHECK_RESOURCE-23" transform="translate(840.4843764305115, 1434.8802185058594)"><polygon points="87.421875,0 174.84375,-87.421875 87.421875,-174.84375 0,-87.421875" class="label-container" transform="translate(-87.421875,87.421875)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-60.421875, -12)"><rect></rect><foreignObject width="120.84375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Resource Access?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-PERMISSION_DENIED-25" transform="translate(591.1822891235352, 1853.0729217529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-95.05729675292969" y="-27" width="190.11459350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-65.05729675292969, -12)"><rect></rect><foreignObject width="130.11459350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Permission Denied</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-CHECK_CONTEXT-29" transform="translate(966.8385391235352, 1674.1875076293945)"><polygon points="77.88541793823242,0 155.77083587646484,-77.88541793823242 77.88541793823242,-155.77083587646484 0,-77.88541793823242" class="label-container" transform="translate(-77.88541793823242,77.88541793823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-50.88541793823242, -12)"><rect></rect><foreignObject width="101.77083587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Context Valid?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-CONTEXT_ERROR-31" transform="translate(814.7239532470703, 1853.0729217529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-78.484375" y="-27" width="156.96875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-48.484375, -12)"><rect></rect><foreignObject width="96.96875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Context Error</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-GRANT_ACCESS-35" transform="translate(1171.0416641235352, 1853.0729217529297)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-75.71875" y="-27" width="151.4375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-45.71875, -12)"><rect></rect><foreignObject width="91.4375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Grant Access</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-LOG_SUCCESS-37" transform="translate(1171.0416641235352, 1957.0729217529297)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-106.890625" y="-27" width="213.78125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-76.890625, -12)"><rect></rect><foreignObject width="153.78125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Log Successful Access</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-EXECUTE-39" transform="translate(1171.0416641235352, 2061.0729217529297)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-83.29166793823242" y="-27" width="166.58333587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-53.29166793823242, -12)"><rect></rect><foreignObject width="106.58333587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Execute Action</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-UPDATE_CACHE-41" transform="translate(1171.0416641235352, 2165.0729217529297)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-120.25" y="-27" width="240.5" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-90.25, -12)"><rect></rect><foreignObject width="180.5" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update Permission Cache</p></span></div></foreignObject></g></g><g class="node default success" id="flowchart-SUCCESS-43" transform="translate(1171.0416641235352, 2269.0729217529297)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-84.97916793823242" y="-27" width="169.95833587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-54.97916793823242, -12)"><rect></rect><foreignObject width="109.95833587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Access Granted</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-END_DENY-45" transform="translate(340.4531264305115, 2165.0729217529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" rx="19.5" ry="19.5" x="-63.01041793823242" y="-19.5" width="126.02083587646484" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-50.63541793823242, -12)"><rect></rect><foreignObject width="101.27083587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Access Denied</p></span></div></foreignObject></g></g><g class="node default success" id="flowchart-END_SUCCESS-47" transform="translate(1171.0416641235352, 2365.5729217529297)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-67.35416793823242" y="-19.5" width="134.70833587646484" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-54.97916793823242, -12)"><rect></rect><foreignObject width="109.95833587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Access Granted</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-MATRIX-49" transform="translate(1045.7369780540466, 1079.6927185058594)"><polygon points="89.765625,0 179.53125,-89.765625 89.765625,-179.53125 0,-89.765625" class="label-container" transform="translate(-89.765625,89.765625)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-62.765625, -12)"><rect></rect><foreignObject width="125.53125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Permission Matrix</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-CREATE_CHECK-51" transform="translate(560.6562514305115, 1270.4583435058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-118.234375" y="-27" width="236.46875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-88.234375, -12)"><rect></rect><foreignObject width="176.46875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Check Create Permission</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-READ_CHECK-53" transform="translate(840.4843764305115, 1270.4583435058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-111.59375" y="-27" width="223.1875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-81.59375, -12)"><rect></rect><foreignObject width="163.1875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Check Read Permission</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-UPDATE_CHECK-55" transform="translate(1122.1562514305115, 1270.4583435058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-120.078125" y="-27" width="240.15625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-90.078125, -12)"><rect></rect><foreignObject width="180.15625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Check Update Permission</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-DELETE_CHECK-57" transform="translate(1410.0000014305115, 1270.4583435058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-117.765625" y="-27" width="235.53125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-87.765625, -12)"><rect></rect><foreignObject width="175.53125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Check Delete Permission</p></span></div></foreignObject></g></g></g></g></g></svg>