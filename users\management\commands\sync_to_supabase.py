"""
Management command to sync local SQLite data to Supabase PostgreSQL database.
This command handles the transfer of users, schools, and RBAC data to Supabase.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from schools.models import School
from users.models import Role, UserRole, RoleTemplate
import requests
import json
import os
from datetime import datetime

User = get_user_model()


class Command(BaseCommand):
    help = 'Sync local SQLite data to Supabase PostgreSQL database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be synced without actually syncing'
        )
        
        parser.add_argument(
            '--clear-supabase',
            action='store_true',
            help='Clear existing data in Supabase before syncing'
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        clear_supabase = options['clear_supabase']

        self.stdout.write(self.style.SUCCESS('Starting Supabase sync process...'))

        # Get Supabase configuration
        supabase_url = os.getenv('SUPABASE_URL')
        supabase_key = os.getenv('SUPABASE_ANON_KEY')
        project_id = os.getenv('SUPABASE_PROJECT_ID', 'fsznynpmqxljaijcuweb')

        if not all([supabase_url, supabase_key]):
            self.stdout.write(
                self.style.ERROR('Missing Supabase configuration. Please set SUPABASE_URL and SUPABASE_ANON_KEY')
            )
            return

        # Test Supabase connectivity
        if not self.test_supabase_connection(project_id):
            self.stdout.write(
                self.style.ERROR('Cannot connect to Supabase. Please check network connectivity.')
            )
            return

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No data will be actually synced'))

        # Clear Supabase data if requested
        if clear_supabase and not dry_run:
            self.clear_supabase_data(project_id)

        # Sync data
        self.sync_schools(project_id, dry_run)
        self.sync_users(project_id, dry_run)
        self.sync_roles(project_id, dry_run)
        self.sync_user_roles(project_id, dry_run)

        self.stdout.write(self.style.SUCCESS('Supabase sync completed!'))

    def test_supabase_connection(self, project_id):
        """Test if we can connect to Supabase."""
        try:
            self.stdout.write('Testing Supabase connection...')
            
            # Use the Supabase Management API to test connectivity
            response = self.make_supabase_query(project_id, "SELECT 1 as test;")
            
            if response and len(response) > 0:
                self.stdout.write(self.style.SUCCESS('✅ Supabase connection successful'))
                return True
            else:
                self.stdout.write(self.style.ERROR('❌ Supabase connection failed'))
                return False
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Supabase connection error: {str(e)}'))
            return False

    def make_supabase_query(self, project_id, query, data=None):
        """Make a query to Supabase using the Management API."""
        try:
            import requests
            
            # This would need to be implemented with proper Supabase Management API
            # For now, we'll use a placeholder that shows the structure
            
            url = f"https://api.supabase.com/v1/projects/{project_id}/database/query"
            headers = {
                'Authorization': f'Bearer {os.getenv("SUPABASE_ACCESS_TOKEN")}',
                'Content-Type': 'application/json'
            }
            
            payload = {'query': query}
            if data:
                payload['data'] = data
            
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                return response.json()
            else:
                self.stdout.write(f'Query failed: {response.status_code} - {response.text}')
                return None
                
        except Exception as e:
            self.stdout.write(f'Query error: {str(e)}')
            return None

    def clear_supabase_data(self, project_id):
        """Clear existing data in Supabase."""
        self.stdout.write('Clearing existing Supabase data...')
        
        # Clear in reverse order due to foreign key constraints
        clear_queries = [
            "DELETE FROM user_roles;",
            "DELETE FROM users WHERE email NOT LIKE '%@supabase.%';",  # Keep Supabase system users
            "DELETE FROM roles WHERE name NOT IN ('authenticated', 'anon');",  # Keep Supabase system roles
            "DELETE FROM schools WHERE name NOT LIKE '%Supabase%';"  # Keep any Supabase system schools
        ]
        
        for query in clear_queries:
            self.make_supabase_query(project_id, query)
            
        self.stdout.write('Supabase data cleared.')

    def sync_schools(self, project_id, dry_run):
        """Sync schools to Supabase."""
        schools = School.objects.all()
        self.stdout.write(f'Syncing {schools.count()} schools...')
        
        for school in schools:
            if dry_run:
                self.stdout.write(f'  Would sync school: {school.name}')
                continue
                
            # Convert school to SQL INSERT
            query = f"""
            INSERT INTO schools (
                id, name, slug, address_line_1, city, state_province, 
                postal_code, country, phone, email, website, 
                subscription_plan, status, academic_year_start, 
                academic_year_end, created_at, updated_at
            ) VALUES (
                '{school.id}', '{school.name}', '{school.slug}',
                '{school.address_line_1 or ""}', '{school.city or ""}', 
                '{school.state_province or ""}', '{school.postal_code or ""}',
                '{school.country or ""}', '{school.phone or ""}', 
                '{school.email or ""}', '{school.website or ""}',
                '{school.subscription_plan}', '{school.status}',
                '{school.academic_year_start}', '{school.academic_year_end}',
                '{school.created_at}', '{school.updated_at}'
            ) ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                updated_at = EXCLUDED.updated_at;
            """
            
            result = self.make_supabase_query(project_id, query)
            if result is not None:
                self.stdout.write(f'  ✅ Synced school: {school.name}')
            else:
                self.stdout.write(f'  ❌ Failed to sync school: {school.name}')

    def sync_users(self, project_id, dry_run):
        """Sync users to Supabase."""
        users = User.objects.all()
        self.stdout.write(f'Syncing {users.count()} users...')
        
        for user in users:
            if dry_run:
                self.stdout.write(f'  Would sync user: {user.email}')
                continue
                
            # Convert user to SQL INSERT
            school_id = f"'{user.school.id}'" if user.school else 'NULL'
            
            query = f"""
            INSERT INTO users (
                id, email, password, first_name, last_name, middle_name,
                user_type, school_id, is_active, is_staff, is_superuser,
                date_joined, last_login, phone, date_of_birth,
                student_id, enrollment_date, graduation_date,
                employee_id, hire_date, department, avatar, bio,
                email_verified, created_at, updated_at
            ) VALUES (
                '{user.id}', '{user.email}', '{user.password}',
                '{user.first_name or ""}', '{user.last_name or ""}', 
                '{user.middle_name or ""}', '{user.user_type}',
                {school_id}, {user.is_active}, {user.is_staff}, 
                {user.is_superuser}, '{user.date_joined}', 
                {f"'{user.last_login}'" if user.last_login else 'NULL'},
                '{user.phone or ""}', 
                {f"'{user.date_of_birth}'" if user.date_of_birth else 'NULL'},
                '{user.student_id or ""}', 
                {f"'{user.enrollment_date}'" if user.enrollment_date else 'NULL'},
                {f"'{user.graduation_date}'" if user.graduation_date else 'NULL'},
                '{user.employee_id or ""}',
                {f"'{user.hire_date}'" if user.hire_date else 'NULL'},
                '{user.department or ""}', '{user.avatar or ""}',
                '{user.bio or ""}', {user.email_verified},
                '{user.created_at}', '{user.updated_at}'
            ) ON CONFLICT (id) DO UPDATE SET
                email = EXCLUDED.email,
                updated_at = EXCLUDED.updated_at;
            """
            
            result = self.make_supabase_query(project_id, query)
            if result is not None:
                self.stdout.write(f'  ✅ Synced user: {user.email}')
            else:
                self.stdout.write(f'  ❌ Failed to sync user: {user.email}')

    def sync_roles(self, project_id, dry_run):
        """Sync roles to Supabase."""
        roles = Role.objects.all()
        self.stdout.write(f'Syncing {roles.count()} roles...')
        
        for role in roles:
            if dry_run:
                self.stdout.write(f'  Would sync role: {role.name}')
                continue
                
            # Convert role to SQL INSERT
            school_id = f"'{role.school.id}'" if role.school else 'NULL'
            
            query = f"""
            INSERT INTO roles (
                id, name, description, school_id, permissions,
                is_system_role, is_active, created_at, updated_at
            ) VALUES (
                '{role.id}', '{role.name}', '{role.description or ""}',
                {school_id}, '{json.dumps(role.permissions)}',
                {role.is_system_role}, {role.is_active},
                '{role.created_at}', '{role.updated_at}'
            ) ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                updated_at = EXCLUDED.updated_at;
            """
            
            result = self.make_supabase_query(project_id, query)
            if result is not None:
                self.stdout.write(f'  ✅ Synced role: {role.name}')
            else:
                self.stdout.write(f'  ❌ Failed to sync role: {role.name}')

    def sync_user_roles(self, project_id, dry_run):
        """Sync user roles to Supabase."""
        user_roles = UserRole.objects.all()
        self.stdout.write(f'Syncing {user_roles.count()} user roles...')
        
        for user_role in user_roles:
            if dry_run:
                self.stdout.write(f'  Would sync user role: {user_role.user.email} -> {user_role.role.name}')
                continue
                
            # Convert user role to SQL INSERT
            query = f"""
            INSERT INTO user_roles (
                id, user_id, role_id, assigned_by_id, assigned_at,
                is_active, created_at, updated_at
            ) VALUES (
                '{user_role.id}', '{user_role.user.id}', '{user_role.role.id}',
                {f"'{user_role.assigned_by.id}'" if user_role.assigned_by else 'NULL'},
                '{user_role.assigned_at}', {user_role.is_active},
                '{user_role.created_at}', '{user_role.updated_at}'
            ) ON CONFLICT (id) DO UPDATE SET
                is_active = EXCLUDED.is_active,
                updated_at = EXCLUDED.updated_at;
            """
            
            result = self.make_supabase_query(project_id, query)
            if result is not None:
                self.stdout.write(f'  ✅ Synced user role: {user_role.user.email} -> {user_role.role.name}')
            else:
                self.stdout.write(f'  ❌ Failed to sync user role: {user_role.user.email} -> {user_role.role.name}')

    def display_sync_summary(self):
        """Display summary of what was synced."""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('SUPABASE SYNC SUMMARY'))
        self.stdout.write('='*60)
        
        self.stdout.write(f'📊 Schools synced: {School.objects.count()}')
        self.stdout.write(f'👥 Users synced: {User.objects.count()}')
        self.stdout.write(f'🔐 Roles synced: {Role.objects.count()}')
        self.stdout.write(f'🔗 User roles synced: {UserRole.objects.count()}')
        
        self.stdout.write('\n🔍 Verification Steps:')
        self.stdout.write('1. Check Supabase dashboard for synced data')
        self.stdout.write('2. Test login with synced credentials')
        self.stdout.write('3. Verify multi-tenant data isolation')
        self.stdout.write('4. Test RBAC permissions')
        
        self.stdout.write('\n🌐 Supabase Dashboard:')
        self.stdout.write('https://supabase.com/dashboard/project/fsznynpmqxljaijcuweb')
        
        self.stdout.write('='*60)
