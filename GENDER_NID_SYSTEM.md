# Gender and NID System Documentation

## Overview

The SMS project now includes comprehensive **Gender and National Identification Number (NID)** fields for all users. This system ensures proper user identification, demographic tracking, and compliance with educational data requirements.

## Features

### ✅ **Gender Field**
- **Multiple choice options**: Male, Female, Other, Prefer not to say
- **Optional field**: Users can leave blank if preferred
- **Inclusive design**: Supports diverse gender identities
- **Database indexed**: Optimized for reporting and analytics

### ✅ **National Identification Number (NID)**
- **Global uniqueness**: Each NID must be unique across all users
- **Optional field**: Not required for account creation
- **Flexible format**: Supports various national ID formats
- **Database constraints**: Prevents duplicate NIDs

### ✅ **Integration with Registration System**
- **Works with student registration numbers**: Both systems coexist
- **Multi-tenant support**: NIDs are unique globally, not per school
- **Automatic validation**: Database-level constraints ensure data integrity

## Gender System

### Available Options
```python
GENDER_CHOICES = [
    ('male', 'Male'),
    ('female', 'Female'),
    ('other', 'Other'),
    ('prefer_not_to_say', 'Prefer not to say'),
]
```

### Current Gender Distribution
- **Female**: 5 users (50%)
- **Male**: 4 users (40%)
- **Prefer not to say**: 1 user (10%)
- **Other**: 0 users (0%)

### Usage Example
```python
from users.models import User

# Create user with gender
user = User.objects.create(
    email='<EMAIL>',
    user_type='student',
    gender='female',  # Optional
    first_name='Jane',
    last_name='Doe'
)

# Get gender display
print(user.get_gender_display())  # Output: "Female"
```

## NID System

### Format and Validation
- **Length**: Flexible (currently 13 digits in sample data)
- **Format**: Alphanumeric (currently all numeric)
- **Uniqueness**: Global across all users and schools
- **Constraint**: Database-level unique constraint

### Current NID Data
All users (100%) have NID data with consistent 13-digit format:

#### 🏫 Greenwood High School
- **Emily Davis**: 4567890123456 (Student - GW2024001)
- **Sarah Johnson**: 3456789012345 (Lecturer - GW001)
- **John Smith**: 2345678901234 (Admin)
- **Sarah Thompson**: 6789012345678 (Student - GW2025002)
- **John Wilson**: 5678901234567 (Student - GW2025001)

#### 🏫 Riverside Academy
- **Michael Brown**: 7890123456789 (Admin)
- **Maria Garcia**: 0123456789012 (Student - RA2025001)
- **Alex Martinez**: 9012345678901 (Student - RA2024001)
- **Lisa Wilson**: 8901234567890 (Lecturer - RA001)

#### 🏫 System Admin
- **Super Admin**: 1234567890123 (System Administrator)

### Usage Example
```python
from users.models import User

# Create user with NID
user = User.objects.create(
    email='<EMAIL>',
    user_type='student',
    nid='1234567890123',  # Must be unique globally
    first_name='John',
    last_name='Doe'
)

# NID uniqueness is enforced
try:
    duplicate_user = User.objects.create(
        email='<EMAIL>',
        nid='1234567890123'  # Same NID - will fail
    )
except IntegrityError:
    print("NID must be unique!")
```

## Database Schema

### User Model Fields
```python
class User(AbstractBaseUser):
    # ... other fields ...
    
    gender = models.CharField(
        max_length=20,
        choices=GENDER_CHOICES,
        blank=True,
        help_text="Gender identity"
    )
    
    nid = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="National Identification Number (NID)"
    )
```

### Database Constraints
```sql
-- Unique constraint for NID (when provided)
CREATE UNIQUE INDEX unique_nid_global 
ON users(nid) 
WHERE nid IS NOT NULL AND nid != '';

-- Indexes for performance
CREATE INDEX users_gender_idx ON users(gender);
CREATE INDEX users_nid_idx ON users(nid);
```

## API Integration

### User API Response
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "gender": "male",
  "nid": "1234567890123",
  "student_id": "GW2025001",
  "school": "greenwood-high",
  "user_type": "student"
}
```

### Gender Display Values
```json
{
  "gender": "male",
  "gender_display": "Male"
}
```

## Validation and Constraints

### ✅ **Gender Validation**
- Must be one of the predefined choices
- Can be blank/null
- Case-sensitive values

### ✅ **NID Validation**
- Must be unique across all users globally
- Can be blank/null for optional use
- Maximum 50 characters
- Database constraint prevents duplicates

### ✅ **Integration Validation**
- Works with existing registration number system
- Compatible with multi-tenant architecture
- Maintains data integrity across schools

## Testing

### Run Comprehensive Tests
```bash
# Test gender and NID system
python test_gender_nid_system.py

# Test registration number system
python test_registration_system.py

# Test complete system
python test_final_connection.py
```

### Test Results Summary
- **✅ Gender Coverage**: 100% of users have gender data
- **✅ NID Coverage**: 100% of users have NID data
- **✅ Uniqueness**: All NIDs are unique globally
- **✅ Validation**: Database constraints working correctly
- **✅ Integration**: Works with registration number system

## Supabase Sync

Gender and NID data is automatically synced to Supabase:

```bash
# Sync all user data including gender and NID
python manage.py sync_supabase --direction=push
```

### Supabase Schema
```sql
-- Supabase users table includes:
ALTER TABLE users ADD COLUMN gender VARCHAR(20);
ALTER TABLE users ADD COLUMN nid VARCHAR(50);
CREATE UNIQUE INDEX unique_nid_global ON users(nid) 
WHERE nid IS NOT NULL AND nid != '';
```

## Use Cases

### 📊 **Demographics and Reporting**
- Generate gender distribution reports
- Track diversity metrics
- Compliance reporting

### 🆔 **Identity Verification**
- Verify student identity using NID
- Prevent duplicate registrations
- Government compliance requirements

### 📋 **Student Records**
- Complete student profiles
- Official document generation
- Academic transcript requirements

### 🏫 **Multi-School Management**
- Global NID uniqueness prevents conflicts
- Cross-school student transfers
- Centralized identity management

## Best Practices

### 🔒 **Privacy and Security**
- Gender information is optional and respectful
- NID data should be handled securely
- Comply with local privacy regulations

### 📝 **Data Entry**
- Validate NID format according to local standards
- Provide clear gender options
- Allow users to update their information

### 🔄 **Data Management**
- Regular validation of NID uniqueness
- Monitor for data quality issues
- Backup sensitive identification data

## Future Enhancements

1. **NID Format Validation**: Add country-specific NID format validation
2. **Gender History**: Track gender identity changes over time
3. **Document Upload**: Allow NID document uploads for verification
4. **Bulk Import**: NID validation during bulk user imports
5. **Reporting Dashboard**: Gender and demographic analytics
6. **Privacy Controls**: User-controlled visibility of gender information

## Benefits

- **🔒 Unique Identification**: Global NID uniqueness prevents duplicates
- **📊 Demographics**: Comprehensive gender tracking for reporting
- **🏫 Multi-tenant Safe**: Works across multiple schools
- **🛡️ Validated**: Database constraints ensure data integrity
- **🔄 Synced**: Automatic synchronization with Supabase
- **📈 Scalable**: Supports unlimited users with unique identification
- **🌍 Global**: NID system works across different countries

The Gender and NID System is now fully operational and ready for production use!
