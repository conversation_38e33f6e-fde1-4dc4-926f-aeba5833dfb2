"""
Management command to set up proper admin permissions for school administrators.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from users.models import Permission, Role, UserRole, RolePermission
from users.rbac_utils import RBACManager
from schools.models import School

User = get_user_model()


class Command(BaseCommand):
    help = 'Set up proper admin permissions for school administrators'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-permissions',
            action='store_true',
            help='Create missing permissions'
        )

    def handle(self, *args, **options):
        create_permissions = options['create_permissions']

        self.stdout.write(self.style.SUCCESS('Setting up admin permissions...'))

        with transaction.atomic():
            if create_permissions:
                self.create_admin_permissions()
            
            self.create_school_admin_roles()
            self.assign_roles_to_existing_admins()

        self.stdout.write(self.style.SUCCESS('Admin permissions setup completed!'))

    def create_admin_permissions(self):
        """Create necessary permissions for admin functionality."""
        self.stdout.write('Creating admin permissions...')
        
        permissions_data = [
            # User management permissions
            {
                'name': 'Can manage users',
                'codename': 'can_manage_users',
                'description': 'Can create, edit, and delete users within school',
                'category': 'user_management'
            },
            {
                'name': 'Can view users',
                'codename': 'can_view_users',
                'description': 'Can view user information within school',
                'category': 'user_management'
            },
            {
                'name': 'Can assign roles',
                'codename': 'can_assign_roles',
                'description': 'Can assign and remove roles from users',
                'category': 'role_management'
            },
            
            # School management permissions
            {
                'name': 'Can view school settings',
                'codename': 'can_view_school_settings',
                'description': 'Can view school configuration and settings',
                'category': 'school_management'
            },
            {
                'name': 'Can manage school settings',
                'codename': 'can_manage_school_settings',
                'description': 'Can edit school configuration and settings',
                'category': 'school_management'
            },
            
            # Course management permissions
            {
                'name': 'Can manage courses',
                'codename': 'can_manage_courses',
                'description': 'Can create, edit, and delete courses',
                'category': 'course_management'
            },
            {
                'name': 'Can view courses',
                'codename': 'can_view_courses',
                'description': 'Can view course information',
                'category': 'course_management'
            },
            
            # Reporting permissions
            {
                'name': 'Can view reports',
                'codename': 'can_view_reports',
                'description': 'Can access school reports and analytics',
                'category': 'reporting'
            },
            {
                'name': 'Can generate reports',
                'codename': 'can_generate_reports',
                'description': 'Can generate custom reports',
                'category': 'reporting'
            },
            
            # System-level permissions
            {
                'name': 'Can manage schools',
                'codename': 'can_manage_schools',
                'description': 'Can create, edit, and delete schools (system-level)',
                'category': 'system_management',
                'is_system_permission': True
            }
        ]

        for perm_data in permissions_data:
            permission, created = Permission.objects.get_or_create(
                codename=perm_data['codename'],
                defaults=perm_data
            )
            
            if created:
                self.stdout.write(f'  ✅ Created permission: {permission.name}')
            else:
                self.stdout.write(f'  ℹ️  Permission exists: {permission.name}')

    def create_school_admin_roles(self):
        """Create school administrator roles for each school."""
        self.stdout.write('Creating school administrator roles...')
        
        # Define permissions for school administrators
        school_admin_permissions = [
            'can_manage_users',
            'can_view_users',
            'can_assign_roles',
            'can_view_school_settings',
            'can_manage_school_settings',
            'can_manage_courses',
            'can_view_courses',
            'can_view_reports',
            'can_generate_reports'
        ]
        
        schools = School.objects.all()
        
        for school in schools:
            # Create or get school administrator role
            role_name = f'{school.name} Administrator'
            role, created = Role.objects.get_or_create(
                name=role_name,
                school=school,
                defaults={
                    'description': f'Administrator role for {school.name}',
                    'role_type': 'school',
                    'is_active': True
                }
            )
            
            if created:
                self.stdout.write(f'  ✅ Created role: {role_name}')
            else:
                self.stdout.write(f'  ℹ️  Role exists: {role_name}')
            
            # Assign permissions to the role
            for perm_codename in school_admin_permissions:
                try:
                    permission = Permission.objects.get(codename=perm_codename)
                    role_permission, created = RolePermission.objects.get_or_create(
                        role=role,
                        permission=permission,
                        defaults={
                            'granted': True,
                            'granted_by': None  # System assignment
                        }
                    )
                    if created:
                        self.stdout.write(f'    ✅ Assigned permission: {permission.name}')
                except Permission.DoesNotExist:
                    self.stdout.write(f'  ⚠️  Permission not found: {perm_codename}')

    def assign_roles_to_existing_admins(self):
        """Assign administrator roles to existing admin users."""
        self.stdout.write('Assigning roles to existing administrators...')
        
        # Find users who should be school administrators
        admin_users = User.objects.filter(
            user_type='admin',
            school__isnull=False,
            is_active=True
        )
        
        for user in admin_users:
            # Find the administrator role for this user's school
            role_name = f'{user.school.name} Administrator'
            try:
                role = Role.objects.get(name=role_name, school=user.school)
                
                # Check if user already has this role
                existing_assignment = UserRole.objects.filter(
                    user=user,
                    role=role,
                    is_active=True
                ).first()
                
                if not existing_assignment:
                    # Assign the role
                    RBACManager.assign_role_to_user(
                        user=user,
                        role=role,
                        assigned_by=None,  # System assignment
                        requires_approval=False
                    )
                    self.stdout.write(f'  ✅ Assigned {role_name} to {user.email}')
                else:
                    self.stdout.write(f'  ℹ️  {user.email} already has {role_name}')
                    
            except Role.DoesNotExist:
                self.stdout.write(f'  ❌ Role not found: {role_name}')

        # Create system administrator role and assign to system users
        self.create_system_admin_role()

    def create_system_admin_role(self):
        """Create system administrator role for system-level users."""
        self.stdout.write('Creating system administrator role...')
        
        # System admin permissions
        system_admin_permissions = [
            'can_manage_schools',
            'can_manage_users',
            'can_view_users',
            'can_assign_roles',
            'can_view_school_settings',
            'can_manage_school_settings',
            'can_manage_courses',
            'can_view_courses',
            'can_view_reports',
            'can_generate_reports'
        ]
        
        # Create system administrator role
        role, created = Role.objects.get_or_create(
            name='System Administrator',
            school=None,  # System-wide role
            defaults={
                'description': 'System-wide administrator with full access',
                'role_type': 'system',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write('  ✅ Created System Administrator role')
        else:
            self.stdout.write('  ℹ️  System Administrator role exists')
        
        # Assign permissions to the role
        for perm_codename in system_admin_permissions:
            try:
                permission = Permission.objects.get(codename=perm_codename)
                role_permission, created = RolePermission.objects.get_or_create(
                    role=role,
                    permission=permission,
                    defaults={
                        'granted': True,
                        'granted_by': None  # System assignment
                    }
                )
                if created:
                    self.stdout.write(f'    ✅ Assigned permission: {permission.name}')
            except Permission.DoesNotExist:
                self.stdout.write(f'  ⚠️  Permission not found: {perm_codename}')
        
        # Assign to system users
        system_users = User.objects.filter(
            user_type='system',
            is_active=True
        )
        
        for user in system_users:
            # Check if user already has this role
            existing_assignment = UserRole.objects.filter(
                user=user,
                role=role,
                is_active=True
            ).first()
            
            if not existing_assignment:
                RBACManager.assign_role_to_user(
                    user=user,
                    role=role,
                    assigned_by=None,  # System assignment
                    requires_approval=False
                )
                self.stdout.write(f'  ✅ Assigned System Administrator to {user.email}')
            else:
                self.stdout.write(f'  ℹ️  {user.email} already has System Administrator role')

        self.stdout.write('\n' + '='*60)
        self.stdout.write('📊 PERMISSION SETUP SUMMARY')
        self.stdout.write('='*60)
        
        total_permissions = Permission.objects.count()
        total_roles = Role.objects.count()
        total_assignments = UserRole.objects.filter(is_active=True).count()
        
        self.stdout.write(f'📋 Total Permissions: {total_permissions}')
        self.stdout.write(f'🔐 Total Roles: {total_roles}')
        self.stdout.write(f'👥 Active Role Assignments: {total_assignments}')
        
        self.stdout.write('\n🔗 Next Steps:')
        self.stdout.write('1. Test login with school administrator accounts')
        self.stdout.write('2. Verify admin dashboard access and permissions')
        self.stdout.write('3. Check multi-tenant data isolation')
        
        self.stdout.write('='*60)
