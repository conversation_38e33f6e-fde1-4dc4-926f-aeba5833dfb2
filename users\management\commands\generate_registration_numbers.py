"""
Django management command to generate registration numbers for students.
"""
from django.core.management.base import BaseCommand
from django.db import transaction
from users.models import User
from schools.models import School


class Command(BaseCommand):
    help = 'Generate registration numbers for students who don\'t have them'

    def add_arguments(self, parser):
        parser.add_argument(
            '--school',
            type=str,
            help='Generate registration numbers for a specific school (slug)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Regenerate registration numbers even for students who already have them'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be generated without making changes'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== Student Registration Number Generator ===\n'))
        
        school_slug = options.get('school')
        force = options.get('force', False)
        dry_run = options.get('dry_run', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made\n'))
        
        # Filter students
        students = User.objects.filter(user_type='student')
        
        if school_slug:
            try:
                school = School.objects.get(slug=school_slug)
                students = students.filter(school=school)
                self.stdout.write(f'Filtering students for school: {school.name}\n')
            except School.DoesNotExist:
                self.stdout.write(self.style.ERROR(f'School with slug "{school_slug}" not found'))
                return
        
        # Filter students who need registration numbers
        if not force:
            students = students.filter(student_id__isnull=True) | students.filter(student_id='')
            self.stdout.write('Processing students without registration numbers...\n')
        else:
            self.stdout.write('Processing ALL students (force mode)...\n')
        
        if not students.exists():
            self.stdout.write(self.style.WARNING('No students found that need registration numbers'))
            return
        
        self.stdout.write(f'Found {students.count()} students to process\n')
        
        # Group students by school for better organization
        schools_processed = {}
        
        with transaction.atomic():
            for student in students.select_related('school'):
                if not student.school:
                    self.stdout.write(
                        self.style.WARNING(f'Skipping {student.email} - no school assigned')
                    )
                    continue
                
                school_name = student.school.name
                if school_name not in schools_processed:
                    schools_processed[school_name] = []
                
                try:
                    if force or not student.student_id:
                        old_reg_num = student.student_id
                        new_reg_num = student.generate_student_registration_number()
                        
                        if not dry_run:
                            student.student_id = new_reg_num
                            student.save()
                        
                        action = 'Updated' if old_reg_num else 'Generated'
                        if old_reg_num:
                            self.stdout.write(
                                f'  {action}: {student.get_full_name()} ({student.email})'
                                f' - {old_reg_num} → {new_reg_num}'
                            )
                        else:
                            self.stdout.write(
                                f'  {action}: {student.get_full_name()} ({student.email})'
                                f' - {new_reg_num}'
                            )
                        
                        schools_processed[school_name].append({
                            'student': student,
                            'old_reg': old_reg_num,
                            'new_reg': new_reg_num,
                            'action': action
                        })
                    else:
                        self.stdout.write(
                            f'  Skipped: {student.get_full_name()} - already has {student.student_id}'
                        )
                
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error processing {student.email}: {str(e)}')
                    )
        
        # Summary by school
        self.stdout.write('\n=== SUMMARY BY SCHOOL ===')
        total_processed = 0
        
        for school_name, processed_students in schools_processed.items():
            if processed_students:
                self.stdout.write(f'\n🏫 {school_name}:')
                generated_count = len([s for s in processed_students if not s['old_reg']])
                updated_count = len([s for s in processed_students if s['old_reg']])
                
                if generated_count > 0:
                    self.stdout.write(f'  ✅ Generated {generated_count} new registration numbers')
                if updated_count > 0:
                    self.stdout.write(f'  🔄 Updated {updated_count} existing registration numbers')
                
                # Show the registration numbers
                for item in processed_students:
                    reg_display = item['new_reg']
                    if item['old_reg']:
                        reg_display = f"{item['old_reg']} → {item['new_reg']}"
                    self.stdout.write(f'    - {item["student"].get_full_name()}: {reg_display}')
                
                total_processed += len(processed_students)
        
        if dry_run:
            self.stdout.write(f'\n🔍 DRY RUN COMPLETE - {total_processed} students would be processed')
        else:
            self.stdout.write(f'\n✅ COMPLETED - {total_processed} students processed successfully')
        
        # Show next available registration numbers for each school
        self.stdout.write('\n=== NEXT AVAILABLE REGISTRATION NUMBERS ===')
        for school in School.objects.all():
            try:
                # Create a temporary student to get the next number
                temp_student = User(school=school, user_type='student')
                next_reg_num = temp_student.generate_student_registration_number()
                self.stdout.write(f'🏫 {school.name}: Next available - {next_reg_num}')
            except Exception as e:
                self.stdout.write(f'🏫 {school.name}: Error getting next number - {str(e)}')
