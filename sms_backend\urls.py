"""
URL configuration for sms_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
    TokenVerifyView,
)
from users.admin import rbac_admin_site

urlpatterns = [
    # Standard Django admin
    path("admin/", admin.site.urls),

    # Enhanced RBAC admin dashboard
    path("rbac-admin/", rbac_admin_site.urls),

    # API v1 endpoints
    path("api/v1/auth/", include("authentication.urls")),
    path("api/v1/schools/", include("schools.urls")),
    path("api/v1/users/", include("users.urls")),
    path("api/v1/courses/", include("courses.urls")),

    # JWT Authentication endpoints
    path("api/v1/token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("api/v1/token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    path("api/v1/token/verify/", TokenVerifyView.as_view(), name="token_verify"),
]
