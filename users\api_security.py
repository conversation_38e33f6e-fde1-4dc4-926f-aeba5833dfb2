"""
API Security configuration and utilities for RBAC system.
"""

import jwt
import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth import get_user_model
from django.core.cache import cache
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.exceptions import TokenError
from .models import Role<PERSON>uditLog
from .rbac_utils import RBACManager

User = get_user_model()
logger = logging.getLogger(__name__)


class JWTRBACTokenGenerator:
    """
    Enhanced JWT token generator with RBAC claims.
    """
    
    @staticmethod
    def generate_token_for_user(user):
        """
        Generate JWT token with RBAC claims for a user.
        
        Args:
            user: User instance
            
        Returns:
            dict: Token data with access and refresh tokens
        """
        try:
            # Generate base tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            # Add RBAC claims to access token
            rbac_claims = JWTRBACTokenGenerator.get_rbac_claims(user)
            for key, value in rbac_claims.items():
                access_token[key] = value
            
            # Add custom claims
            access_token['user_type'] = user.user_type
            access_token['school_id'] = str(user.school.id) if user.school else None
            access_token['is_superuser'] = user.is_superuser
            access_token['last_login'] = user.last_login.isoformat() if user.last_login else None
            
            return {
                'access': str(access_token),
                'refresh': str(refresh),
                'expires_in': settings.SIMPLE_JWT['ACCESS_TOKEN_LIFETIME'].total_seconds(),
                'token_type': 'Bearer',
                'user_id': str(user.id),
                'user_email': user.email,
                'rbac_claims': rbac_claims
            }
            
        except Exception as e:
            logger.error(f"Failed to generate token for user {user.id}: {e}")
            raise
    
    @staticmethod
    def get_rbac_claims(user):
        """
        Get RBAC claims for a user.
        
        Args:
            user: User instance
            
        Returns:
            dict: RBAC claims
        """
        try:
            # Get user permissions
            permissions = RBACManager.get_user_permissions(user, user.school)
            
            # Get user roles
            roles = list(
                user.user_roles.filter(is_active=True)
                .select_related('role')
                .values('role__id', 'role__name', 'role__role_type', 'role__level')
            )
            
            # Get role hierarchy
            role_hierarchy = []
            for user_role in user.user_roles.filter(is_active=True).select_related('role'):
                role = user_role.role
                hierarchy_path = []
                current_role = role
                while current_role:
                    hierarchy_path.append({
                        'id': str(current_role.id),
                        'name': current_role.name,
                        'level': current_role.level
                    })
                    current_role = current_role.parent_role
                role_hierarchy.append(hierarchy_path)
            
            return {
                'permissions': list(permissions)[:50],  # Limit to prevent token bloat
                'roles': roles[:10],  # Limit roles
                'role_hierarchy': role_hierarchy[:5],  # Limit hierarchy
                'permission_count': len(permissions),
                'role_count': len(roles),
                'highest_role_level': max([role['role__level'] for role in roles], default=0),
                'school_context': str(user.school.id) if user.school else None,
            }
            
        except Exception as e:
            logger.error(f"Failed to get RBAC claims for user {user.id}: {e}")
            return {
                'permissions': [],
                'roles': [],
                'role_hierarchy': [],
                'permission_count': 0,
                'role_count': 0,
                'highest_role_level': 0,
                'school_context': None,
            }
    
    @staticmethod
    def refresh_rbac_claims(access_token):
        """
        Refresh RBAC claims in an existing access token.
        
        Args:
            access_token: JWT access token
            
        Returns:
            str: Updated token with fresh RBAC claims
        """
        try:
            # Decode token to get user
            decoded = jwt.decode(
                access_token, 
                settings.SECRET_KEY, 
                algorithms=['HS256'],
                options={'verify_exp': False}  # Don't verify expiration for refresh
            )
            
            user_id = decoded.get('user_id')
            if not user_id:
                raise ValueError("Invalid token: missing user_id")
            
            user = User.objects.get(id=user_id)
            
            # Get fresh RBAC claims
            fresh_claims = JWTRBACTokenGenerator.get_rbac_claims(user)
            
            # Update token with fresh claims
            decoded.update(fresh_claims)
            
            # Re-encode token
            return jwt.encode(decoded, settings.SECRET_KEY, algorithm='HS256')
            
        except Exception as e:
            logger.error(f"Failed to refresh RBAC claims: {e}")
            raise


class APISecurityManager:
    """
    Manager for API security operations.
    """
    
    @staticmethod
    def validate_api_access(request, required_permission=None, school_context=True):
        """
        Comprehensive API access validation.
        
        Args:
            request: Django request object
            required_permission: Required permission codename
            school_context: Whether to enforce school context
            
        Returns:
            dict: Validation result with success status and details
        """
        result = {
            'success': False,
            'user': None,
            'permissions': [],
            'roles': [],
            'school': None,
            'errors': []
        }
        
        try:
            # Check authentication
            if not request.user or not request.user.is_authenticated:
                result['errors'].append('Authentication required')
                return result
            
            result['user'] = request.user
            
            # Check school context
            if school_context and not request.user.is_superuser:
                user_school = request.user.school
                if not user_school:
                    result['errors'].append('School context required')
                    return result
                result['school'] = user_school
            
            # Get user permissions and roles
            permissions = RBACManager.get_user_permissions(request.user, result['school'])
            roles = list(
                request.user.user_roles.filter(is_active=True)
                .select_related('role')
                .values_list('role__name', flat=True)
            )
            
            result['permissions'] = list(permissions)
            result['roles'] = roles
            
            # Check required permission
            if required_permission:
                if required_permission not in permissions:
                    result['errors'].append(f'Permission denied: {required_permission}')
                    return result
            
            result['success'] = True
            return result
            
        except Exception as e:
            logger.error(f"API access validation failed: {e}")
            result['errors'].append('Internal validation error')
            return result
    
    @staticmethod
    def log_security_event(event_type, request, details=None):
        """
        Log security-related events.
        
        Args:
            event_type: Type of security event
            request: Django request object
            details: Additional event details
        """
        try:
            # Get client information
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0]
            else:
                ip_address = request.META.get('REMOTE_ADDR')
            
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # Create audit log
            RoleAuditLog.objects.create(
                action_type=f'security_{event_type}',
                description=f'Security event: {event_type}',
                performed_by=request.user if hasattr(request, 'user') and request.user.is_authenticated else None,
                school=getattr(request.user, 'school', None) if hasattr(request, 'user') else None,
                ip_address=ip_address,
                user_agent=user_agent,
                metadata={
                    'event_type': event_type,
                    'path': request.path,
                    'method': request.method,
                    'details': details or {},
                    'timestamp': datetime.now().isoformat(),
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to log security event: {e}")
    
    @staticmethod
    def check_rate_limit(user_id, endpoint, limit=100, window=3600):
        """
        Check rate limit for a user and endpoint.
        
        Args:
            user_id: User ID
            endpoint: API endpoint
            limit: Request limit
            window: Time window in seconds
            
        Returns:
            dict: Rate limit status
        """
        cache_key = f"rate_limit:{user_id}:{endpoint}"
        
        try:
            # Get current count
            current_count = cache.get(cache_key, 0)
            
            if current_count >= limit:
                return {
                    'allowed': False,
                    'current_count': current_count,
                    'limit': limit,
                    'reset_time': window
                }
            
            # Increment count
            cache.set(cache_key, current_count + 1, window)
            
            return {
                'allowed': True,
                'current_count': current_count + 1,
                'limit': limit,
                'remaining': limit - current_count - 1
            }
            
        except Exception as e:
            logger.error(f"Rate limit check failed: {e}")
            # Allow request if rate limiting fails
            return {'allowed': True, 'error': str(e)}
    
    @staticmethod
    def validate_school_access(user, target_school_id):
        """
        Validate if user can access data from target school.
        
        Args:
            user: User instance
            target_school_id: Target school ID
            
        Returns:
            bool: Whether access is allowed
        """
        # Super users can access all schools
        if user.is_superuser:
            return True
        
        # Users can only access their own school
        if user.school and str(user.school.id) == str(target_school_id):
            return True
        
        # Check for cross-school permissions (for special roles)
        cross_school_permissions = [
            'can_manage_schools',
            'can_view_all_schools',
            'can_audit_all_schools'
        ]
        
        user_permissions = RBACManager.get_user_permissions(user, None)
        if any(perm in user_permissions for perm in cross_school_permissions):
            return True
        
        return False


class TokenBlacklist:
    """
    Simple token blacklist implementation using cache.
    """
    
    @staticmethod
    def blacklist_token(token_jti, expiry_time):
        """
        Add token to blacklist.
        
        Args:
            token_jti: Token JTI (unique identifier)
            expiry_time: Token expiry time
        """
        try:
            # Calculate TTL (time to live) for cache
            ttl = int((expiry_time - datetime.now()).total_seconds())
            if ttl > 0:
                cache.set(f"blacklist:{token_jti}", True, ttl)
                logger.info(f"Token {token_jti} blacklisted")
        except Exception as e:
            logger.error(f"Failed to blacklist token: {e}")
    
    @staticmethod
    def is_token_blacklisted(token_jti):
        """
        Check if token is blacklisted.
        
        Args:
            token_jti: Token JTI
            
        Returns:
            bool: Whether token is blacklisted
        """
        try:
            return cache.get(f"blacklist:{token_jti}", False)
        except Exception as e:
            logger.error(f"Failed to check token blacklist: {e}")
            return False
    
    @staticmethod
    def clear_user_tokens(user_id):
        """
        Clear all tokens for a user (logout from all devices).
        
        Args:
            user_id: User ID
        """
        try:
            # This is a simplified implementation
            # In production, you might want to track user tokens more explicitly
            cache_pattern = f"user_tokens:{user_id}:*"
            # Implementation depends on your cache backend
            logger.info(f"Cleared tokens for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to clear user tokens: {e}")


# Security configuration constants
API_SECURITY_CONFIG = {
    'RATE_LIMITS': {
        'default': {'limit': 100, 'window': 3600},  # 100 requests per hour
        'auth': {'limit': 10, 'window': 300},       # 10 auth requests per 5 minutes
        'admin': {'limit': 200, 'window': 3600},    # 200 requests per hour for admins
    },
    'TOKEN_SETTINGS': {
        'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
        'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
        'ROTATE_REFRESH_TOKENS': True,
        'BLACKLIST_AFTER_ROTATION': True,
    },
    'SECURITY_HEADERS': {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Content-Security-Policy': "default-src 'self'",
    }
}
