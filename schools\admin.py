from django.contrib import admin
from django.db.models import Count
from .models import School


@admin.register(School)
class SchoolAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for School model with RBAC features.
    """
    list_display = [
        'name', 'slug', 'city', 'state_province', 'country',
        'subscription_plan', 'status', 'user_count', 'created_at'
    ]
    list_filter = [
        'status', 'subscription_plan', 'country', 'state_province',
        'created_at'
    ]
    search_fields = ['name', 'slug', 'email', 'city', 'phone']
    readonly_fields = ['created_at', 'updated_at', 'user_count']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'slug', 'email', 'phone', 'website')
        }),
        ('Address', {
            'fields': ('address_line_1', 'address_line_2', 'city', 'state_province', 'postal_code', 'country')
        }),
        ('Academic Information', {
            'fields': ('academic_year_start', 'academic_year_end')
        }),
        ('Subscription & Status', {
            'fields': ('subscription_plan', 'status')
        }),
        ('Statistics', {
            'fields': ('user_count',),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Filter schools based on user's permissions."""
        queryset = super().get_queryset(request).annotate(
            user_count=Count('users')
        )

        # Super users can see all schools
        if request.user.is_superuser:
            return queryset

        # Users with system-level permissions can see all schools
        from users.rbac_utils import RBACManager
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return queryset

        # School admins can only see their own school
        if request.user.school:
            return queryset.filter(id=request.user.school.id)

        # Default: no access
        return queryset.none()

    def has_view_permission(self, request, obj=None):
        """Check if user can view schools."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # School admins can view their own school
        if request.user.school:
            if obj and obj.id != request.user.school.id:
                return False
            return RBACManager.user_has_permission(request.user, 'can_view_school_settings', school=request.user.school)

        return False

    def has_change_permission(self, request, obj=None):
        """Check if user can change schools."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # School admins can edit their own school
        if request.user.school:
            if obj and obj.id != request.user.school.id:
                return False
            return RBACManager.user_has_permission(request.user, 'can_manage_school_settings', school=request.user.school)

        return False

    def has_add_permission(self, request):
        """Check if user can add schools."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Only system-level users can create new schools
        return RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None)

    def has_delete_permission(self, request, obj=None):
        """Check if user can delete schools."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Only system-level users can delete schools
        return RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None)

    def user_count(self, obj):
        """Display user count for the school."""
        return obj.user_count if hasattr(obj, 'user_count') else obj.users.count()
    user_count.short_description = 'Users'
    user_count.admin_order_field = 'user_count'
