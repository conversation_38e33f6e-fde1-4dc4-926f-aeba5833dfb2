<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-2033" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 1938.265625 1048.979248046875" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-2033{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-2033 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-2033 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-2033 .error-icon{fill:#a44141;}#graph-2033 .error-text{fill:#ddd;stroke:#ddd;}#graph-2033 .edge-thickness-normal{stroke-width:1px;}#graph-2033 .edge-thickness-thick{stroke-width:3.5px;}#graph-2033 .edge-pattern-solid{stroke-dasharray:0;}#graph-2033 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-2033 .edge-pattern-dashed{stroke-dasharray:3;}#graph-2033 .edge-pattern-dotted{stroke-dasharray:2;}#graph-2033 .marker{fill:lightgrey;stroke:lightgrey;}#graph-2033 .marker.cross{stroke:lightgrey;}#graph-2033 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-2033 p{margin:0;}#graph-2033 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-2033 .cluster-label text{fill:#F9FFFE;}#graph-2033 .cluster-label span{color:#F9FFFE;}#graph-2033 .cluster-label span p{background-color:transparent;}#graph-2033 .label text,#graph-2033 span{fill:#ccc;color:#ccc;}#graph-2033 .node rect,#graph-2033 .node circle,#graph-2033 .node ellipse,#graph-2033 .node polygon,#graph-2033 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-2033 .rough-node .label text,#graph-2033 .node .label text,#graph-2033 .image-shape .label,#graph-2033 .icon-shape .label{text-anchor:middle;}#graph-2033 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-2033 .rough-node .label,#graph-2033 .node .label,#graph-2033 .image-shape .label,#graph-2033 .icon-shape .label{text-align:center;}#graph-2033 .node.clickable{cursor:pointer;}#graph-2033 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-2033 .arrowheadPath{fill:lightgrey;}#graph-2033 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-2033 .flowchart-link{stroke:lightgrey;fill:none;}#graph-2033 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-2033 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-2033 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-2033 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-2033 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-2033 .cluster text{fill:#F9FFFE;}#graph-2033 .cluster span{color:#F9FFFE;}#graph-2033 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-2033 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-2033 rect.text{fill:none;stroke-width:0;}#graph-2033 .icon-shape,#graph-2033 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-2033 .icon-shape p,#graph-2033 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-2033 .icon-shape rect,#graph-2033 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-2033 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-2033 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-2033 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-2033 .monitor&gt;*{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-2033 .monitor span{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-2033 .monitor tspan{fill:#000!important;}#graph-2033 .threshold&gt;*{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-2033 .threshold span{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-2033 .threshold tspan{fill:#000!important;}#graph-2033 .action&gt;*{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-2033 .action span{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-2033 .action tspan{fill:#000!important;}#graph-2033 .recovery&gt;*{fill:#F3E5F5!important;stroke:#9C27B0!important;color:#000!important;}#graph-2033 .recovery span{fill:#F3E5F5!important;stroke:#9C27B0!important;color:#000!important;}#graph-2033 .recovery tspan{fill:#000!important;}#graph-2033 .dashboard&gt;*{fill:#FCE4EC!important;stroke:#C2185B!important;color:#000!important;}#graph-2033 .dashboard span{fill:#FCE4EC!important;stroke:#C2185B!important;color:#000!important;}#graph-2033 .dashboard tspan{fill:#000!important;}#graph-2033 .security&gt;*{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-2033 .security span{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-2033 .security tspan{fill:#000!important;}</style><g><marker id="graph-2033_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-2033_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-2033_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-2033_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-2033_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-2033_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"><g class="cluster" id="subGraph4" data-look="classic"><rect style="" x="60.3359375" y="832.9791717529297" width="1546.4375" height="208"></rect><g class="cluster-label" transform="translate(756.0390625, 832.9791717529297)"><foreignObject width="155.03125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Monitoring Dashboard</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph3" data-look="classic"><rect style="" x="8" y="678.9791717529297" width="1653.953125" height="104"></rect><g class="cluster-label" transform="translate(774.6848945617676, 678.9791717529297)"><foreignObject width="120.58333587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Recovery Actions</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph2" data-look="classic"><rect style="" x="20.005210876464844" y="524.9791717529297" width="1860.4765586853027" height="104"></rect><g class="cluster-label" transform="translate(904.5143222808838, 524.9791717529297)"><foreignObject width="91.45833587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Alert Actions</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph1" data-look="classic"><rect style="" x="44.84375" y="186" width="1841.5234375" height="264.9791717529297"></rect><g class="cluster-label" transform="translate(907.16796875, 186)"><foreignObject width="116.875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Alert Thresholds</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph0" data-look="classic"><rect style="" x="11.59375" y="8" width="1883.5703125" height="128"></rect><g class="cluster-label" transform="translate(896.83203125, 8)"><foreignObject width="113.09375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Health Monitors</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path d="M152.672,111L152.672,115.167C152.672,119.333,152.672,127.667,152.672,136C152.672,144.333,152.672,152.667,152.672,161C152.672,169.333,152.672,177.667,152.75,191.194C152.827,204.721,152.983,223.441,153.061,232.801L153.139,242.162" id="L_API_MONITOR_API_THRESHOLD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M420.87,111L420.87,115.167C420.87,119.333,420.87,127.667,420.87,136C420.87,144.333,420.87,152.667,420.87,161C420.87,169.333,420.87,177.667,420.94,185.417C421.01,193.167,421.151,200.334,421.221,203.917L421.291,207.501" id="L_DB_MONITOR_DB_THRESHOLD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M679.073,111L679.073,115.167C679.073,119.333,679.073,127.667,679.073,136C679.073,144.333,679.073,152.667,679.073,161C679.073,169.333,679.073,177.667,679.15,190.14C679.227,202.613,679.381,219.226,679.459,227.532L679.536,235.839" id="L_STORAGE_MONITOR_STORAGE_THRESHOLD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M963.688,111L963.688,115.167C963.688,119.333,963.688,127.667,963.688,136C963.688,144.333,963.688,152.667,963.688,161C963.688,169.333,963.688,177.667,963.764,188.957C963.84,200.247,963.992,214.493,964.069,221.617L964.145,228.74" id="L_SESSION_MONITOR_SESSION_THRESHOLD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1255.203,111L1255.203,115.167C1255.203,119.333,1255.203,127.667,1255.203,136C1255.203,144.333,1255.203,152.667,1255.203,161C1255.203,169.333,1255.203,177.667,1255.28,189.767C1255.357,201.866,1255.511,217.733,1255.587,225.666L1255.664,233.599" id="L_NOTIF_MONITOR_NOTIF_THRESHOLD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1752.055,111L1752.055,115.167C1752.055,119.333,1752.055,127.667,1752.055,136C1752.055,144.333,1752.055,152.667,1752.055,161C1752.055,169.333,1752.055,177.667,1752.128,186.78C1752.202,195.893,1752.348,205.785,1752.422,210.731L1752.495,215.678" id="L_AUTH_MONITOR_AUTH_THRESHOLD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M153.172,391.818L153.089,401.678C153.005,411.538,152.839,431.259,152.755,447.286C152.672,463.313,152.672,475.646,152.672,487.979C152.672,500.313,152.672,512.646,152.672,522.313C152.672,531.979,152.672,538.979,152.672,542.479L152.672,545.979" id="L_API_THRESHOLD_SCALE_API_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M421.37,426.479L421.286,430.563C421.203,434.646,421.036,442.813,420.953,453.063C420.87,463.313,420.87,475.646,420.87,487.979C420.87,500.313,420.87,512.646,420.87,522.313C420.87,531.979,420.87,538.979,420.87,542.479L420.87,545.979" id="L_DB_THRESHOLD_SCALE_DB_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M679.573,398.141L679.49,406.947C679.406,415.753,679.24,433.366,679.156,448.339C679.073,463.313,679.073,475.646,679.073,487.979C679.073,500.313,679.073,512.646,679.073,522.313C679.073,531.979,679.073,538.979,679.073,542.479L679.073,545.979" id="L_STORAGE_THRESHOLD_CLEANUP_STORAGE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M964.188,405.24L964.104,412.863C964.021,420.486,963.854,435.733,963.771,449.523C963.688,463.313,963.688,475.646,963.688,487.979C963.688,500.313,963.688,512.646,963.688,522.313C963.688,531.979,963.688,538.979,963.688,542.479L963.688,545.979" id="L_SESSION_THRESHOLD_LOAD_BALANCE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1255.703,400.38L1255.62,408.813C1255.536,417.247,1255.37,434.113,1255.286,448.713C1255.203,463.313,1255.203,475.646,1255.203,487.979C1255.203,500.313,1255.203,512.646,1255.203,522.313C1255.203,531.979,1255.203,538.979,1255.203,542.479L1255.203,545.979" id="L_NOTIF_THRESHOLD_FIX_NOTIFICATIONS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1752.555,418.302L1752.471,423.748C1752.388,429.194,1752.221,440.087,1752.138,451.7C1752.055,463.313,1752.055,475.646,1752.055,487.979C1752.055,500.313,1752.055,512.646,1752.055,522.313C1752.055,531.979,1752.055,538.979,1752.055,542.479L1752.055,545.979" id="L_AUTH_THRESHOLD_SECURITY_ALERT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M152.672,603.979L152.672,608.146C152.672,612.313,152.672,620.646,152.672,628.979C152.672,637.313,152.672,645.646,152.672,653.979C152.672,662.313,152.672,670.646,152.672,678.313C152.672,685.979,152.672,692.979,152.672,696.479L152.672,699.979" id="L_SCALE_API_AUTO_SCALE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M420.87,603.979L420.87,608.146C420.87,612.313,420.87,620.646,420.87,628.979C420.87,637.313,420.87,645.646,420.87,653.979C420.87,662.313,420.87,670.646,420.87,678.313C420.87,685.979,420.87,692.979,420.87,696.479L420.87,699.979" id="L_SCALE_DB_FAILOVER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M679.073,603.979L679.073,608.146C679.073,612.313,679.073,620.646,679.073,628.979C679.073,637.313,679.073,645.646,679.073,653.979C679.073,662.313,679.073,670.646,679.073,678.313C679.073,685.979,679.073,692.979,679.073,696.479L679.073,699.979" id="L_CLEANUP_STORAGE_ARCHIVE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M963.688,603.979L963.688,608.146C963.688,612.313,963.688,620.646,963.688,628.979C963.688,637.313,963.688,645.646,963.688,653.979C963.688,662.313,963.688,670.646,963.688,678.313C963.688,685.979,963.688,692.979,963.688,696.479L963.688,699.979" id="L_LOAD_BALANCE_CIRCUIT_BREAKER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1255.203,603.979L1255.203,608.146C1255.203,612.313,1255.203,620.646,1255.203,628.979C1255.203,637.313,1255.203,645.646,1255.203,653.979C1255.203,662.313,1255.203,670.646,1255.203,678.313C1255.203,685.979,1255.203,692.979,1255.203,696.479L1255.203,699.979" id="L_FIX_NOTIFICATIONS_RETRY_QUEUE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1673.31,595.148L1648.873,600.787C1624.436,606.425,1575.562,617.702,1551.125,627.507C1526.688,637.313,1526.688,645.646,1526.688,653.979C1526.688,662.313,1526.688,670.646,1526.688,678.313C1526.688,685.979,1526.688,692.979,1526.688,696.479L1526.688,699.979" id="L_SECURITY_ALERT_BLOCK_IP_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M152.672,757.979L152.672,762.146C152.672,766.313,152.672,774.646,152.672,782.979C152.672,791.313,152.672,799.646,152.672,807.979C152.672,816.313,152.672,824.646,243.297,835.86C333.922,847.074,515.173,861.168,605.798,868.215L696.423,875.262" id="L_AUTO_SCALE_HEALTH_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M420.87,757.979L420.87,762.146C420.87,766.313,420.87,774.646,420.87,782.979C420.87,791.313,420.87,799.646,420.87,807.979C420.87,816.313,420.87,824.646,466.799,834.776C512.728,844.906,604.586,856.832,650.516,862.795L696.445,868.758" id="L_FAILOVER_HEALTH_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M679.073,757.979L679.073,762.146C679.073,766.313,679.073,774.646,679.073,782.979C679.073,791.313,679.073,799.646,679.073,807.979C679.073,816.313,679.073,824.646,689.85,832.75C700.626,840.855,722.18,848.731,732.956,852.668L743.733,856.606" id="L_ARCHIVE_HEALTH_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M963.688,757.979L963.688,762.146C963.688,766.313,963.688,774.646,963.688,782.979C963.688,791.313,963.688,799.646,963.688,807.979C963.688,816.313,963.688,824.646,952.911,832.75C942.134,840.855,920.581,848.731,909.804,852.668L899.028,856.606" id="L_CIRCUIT_BREAKER_HEALTH_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1255.203,757.979L1255.203,762.146C1255.203,766.313,1255.203,774.646,1255.203,782.979C1255.203,791.313,1255.203,799.646,1255.203,807.979C1255.203,816.313,1255.203,824.646,1203.723,834.983C1152.242,845.321,1049.281,857.662,997.801,863.833L946.321,870.003" id="L_RETRY_QUEUE_HEALTH_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1526.688,757.979L1526.688,762.146C1526.688,766.313,1526.688,774.646,1526.688,782.979C1526.688,791.313,1526.688,799.646,1526.688,807.979C1526.688,816.313,1526.688,824.646,1429.963,835.944C1333.238,847.242,1139.788,861.504,1043.063,868.635L946.338,875.766" id="L_BLOCK_IP_HEALTH_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M700.411,894.914L615.043,901.925C529.674,908.936,358.938,922.957,273.569,933.468C188.201,943.979,188.201,950.979,188.201,954.479L188.201,957.979" id="L_HEALTH_DASH_METRICS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M823.499,911.979L823.826,916.146C824.153,920.313,824.807,928.646,825.134,936.313C825.461,943.979,825.461,950.979,825.461,954.479L825.461,957.979" id="L_HEALTH_DASH_ALERTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M942.349,894.723L1029.781,901.766C1117.213,908.809,1292.076,922.894,1379.508,933.437C1466.94,943.979,1466.94,950.979,1466.94,954.479L1466.94,957.979" id="L_HEALTH_DASH_REPORTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1784.016,603.979L1788.948,608.146C1793.88,612.313,1803.745,620.646,1808.677,628.979C1813.609,637.313,1813.609,645.646,1813.609,653.979C1813.609,662.313,1813.609,670.646,1813.609,678.313C1813.609,685.979,1813.609,692.979,1813.609,696.479L1813.609,699.979" id="L_SECURITY_ALERT_ESCALATE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1813.609,757.979L1813.609,762.146C1813.609,766.313,1813.609,774.646,1813.609,782.979C1813.609,791.313,1813.609,799.646,1813.609,807.979C1813.609,816.313,1813.609,824.646,1813.609,832.313C1813.609,839.979,1813.609,846.979,1813.609,850.479L1813.609,853.979" id="L_ESCALATE_INCIDENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path><path d="M1813.609,911.979L1813.609,916.146C1813.609,920.313,1813.609,928.646,1813.609,936.313C1813.609,943.979,1813.609,950.979,1813.609,954.479L1813.609,957.979" id="L_INCIDENT_INVESTIGATE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-2033_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(152.671875, 487.9791717529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(420.86978912353516, 487.9791717529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(679.0729141235352, 487.9791717529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(963.6875, 487.9791717529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1255.203125, 487.9791717529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1752.0546875, 487.9791717529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default monitor" id="flowchart-API_MONITOR-0" transform="translate(152.671875, 72)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-106.078125" y="-39" width="212.15625" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-76.078125, -24)"><rect></rect><foreignObject width="152.15625" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>API Response Monitor<br/>Target: &lt;200ms</p></span></div></foreignObject></g></g><g class="node default monitor" id="flowchart-DB_MONITOR-1" transform="translate(420.86978912353516, 72)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-112.11979675292969" y="-39" width="224.23959350585938" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-82.11979675292969, -24)"><rect></rect><foreignObject width="164.23959350585938" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Database Monitor<br/>Connection Pool Status</p></span></div></foreignObject></g></g><g class="node default monitor" id="flowchart-STORAGE_MONITOR-2" transform="translate(679.0729141235352, 72)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-92.484375" y="-39" width="184.96875" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-62.484375, -24)"><rect></rect><foreignObject width="124.96875" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Storage Monitor<br/>Usage &amp; Capacity</p></span></div></foreignObject></g></g><g class="node default monitor" id="flowchart-SESSION_MONITOR-3" transform="translate(963.6875, 72)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-94.125" y="-39" width="188.25" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-64.125, -24)"><rect></rect><foreignObject width="128.25" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Session Monitor<br/>Active User Count</p></span></div></foreignObject></g></g><g class="node default monitor" id="flowchart-NOTIF_MONITOR-4" transform="translate(1255.203125, 72)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-107.44271087646484" y="-39" width="214.8854217529297" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-77.44271087646484, -24)"><rect></rect><foreignObject width="154.8854217529297" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Notification Monitor<br/>Delivery Success Rate</p></span></div></foreignObject></g></g><g class="node default monitor" id="flowchart-AUTH_MONITOR-5" transform="translate(1752.0546875, 72)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-108.109375" y="-39" width="216.21875" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-78.109375, -24)"><rect></rect><foreignObject width="156.21875" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Auth Monitor<br/>Success/Failure Rates</p></span></div></foreignObject></g></g><g class="node default threshold" id="flowchart-API_THRESHOLD-6" transform="translate(152.671875, 318.48958587646484)"><polygon points="72.828125,0 145.65625,-72.828125 72.828125,-145.65625 0,-72.828125" class="label-container" transform="translate(-72.828125,72.828125)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-45.828125, -12)"><rect></rect><foreignObject width="91.65625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>API &gt; 500ms?</p></span></div></foreignObject></g></g><g class="node default threshold" id="flowchart-DB_THRESHOLD-7" transform="translate(420.86978912353516, 318.48958587646484)"><polygon points="107.48958587646484,0 214.9791717529297,-107.48958587646484 107.48958587646484,-214.9791717529297 0,-107.48958587646484" class="label-container" transform="translate(-107.48958587646484,107.48958587646484)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-80.48958587646484, -12)"><rect></rect><foreignObject width="160.9791717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>DB Connections &gt; 80%?</p></span></div></foreignObject></g></g><g class="node default threshold" id="flowchart-STORAGE_THRESHOLD-8" transform="translate(679.0729141235352, 318.48958587646484)"><polygon points="79.15104293823242,0 158.30208587646484,-79.15104293823242 79.15104293823242,-158.30208587646484 0,-79.15104293823242" class="label-container" transform="translate(-79.15104293823242,79.15104293823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-52.15104293823242, -12)"><rect></rect><foreignObject width="104.30208587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Storage &gt; 90%?</p></span></div></foreignObject></g></g><g class="node default threshold" id="flowchart-SESSION_THRESHOLD-9" transform="translate(963.6875, 318.48958587646484)"><polygon points="86.25,0 172.5,-86.25 86.25,-172.5 0,-86.25" class="label-container" transform="translate(-86.25,86.25)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-59.25, -12)"><rect></rect><foreignObject width="118.5" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Sessions &gt; Limit?</p></span></div></foreignObject></g></g><g class="node default threshold" id="flowchart-NOTIF_THRESHOLD-10" transform="translate(1255.203125, 318.48958587646484)"><polygon points="81.390625,0 162.78125,-81.390625 81.390625,-162.78125 0,-81.390625" class="label-container" transform="translate(-81.390625,81.390625)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-54.390625, -12)"><rect></rect><foreignObject width="108.78125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Delivery &lt; 95%?</p></span></div></foreignObject></g></g><g class="node default threshold" id="flowchart-AUTH_THRESHOLD-11" transform="translate(1752.0546875, 318.48958587646484)"><polygon points="99.3125,0 198.625,-99.3125 99.3125,-198.625 0,-99.3125" class="label-container" transform="translate(-99.3125,99.3125)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-72.3125, -12)"><rect></rect><foreignObject width="144.625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Auth Failures &gt; 10%?</p></span></div></foreignObject></g></g><g class="node default action" id="flowchart-SCALE_API-12" transform="translate(152.671875, 576.9791717529297)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-97.66667175292969" y="-27" width="195.33334350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-67.66667175292969, -12)"><rect></rect><foreignObject width="135.33334350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Scale API Instances</p></span></div></foreignObject></g></g><g class="node default action" id="flowchart-SCALE_DB-13" transform="translate(420.86978912353516, 576.9791717529297)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-83.890625" y="-27" width="167.78125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-53.890625, -12)"><rect></rect><foreignObject width="107.78125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Scale Database</p></span></div></foreignObject></g></g><g class="node default action" id="flowchart-CLEANUP_STORAGE-14" transform="translate(679.0729141235352, 576.9791717529297)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-92.38021087646484" y="-27" width="184.7604217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-62.380210876464844, -12)"><rect></rect><foreignObject width="124.76042175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Cleanup Old Files</p></span></div></foreignObject></g></g><g class="node default action" id="flowchart-LOAD_BALANCE-15" transform="translate(963.6875, 576.9791717529297)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-93.015625" y="-27" width="186.03125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-63.015625, -12)"><rect></rect><foreignObject width="126.03125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Redistribute Load</p></span></div></foreignObject></g></g><g class="node default action" id="flowchart-FIX_NOTIFICATIONS-16" transform="translate(1255.203125, 576.9791717529297)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-113.52604675292969" y="-27" width="227.05209350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-83.52604675292969, -12)"><rect></rect><foreignObject width="167.05209350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Fix Notification Service</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-SECURITY_ALERT-17" transform="translate(1752.0546875, 576.9791717529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-78.74479293823242" y="-27" width="157.48958587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-48.74479293823242, -12)"><rect></rect><foreignObject width="97.48958587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Security Alert</p></span></div></foreignObject></g></g><g class="node default recovery" id="flowchart-AUTO_SCALE-18" transform="translate(152.671875, 730.9791717529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-109.671875" y="-27" width="219.34375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-79.671875, -12)"><rect></rect><foreignObject width="159.34375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Auto-scaling Triggered</p></span></div></foreignObject></g></g><g class="node default recovery" id="flowchart-FAILOVER-19" transform="translate(420.86978912353516, 730.9791717529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-93.88541793823242" y="-27" width="187.77083587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-63.88541793823242, -12)"><rect></rect><foreignObject width="127.77083587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Database Failover</p></span></div></foreignObject></g></g><g class="node default recovery" id="flowchart-ARCHIVE-20" transform="translate(679.0729141235352, 730.9791717529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-114.31771087646484" y="-27" width="228.6354217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-84.31771087646484, -12)"><rect></rect><foreignObject width="168.6354217529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Archive to Cold Storage</p></span></div></foreignObject></g></g><g class="node default recovery" id="flowchart-CIRCUIT_BREAKER-21" transform="translate(963.6875, 730.9791717529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-120.296875" y="-27" width="240.59375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-90.296875, -12)"><rect></rect><foreignObject width="180.59375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Circuit Breaker Activated</p></span></div></foreignObject></g></g><g class="node default recovery" id="flowchart-RETRY_QUEUE-22" transform="translate(1255.203125, 730.9791717529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-121.21875" y="-27" width="242.4375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-91.21875, -12)"><rect></rect><foreignObject width="182.4375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Retry Failed Notifications</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-BLOCK_IP-23" transform="translate(1526.6875, 730.9791717529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-100.265625" y="-27" width="200.53125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-70.265625, -12)"><rect></rect><foreignObject width="140.53125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Block Suspicious IPs</p></span></div></foreignObject></g></g><g class="node default dashboard" id="flowchart-HEALTH_DASH-24" transform="translate(821.3802070617676, 884.9791717529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-120.96875" y="-27" width="241.9375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-90.96875, -12)"><rect></rect><foreignObject width="181.9375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>System Health Dashboard</p></span></div></foreignObject></g></g><g class="node default dashboard" id="flowchart-METRICS-25" transform="translate(188.20052337646484, 988.9791717529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-92.86458587646484" y="-27" width="185.7291717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-62.864585876464844, -12)"><rect></rect><foreignObject width="125.72917175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Real-time Metrics</p></span></div></foreignObject></g></g><g class="node default dashboard" id="flowchart-ALERTS-26" transform="translate(825.4609375, 988.9791717529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-95.515625" y="-27" width="191.03125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-65.515625, -12)"><rect></rect><foreignObject width="131.03125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Alert Management</p></span></div></foreignObject></g></g><g class="node default dashboard" id="flowchart-REPORTS-27" transform="translate(1466.9401016235352, 988.9791717529297)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-104.83333587646484" y="-27" width="209.6666717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-74.83333587646484, -12)"><rect></rect><foreignObject width="149.6666717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Performance Reports</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-ESCALATE-83" transform="translate(1813.609375, 730.9791717529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-116.65625" y="-27" width="233.3125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-86.65625, -12)"><rect></rect><foreignObject width="173.3125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Escalate to Super Admin</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-INCIDENT-85" transform="translate(1813.609375, 884.9791717529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-111.75" y="-27" width="223.5" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-81.75, -12)"><rect></rect><foreignObject width="163.5" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Create Incident Report</p></span></div></foreignObject></g></g><g class="node default security" id="flowchart-INVESTIGATE-87" transform="translate(1813.609375, 988.9791717529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-104.609375" y="-27" width="209.21875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-74.609375, -12)"><rect></rect><foreignObject width="149.21875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Investigation Process</p></span></div></foreignObject></g></g></g></g></g></svg>