from rest_framework import serializers
from .models import School


class SchoolSerializer(serializers.ModelSerializer):
    """
    Serializer for School model with full details.
    """
    current_students = serializers.SerializerMethodField()
    current_staff = serializers.SerializerMethodField()
    
    class Meta:
        model = School
        fields = [
            'id', 'name', 'slug', 'email', 'phone', 'website',
            'address_line_1', 'address_line_2', 'city', 'state_province',
            'postal_code', 'country', 'timezone', 'academic_year_start',
            'academic_year_end', 'subscription_plan', 'status',
            'max_students', 'max_staff', 'current_students', 'current_staff',
            'created_at', 'updated_at', 'full_address', 'is_active'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'current_students', 'current_staff', 'full_address', 'is_active']
    
    def get_current_students(self, obj):
        """Get current number of students"""
        return obj.users.filter(user_type='student', is_active=True).count()
    
    def get_current_staff(self, obj):
        """Get current number of staff"""
        return obj.users.filter(user_type__in=['lecturer', 'staff', 'school_admin'], is_active=True).count()


class SchoolListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for School list views.
    """
    current_students = serializers.SerializerMethodField()
    current_staff = serializers.SerializerMethodField()
    
    class Meta:
        model = School
        fields = [
            'id', 'name', 'slug', 'email', 'city', 'country',
            'subscription_plan', 'status', 'current_students', 'current_staff',
            'created_at', 'is_active'
        ]
        read_only_fields = ['id', 'created_at', 'current_students', 'current_staff', 'is_active']
    
    def get_current_students(self, obj):
        """Get current number of students"""
        return obj.users.filter(user_type='student', is_active=True).count()
    
    def get_current_staff(self, obj):
        """Get current number of staff"""
        return obj.users.filter(user_type__in=['lecturer', 'staff', 'school_admin'], is_active=True).count()


class SchoolCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating new schools.
    """
    
    class Meta:
        model = School
        fields = [
            'name', 'slug', 'email', 'phone', 'website',
            'address_line_1', 'address_line_2', 'city', 'state_province',
            'postal_code', 'country', 'timezone', 'academic_year_start',
            'academic_year_end', 'subscription_plan', 'max_students', 'max_staff'
        ]
    
    def validate_slug(self, value):
        """Ensure slug is unique"""
        if School.objects.filter(slug=value).exists():
            raise serializers.ValidationError("A school with this slug already exists.")
        return value
    
    def validate_email(self, value):
        """Ensure email is unique"""
        if School.objects.filter(email=value).exists():
            raise serializers.ValidationError("A school with this email already exists.")
        return value
