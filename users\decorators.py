"""
Enhanced RBAC decorators for Django views and API endpoints with security features.
"""

import logging
from functools import wraps
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.cache import never_cache
from rest_framework.decorators import permission_classes, api_view
from rest_framework.permissions import BasePermission
from rest_framework.response import Response
from rest_framework import status
from .rbac_utils import RBACManager

logger = logging.getLogger(__name__)


def require_permission(permission_codename: str, school_context: bool = True):
    """
    Decorator to require a specific permission for a view.
    
    Args:
        permission_codename: The permission codename required
        school_context: Whether to consider school context
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            user = request.user
            school = None
            
            # Get school context if needed
            if school_context:
                # Try to get school from various sources
                school = getattr(user, 'school', None)
                if not school and hasattr(request, 'school'):
                    school = request.school
                if not school and 'school_id' in kwargs:
                    from schools.models import School
                    try:
                        school = School.objects.get(id=kwargs['school_id'])
                    except School.DoesNotExist:
                        pass
            
            # Check permission
            if not RBACManager.user_has_permission(user, permission_codename, school):
                if request.content_type == 'application/json':
                    return JsonResponse({
                        'error': 'Permission denied',
                        'required_permission': permission_codename
                    }, status=403)
                else:
                    raise PermissionDenied(f"Permission '{permission_codename}' required")
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def require_any_permission(*permission_codenames, school_context: bool = True):
    """
    Decorator to require any of the specified permissions for a view.
    
    Args:
        permission_codenames: List of permission codenames (user needs any one)
        school_context: Whether to consider school context
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            user = request.user
            school = None
            
            # Get school context if needed
            if school_context:
                school = getattr(user, 'school', None)
                if not school and hasattr(request, 'school'):
                    school = request.school
                if not school and 'school_id' in kwargs:
                    from schools.models import School
                    try:
                        school = School.objects.get(id=kwargs['school_id'])
                    except School.DoesNotExist:
                        pass
            
            # Check if user has any of the required permissions
            has_permission = any(
                RBACManager.user_has_permission(user, perm, school)
                for perm in permission_codenames
            )
            
            if not has_permission:
                if request.content_type == 'application/json':
                    return JsonResponse({
                        'error': 'Permission denied',
                        'required_permissions': list(permission_codenames)
                    }, status=403)
                else:
                    raise PermissionDenied(f"One of these permissions required: {', '.join(permission_codenames)}")
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def require_all_permissions(*permission_codenames, school_context: bool = True):
    """
    Decorator to require all of the specified permissions for a view.
    
    Args:
        permission_codenames: List of permission codenames (user needs all)
        school_context: Whether to consider school context
    """
    def decorator(view_func):
        @wraps(view_func)
        @login_required
        def _wrapped_view(request, *args, **kwargs):
            user = request.user
            school = None
            
            # Get school context if needed
            if school_context:
                school = getattr(user, 'school', None)
                if not school and hasattr(request, 'school'):
                    school = request.school
                if not school and 'school_id' in kwargs:
                    from schools.models import School
                    try:
                        school = School.objects.get(id=kwargs['school_id'])
                    except School.DoesNotExist:
                        pass
            
            # Check if user has all required permissions
            missing_permissions = [
                perm for perm in permission_codenames
                if not RBACManager.user_has_permission(user, perm, school)
            ]
            
            if missing_permissions:
                if request.content_type == 'application/json':
                    return JsonResponse({
                        'error': 'Permission denied',
                        'missing_permissions': missing_permissions
                    }, status=403)
                else:
                    raise PermissionDenied(f"Missing permissions: {', '.join(missing_permissions)}")
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


class RBACPermission(BasePermission):
    """
    DRF permission class for RBAC-based access control.
    """
    
    def __init__(self, permission_codename: str, school_context: bool = True):
        self.permission_codename = permission_codename
        self.school_context = school_context
    
    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            return False
        
        school = None
        if self.school_context:
            # Get school context
            school = getattr(request.user, 'school', None)
            if not school and hasattr(request, 'school'):
                school = request.school
            if not school and hasattr(view, 'get_school'):
                school = view.get_school()
        
        return RBACManager.user_has_permission(
            request.user, 
            self.permission_codename, 
            school
        )
    
    def has_object_permission(self, request, view, obj):
        # For object-level permissions, we can add additional checks
        # based on the object's school or other attributes
        if hasattr(obj, 'school') and self.school_context:
            school = obj.school
            return RBACManager.user_has_permission(
                request.user,
                self.permission_codename,
                school
            )
        
        return self.has_permission(request, view)


class MultiPermissionMixin:
    """
    Mixin for DRF views that require different permissions for different actions.
    """
    
    permission_map = {}  # Override in subclasses
    
    def get_permissions(self):
        """
        Return the list of permission instances that this view requires.
        """
        try:
            # Get permission for current action
            permission_codename = self.permission_map.get(self.action)
            if permission_codename:
                return [RBACPermission(permission_codename)]
        except AttributeError:
            pass
        
        return super().get_permissions()


def school_admin_required(view_func):
    """
    Decorator to require school admin permissions.
    """
    return require_any_permission(
        'can_manage_school_settings',
        'can_assign_roles',
        'can_view_reports'
    )(view_func)


def system_admin_required(view_func):
    """
    Decorator to require system admin permissions.
    """
    return require_permission('can_manage_schools', school_context=False)(view_func)


def teacher_required(view_func):
    """
    Decorator to require teacher-level permissions.
    """
    return require_any_permission(
        'can_view_grades',
        'can_edit_grades',
        'can_upload_materials'
    )(view_func)


def student_access_required(view_func):
    """
    Decorator for student-accessible views.
    """
    return require_permission('can_view_grades')(view_func)


class ConditionalPermissionMixin:
    """
    Mixin for views that have conditional permission requirements.
    """
    
    def check_conditional_permission(self, request, *args, **kwargs):
        """
        Override this method to implement conditional permission logic.
        Should return True if permission is granted, False otherwise.
        """
        return True
    
    def dispatch(self, request, *args, **kwargs):
        if not self.check_conditional_permission(request, *args, **kwargs):
            return Response(
                {'error': 'Permission denied'},
                status=status.HTTP_403_FORBIDDEN
            )
        return super().dispatch(request, *args, **kwargs)


def audit_action(action_type: str, description: str = None):
    """
    Decorator to automatically log actions to the audit trail.
    
    Args:
        action_type: Type of action being performed
        description: Description of the action (optional)
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            from .models import RoleAuditLog
            
            # Execute the view
            response = view_func(request, *args, **kwargs)
            
            # Log the action if successful
            if hasattr(response, 'status_code') and 200 <= response.status_code < 300:
                school = getattr(request.user, 'school', None)
                
                # Create audit log entry
                RoleAuditLog.objects.create(
                    action_type=action_type,
                    description=description or f"Action {action_type} performed",
                    performed_by=request.user if request.user.is_authenticated else None,
                    school=school,
                    ip_address=request.META.get('REMOTE_ADDR'),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    metadata={
                        'view_name': view_func.__name__,
                        'method': request.method,
                        'path': request.path,
                    }
                )
            
            return response
        return _wrapped_view
    return decorator


# Enhanced API Security Decorators

def api_permission_required(permission_codename: str, school_context: bool = True):
    """
    Enhanced API permission decorator with JWT claims validation.

    Args:
        permission_codename: The permission codename required
        school_context: Whether to consider school context
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Check authentication
            if not request.user or not request.user.is_authenticated:
                return JsonResponse({
                    'error': 'Authentication required',
                    'detail': 'Valid JWT token required'
                }, status=401)

            # Check for school access violation from middleware
            if hasattr(request, 'school_access_violation') and request.school_access_violation:
                return JsonResponse({
                    'error': 'School access violation',
                    'detail': 'Cannot access data from different school'
                }, status=403)

            # Get school context
            school = None
            if school_context:
                school = getattr(request, 'user_school_context', None)
                if not school:
                    school = getattr(request.user, 'school', None)

            # Check permission using JWT claims if available
            if hasattr(request, 'jwt_claims'):
                jwt_permissions = request.jwt_claims.get('permissions', [])
                if permission_codename not in jwt_permissions:
                    # Fallback to database check
                    if not RBACManager.user_has_permission(request.user, permission_codename, school):
                        return JsonResponse({
                            'error': 'Permission denied',
                            'required_permission': permission_codename,
                            'user_permissions': jwt_permissions
                        }, status=403)
            else:
                # Standard permission check
                if not RBACManager.user_has_permission(request.user, permission_codename, school):
                    return JsonResponse({
                        'error': 'Permission denied',
                        'required_permission': permission_codename
                    }, status=403)

            # Log API access
            logger.info(
                f"API access granted: {request.user.email} -> {request.method} {request.path} "
                f"(permission: {permission_codename})"
            )

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator


def secure_api_endpoint(permission_codename: str = None, rate_limit: int = None,
                       require_https: bool = False, school_context: bool = True):
    """
    Comprehensive API security decorator combining multiple security measures.

    Args:
        permission_codename: Required permission (optional)
        rate_limit: Rate limit per minute (optional)
        require_https: Whether HTTPS is required
        school_context: Whether to enforce school context
    """
    def decorator(view_func):
        @wraps(view_func)
        @never_cache  # Prevent caching of sensitive data
        def _wrapped_view(request, *args, **kwargs):
            # HTTPS check
            if require_https and not request.is_secure():
                return JsonResponse({
                    'error': 'HTTPS required',
                    'detail': 'This endpoint requires a secure connection'
                }, status=400)

            # Rate limiting check (if middleware didn't handle it)
            if rate_limit and hasattr(request, 'user') and request.user.is_authenticated:
                # This would be handled by middleware, but we can add additional checks
                pass

            # Permission check
            if permission_codename:
                permission_check = api_permission_required(permission_codename, school_context)
                permission_result = permission_check(view_func)(request, *args, **kwargs)
                if hasattr(permission_result, 'status_code') and permission_result.status_code != 200:
                    return permission_result

            # Add security headers
            response = view_func(request, *args, **kwargs)
            if hasattr(response, '__setitem__'):
                response['X-API-Version'] = '1.0'
                response['X-RateLimit-Limit'] = str(rate_limit) if rate_limit else '100'
                response['Cache-Control'] = 'no-store, no-cache, must-revalidate'

            return response
        return _wrapped_view
    return decorator


def school_isolation_required(view_func):
    """
    Decorator to enforce strict school data isolation.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user or not request.user.is_authenticated:
            return JsonResponse({'error': 'Authentication required'}, status=401)

        # Super users can access all schools
        if request.user.is_superuser:
            return view_func(request, *args, **kwargs)

        # Check if user has a school
        user_school = request.user.school
        if not user_school:
            return JsonResponse({
                'error': 'School context required',
                'detail': 'User must be associated with a school'
            }, status=403)

        # Add school filter to request
        request.school_filter = user_school

        return view_func(request, *args, **kwargs)
    return _wrapped_view


def admin_api_required(view_func):
    """
    Decorator for admin-only API endpoints.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user or not request.user.is_authenticated:
            return JsonResponse({'error': 'Authentication required'}, status=401)

        # Check if user has admin permissions
        is_admin = (
            request.user.is_superuser or
            RBACManager.user_has_permission(request.user, 'can_manage_schools') or
            RBACManager.user_has_permission(request.user, 'can_assign_roles')
        )

        if not is_admin:
            return JsonResponse({
                'error': 'Admin access required',
                'detail': 'This endpoint requires administrative privileges'
            }, status=403)

        return view_func(request, *args, **kwargs)
    return _wrapped_view


class EnhancedRBACPermission(BasePermission):
    """
    Enhanced DRF permission class with JWT claims support and detailed error messages.
    """

    def __init__(self, permission_codename: str, school_context: bool = True,
                 allow_superuser: bool = True):
        self.permission_codename = permission_codename
        self.school_context = school_context
        self.allow_superuser = allow_superuser

    def has_permission(self, request, view):
        if not request.user or not request.user.is_authenticated:
            self.message = 'Authentication credentials were not provided.'
            return False

        # Superuser bypass
        if self.allow_superuser and request.user.is_superuser:
            return True

        # Check for school access violation
        if hasattr(request, 'school_access_violation') and request.school_access_violation:
            self.message = 'Cannot access data from different school.'
            return False

        # Get school context
        school = None
        if self.school_context:
            school = getattr(request, 'user_school_context', None)
            if not school:
                school = getattr(request.user, 'school', None)

        # Check permission using JWT claims first
        if hasattr(request, 'jwt_claims'):
            jwt_permissions = request.jwt_claims.get('permissions', [])
            if self.permission_codename in jwt_permissions:
                return True

        # Fallback to database check
        has_permission = RBACManager.user_has_permission(
            request.user,
            self.permission_codename,
            school
        )

        if not has_permission:
            user_permissions = getattr(request, 'user_permissions', [])
            self.message = (
                f'Permission denied. Required: {self.permission_codename}. '
                f'User has: {", ".join(user_permissions[:5])}{"..." if len(user_permissions) > 5 else ""}'
            )

        return has_permission

    def has_object_permission(self, request, view, obj):
        # Object-level permission checks
        if hasattr(obj, 'school') and self.school_context:
            user_school = getattr(request.user, 'school', None)
            if obj.school != user_school and not request.user.is_superuser:
                self.message = 'Cannot access object from different school.'
                return False

        return self.has_permission(request, view)


class APIKeyPermission(BasePermission):
    """
    Permission class for API key authentication (for external integrations).
    """

    def has_permission(self, request, view):
        api_key = request.META.get('HTTP_X_API_KEY')
        if not api_key:
            self.message = 'API key required in X-API-Key header.'
            return False

        # Validate API key (implement your API key validation logic)
        # This is a placeholder - implement actual API key validation
        valid_api_keys = getattr(settings, 'VALID_API_KEYS', [])
        if api_key not in valid_api_keys:
            self.message = 'Invalid API key.'
            return False

        return True


def validate_request_data(required_fields: list = None, optional_fields: list = None):
    """
    Decorator to validate request data structure.

    Args:
        required_fields: List of required field names
        optional_fields: List of optional field names
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            if request.method in ['POST', 'PUT', 'PATCH']:
                data = getattr(request, 'data', {})

                # Check required fields
                if required_fields:
                    missing_fields = [field for field in required_fields if field not in data]
                    if missing_fields:
                        return JsonResponse({
                            'error': 'Missing required fields',
                            'missing_fields': missing_fields,
                            'required_fields': required_fields
                        }, status=400)

                # Validate field types and values
                errors = []
                for field, value in data.items():
                    if field in required_fields or (optional_fields and field in optional_fields):
                        # Add custom validation logic here
                        if isinstance(value, str) and len(value.strip()) == 0:
                            errors.append(f'{field} cannot be empty')

                if errors:
                    return JsonResponse({
                        'error': 'Validation errors',
                        'validation_errors': errors
                    }, status=400)

            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator
