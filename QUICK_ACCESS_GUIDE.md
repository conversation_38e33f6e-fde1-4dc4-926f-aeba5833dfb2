# SMS RBAC System - Quick Access Guide

## 🚀 Instant Access Information

### Start the System
```bash
# Navigate to project directory
cd sms-project

# Start Django server
python manage.py runserver
```

### 🔗 Dashboard Links

| **Service** | **URL** | **Purpose** |
|-------------|---------|-------------|
| **🎛️ Admin Dashboard** | [http://127.0.0.1:8000/admin/](http://127.0.0.1:8000/admin/) | Complete RBAC management |
| **📊 API Root** | [http://127.0.0.1:8000/api/](http://127.0.0.1:8000/api/) | REST API endpoints |
| **🔐 RBAC API** | [http://127.0.0.1:8000/api/rbac/](http://127.0.0.1:8000/api/rbac/) | Role & permission management |
| **👥 Users API** | [http://127.0.0.1:8000/api/v1/users/](http://127.0.0.1:8000/api/v1/users/) | User management |
| **🏫 Schools API** | [http://127.0.0.1:8000/api/v1/schools/](http://127.0.0.1:8000/api/v1/schools/) | School management |

### 🔑 Default Login Credentials

#### Super Administrator
```
📧 Email: <EMAIL>
🔒 Password: admin123
🎯 Role: System Administrator
🌐 Access: Full system access (all schools)
```

#### School Administrator
```
📧 Email: <EMAIL>
🔒 Password: school123
🎯 Role: School Administrator
🌐 Access: Full access within assigned school
```

#### Teacher Account
```
📧 Email: <EMAIL>
🔒 Password: teacher123
🎯 Role: Teacher
🌐 Access: Student and class management
```

#### Student Account
```
📧 Email: <EMAIL>
🔒 Password: student123
🎯 Role: Student
🌐 Access: Student portal access
```

> ⚠️ **Security Warning**: Change these credentials immediately in production!

### 🔧 Create/Recreate Users

```bash
# Create all default users with test schools
python manage.py create_default_users --create-schools --force

# Recreate existing users
python manage.py create_default_users --force
```

### 🛠️ Quick Setup Commands

```bash
# Apply database migrations
python manage.py migrate

# Create superuser (if needed)
python manage.py createsuperuser

# Create standard role templates
python manage.py create_role_templates

# System health check
python manage.py rbac_maintenance --task=generate_health_report

# System integrity check
python manage.py rbac_maintenance --task=validate_system_integrity
```

### 📱 API Authentication

#### Get JWT Token
```bash
curl -X POST http://127.0.0.1:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

#### Use Token in Requests
```bash
curl -X GET http://127.0.0.1:8000/api/rbac/roles/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 🎯 Key Admin Dashboard Sections

| **Section** | **URL Path** | **Description** |
|-------------|--------------|-----------------|
| **👥 Users** | `/admin/users/user/` | User account management |
| **🎭 Roles** | `/admin/users/role/` | Role creation & management |
| **📋 Role Templates** | `/admin/users/roletemplate/` | Standardized role templates |
| **🔐 Permissions** | `/admin/users/permission/` | System permissions |
| **🔗 User Roles** | `/admin/users/userrole/` | Role assignments & workflows |
| **📊 Audit Logs** | `/admin/users/roleauditlog/` | Activity tracking |
| **🏫 Schools** | `/admin/schools/school/` | School management |

### 🔧 Essential API Endpoints

#### Role Management
```bash
# List all roles
GET /api/rbac/roles/

# Create new role
POST /api/rbac/roles/

# Role details
GET /api/rbac/roles/{id}/

# Assign role to user
POST /api/rbac/roles/{id}/assign_to_user/
```

#### Role Templates
```bash
# List templates
GET /api/rbac/role-templates/

# Create role from template
POST /api/rbac/role-templates/{id}/create_role/

# Validate template
POST /api/rbac/role-templates/{id}/validate_template/
```

#### Analytics & Monitoring
```bash
# Dashboard statistics
GET /api/rbac/analytics/dashboard_stats/

# Security analytics
GET /api/rbac/analytics/security_analytics/

# Compliance report
GET /api/rbac/analytics/compliance_report/
```

#### Workflow Management
```bash
# Pending approvals
GET /api/rbac/workflows/pending_approvals/

# Bulk approve
POST /api/rbac/workflows/bulk_approve/

# Workflow statistics
GET /api/rbac/workflows/workflow_statistics/
```

### 🎨 Admin Dashboard Features

#### 📊 Enhanced Features Available:
- **Role Hierarchy Visualization** - Interactive role structure
- **Permission Matrix** - Visual permission grid
- **Bulk Operations** - Mass role assignments
- **Real-time Analytics** - Usage statistics
- **Security Monitoring** - Alert management
- **Workflow Queues** - Approval management
- **Audit Trail Viewer** - Activity history
- **Multi-tenant Controls** - School isolation

### 🔍 Testing & Validation

#### Run Tests
```bash
# Complete RBAC test suite
python manage.py test users.tests.test_rbac_complete_system

# Multi-tenant isolation tests
python manage.py test users.tests.test_multi_tenant_isolation
```

#### System Maintenance
```bash
# Complete maintenance check
python manage.py rbac_maintenance --task=all

# Performance testing
python manage.py rbac_maintenance --task=test_system_performance

# Cleanup expired roles
python manage.py rbac_maintenance --task=cleanup_expired_roles
```

### 📱 Mobile App Integration

#### React Native Configuration
```javascript
const API_CONFIG = {
  baseURL: 'http://127.0.0.1:8000/api/',
  authEndpoint: 'auth/login/',
  rbacEndpoint: 'rbac/',
  timeout: 10000
};
```

### 🆘 Quick Troubleshooting

#### Common Issues & Solutions:

**🔴 Can't access admin dashboard:**
- Verify server is running: `python manage.py runserver`
- Check URL: `http://127.0.0.1:8000/admin/`
- Ensure superuser exists: `python manage.py createsuperuser`

**🔴 Login credentials not working:**
- Try default credentials above
- Reset password: `python manage.<NAME_EMAIL>`
- Check user exists in admin panel

**🔴 API returns 403 Forbidden:**
- Verify JWT token is valid
- Check user has required permissions
- Ensure proper Authorization header

**🔴 Role assignments not working:**
- Verify school context matches
- Check role permissions are granted
- Ensure user is active

### 📞 Support & Documentation

- **Complete Documentation**: `RBAC_SYSTEM_DOCUMENTATION.md`
- **System Health**: Run `python manage.py rbac_maintenance --task=generate_health_report`
- **Debug Mode**: Add `DEBUG=True` in Django settings for detailed errors

---

## 🎉 You're Ready to Go!

1. **Start the server** with `python manage.py runserver`
2. **Open admin dashboard** at [http://127.0.0.1:8000/admin/](http://127.0.0.1:8000/admin/)
3. **Login** with `<EMAIL>` / `admin123`
4. **Explore** the RBAC features and create your first roles!

> 💡 **Tip**: Bookmark this guide for quick reference during development and testing.
