# SMS RBAC System - Comprehensive Test Credentials

## 🚨 **IMPORTANT UPDATE - ADMIN ACCESS FIXED!**

✅ **School administrators can now access Django admin dashboard!**
- Fixed permission system integration with Django admin
- School admins can now view and manage their school's data
- Multi-tenant isolation working correctly
- All RBAC permissions properly configured

## 🎯 **Complete Testing Environment**

This document contains **144 test user accounts** across **6 schools** demonstrating the full RBAC system capabilities with multi-tenant isolation, role hierarchy, and diverse permission levels.

---

## 📊 **System Statistics**

- **Total Users**: 144
- **Total Schools**: 6
- **System Administrators**: 3
- **School Administrators**: 12
- **Teachers**: 37
- **Students**: 61
- **Staff Members**: 30
- **Special Test Cases**: 2

---

## 🏫 **Test Schools Created**

| **School Name** | **Type** | **Subscription** | **Status** | **Location** |
|-----------------|----------|------------------|------------|--------------|
| **Greenwood Elementary School** | Elementary | Basic | Active | Springfield, IL |
| **Riverside High School** | High School | Premium | Active | Riverside, CA |
| **Metropolitan University Prep** | Prep School | Enterprise | Active | Metro City, NY |
| **Sunset Community College** | Community College | Standard | Pending | Los Angeles, CA |
| **Greenwood High School** | High School | Basic | Active | Springfield, IL |
| **Riverside Academy** | Academy | Premium | Active | Riverside, CA |

---

## 🔑 **Login Credentials by Role Type**

### 🔴 **System Administrators** (Full System Access)

| **Email** | **Password** | **Name** | **Access Level** |
|-----------|--------------|----------|------------------|
| `<EMAIL>` | `admin123` | System Administrator | **Superuser** - Complete system access |
| `<EMAIL>` | `Manager123!` | System Manager | System management without superuser |
| `<EMAIL>` | `Auditor123!` | System Auditor | Read-only system access for auditing |

### 🟡 **School Administrators** (School Management)

| **Email** | **Password** | **School** | **Role** |
|-----------|--------------|------------|----------|
| `<EMAIL>` | `Admin123!` | Greenwood Elementary | Primary Administrator |
| `<EMAIL>` | `Assistant123!` | Greenwood Elementary | Assistant Administrator |
| `<EMAIL>` | `Admin223!` | Riverside High School | Primary Administrator |
| `<EMAIL>` | `Assistant223!` | Riverside High School | Assistant Administrator |
| `<EMAIL>` | `Admin323!` | Metropolitan University Prep | Primary Administrator |
| `<EMAIL>` | `Assistant323!` | Metropolitan University Prep | Assistant Administrator |
| `<EMAIL>` | `Admin423!` | Sunset Community College | Primary Administrator |
| `<EMAIL>` | `Assistant423!` | Sunset Community College | Assistant Administrator |
| `<EMAIL>` | `Admin523!` | Greenwood High School | Primary Administrator |
| `<EMAIL>` | `Assistant523!` | Greenwood High School | Assistant Administrator |
| `<EMAIL>` | `Admin623!` | Riverside Academy | Primary Administrator |
| `<EMAIL>` | `Assistant623!` | Riverside Academy | Assistant Administrator |

### 🟢 **Teachers** (Subject-Based Access)

**Password Pattern**: `Teacher[1-6]23!` (e.g., Teacher123!, Teacher223!, etc.)

| **Subject** | **Schools Available** | **Example Email** |
|-------------|----------------------|-------------------|
| **Mathematics** | All 6 schools | `<EMAIL>` |
| **English** | All 6 schools | `<EMAIL>` |
| **Science** | All 6 schools | `<EMAIL>` |
| **History** | All 6 schools | `<EMAIL>` |
| **Art** | All 6 schools | `<EMAIL>` |
| **Physical Education** | All 6 schools | `<EMAIL>` |

### 🔵 **Students** (Student Portal Access)

**Password Pattern**: `Student[1-10]23!` (e.g., Student123!, Student223!, etc.)

| **Student Name** | **Schools Available** | **Example Email** | **Student ID** |
|------------------|----------------------|-------------------|----------------|
| **Alice Johnson** | All 6 schools | `<EMAIL>` | GRE1000 |
| **Bob Smith** | All 6 schools | `<EMAIL>` | RIV1001 |
| **Carol Davis** | All 6 schools | `<EMAIL>` | MET1002 |
| **David Wilson** | All 6 schools | `<EMAIL>` | SUN1003 |
| **Emma Brown** | All 6 schools | `<EMAIL>` | GRE1004 |
| **Frank Miller** | All 6 schools | `<EMAIL>` | RIV1005 |
| **Grace Taylor** | All 6 schools | `<EMAIL>` | GRE1006 |
| **Henry Anderson** | All 6 schools | `<EMAIL>` | RIV1007 |
| **Ivy Thomas** | All 6 schools | `<EMAIL>` | MET1008 |
| **Jack Jackson** | All 6 schools | `<EMAIL>` | SUN1009 |

### 🟣 **Staff Members** (Specialized Access)

**Password Pattern**: `[Role]123!` (e.g., Librarian123!, Counselor123!, etc.)

| **Role** | **Department** | **Schools Available** | **Example Email** |
|----------|----------------|----------------------|-------------------|
| **Librarian** | Library | All 6 schools | `<EMAIL>` |
| **Counselor** | Counseling | All 6 schools | `<EMAIL>` |
| **Nurse** | Health | All 6 schools | `<EMAIL>` |
| **Security** | Security | All 6 schools | `<EMAIL>` |
| **Maintenance** | Facilities | All 6 schools | `<EMAIL>` |

### 🔶 **Special Test Cases**

| **Email** | **Password** | **Purpose** | **Status** |
|-----------|--------------|-------------|------------|
| `<EMAIL>` | `Inactive123!` | Test inactive user scenarios | **Inactive** |
| `<EMAIL>` | `MultiRole123!` | Test multiple role assignments | **Active** |

---

## 🚀 **Quick Access Instructions**

### 1. **Start the Server**
```bash
cd sms-project
python manage.py runserver
```

### 2. **Access Points**
- **Admin Dashboard**: [http://127.0.0.1:8000/admin/](http://127.0.0.1:8000/admin/)
- **API Root**: [http://127.0.0.1:8000/api/](http://127.0.0.1:8000/api/)
- **RBAC API**: [http://127.0.0.1:8000/api/rbac/](http://127.0.0.1:8000/api/rbac/)

### 3. **Login Examples**
- **System Admin**: `<EMAIL>` / `admin123`
- **School Admin**: `<EMAIL>` / `Admin123!`
- **Teacher**: `<EMAIL>` / `Teacher223!`
- **Student**: `<EMAIL>` / `Student123!`
- **Staff**: `<EMAIL>` / `Librarian123!`

---

## 🧪 **Testing Scenarios**

### **Multi-Tenant Isolation Testing**
1. Login as `<EMAIL>` - should only see Greenwood Elementary data
2. Login as `<EMAIL>` - should only see Riverside High data
3. Verify cross-school data isolation is enforced

### **Role Hierarchy Testing**
1. **System Admin** - Can access all schools and users
2. **School Admin** - Can manage users within their school only
3. **Teacher** - Can access student data within their school
4. **Student** - Can only access their own data
5. **Staff** - Department-specific access within their school

### **Permission Level Testing**
1. **Create/Edit Users** - Test with different admin levels
2. **View Reports** - Test access to analytics and reports
3. **Manage Roles** - Test role assignment capabilities
4. **System Settings** - Test system-level configuration access

### **Edge Case Testing**
1. **Inactive User** - Test `<EMAIL>` login (should fail)
2. **Multiple Roles** - Test `<EMAIL>` permissions
3. **Cross-School Access** - Verify users cannot access other schools' data

---

## 📊 **API Testing Examples**

### **Get JWT Token**
```bash
curl -X POST http://127.0.0.1:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

### **Test RBAC Endpoints**
```bash
# List roles (with JWT token)
curl -X GET http://127.0.0.1:8000/api/rbac/roles/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Get user permissions
curl -X GET http://127.0.0.1:8000/api/rbac/users/me/permissions/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# School-specific data
curl -X GET http://127.0.0.1:8000/api/v1/schools/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## 🔧 **Management Commands**

### **Recreate Test Environment**
```bash
# Clear and recreate all test users
python manage.py create_comprehensive_test_users --create-schools --clear-existing

# Create additional users without clearing
python manage.py create_comprehensive_test_users --create-schools
```

### **System Health Check**
```bash
# Check RBAC system health
python manage.py rbac_maintenance --task=generate_health_report

# Validate system integrity
python manage.py rbac_maintenance --task=validate_system_integrity
```

---

## 🔍 **Supabase Integration Status**

### **Current Status: PARTIALLY WORKING**
- ✅ **Supabase Management API**: Working (confirmed)
- ✅ **Supabase Database**: Active PostgreSQL 17.4 with existing data
- ❌ **Direct PostgreSQL Connection**: DNS resolution issue
- ✅ **Local Development**: Fully functional with complete test data

### **Supabase Verification**
1. **Dashboard Access**: https://supabase.com/dashboard/project/fsznynpmqxljaijcuweb
2. **Existing Data**: 10 users and 2 schools already in Supabase
3. **Tables Created**: All RBAC tables exist and are accessible

### **Connection Troubleshooting**
```bash
# Test connectivity and diagnose issues
python manage.py test_supabase_connection --verbose

# Check current database status
python manage.py dbshell  # Shows current database (SQLite)
```

### **Network Issue Details**
- **Problem**: DNS cannot resolve `db.fsznynpmqxljaijcuweb.supabase.co`
- **Cause**: Likely network/firewall restrictions
- **Solution**: System works perfectly locally; Supabase accessible via API
- **Impact**: No impact on RBAC functionality demonstration

---

## ⚠️ **Security Notes**

1. **Development Only**: All credentials are for development/testing
2. **Password Patterns**: Predictable for testing - change in production
3. **Multi-Tenant Isolation**: Verified and enforced at database level
4. **Access Control**: Role-based permissions properly implemented
5. **Audit Logging**: All actions are logged for security monitoring

---

## 🧪 **Quick Admin Access Test**

**To verify the fix works:**

1. **Open Admin Dashboard**: http://127.0.0.1:8000/admin/
2. **Login as School Admin**:
   - Email: `<EMAIL>`
   - Password: `Admin123!`
3. **Expected Result**: You should now see:
   - ✅ Users section (filtered to your school)
   - ✅ Schools section (your school only)
   - ✅ Courses section (your school's courses)
   - ✅ No "permission denied" message

**Test Different User Types:**
- **System Admin**: `<EMAIL>` / `admin123` (sees all data)
- **School Admin**: Any school admin (sees only their school's data)
- **Teacher**: Limited access to relevant sections

---

## 🎯 **Next Steps**

1. **Test Login**: Try logging in with different user types
2. **Explore Admin Dashboard**: Navigate through RBAC management features
3. **API Testing**: Use provided API examples to test endpoints
4. **Multi-Tenant Testing**: Verify data isolation between schools
5. **Role Testing**: Test different permission levels and access controls

---

## 📞 **Support**

- **Complete Documentation**: `RBAC_SYSTEM_DOCUMENTATION.md`
- **Quick Access Guide**: `QUICK_ACCESS_GUIDE.md`
- **System Health**: Run health check commands for status
- **Troubleshooting**: Check main documentation for common issues

---

**🎉 Ready for Comprehensive RBAC Testing!**

You now have a complete multi-tenant RBAC system with 144 test users across 6 schools, demonstrating all system capabilities and edge cases.
