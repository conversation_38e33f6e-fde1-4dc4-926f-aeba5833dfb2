"""
Tests for multi-tenant isolation functionality.
"""

import uuid
from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied, ValidationError
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from schools.models import School
from users.models import Role, UserRole, Permission
from users.multi_tenant_managers import SchoolContextValidator
from users.cross_school_utils import CrossSchoolRoleComparator

User = get_user_model()


class MultiTenantIsolationTestCase(TestCase):
    """
    Test case for multi-tenant isolation functionality.
    """
    
    def setUp(self):
        """Set up test data."""
        from datetime import date

        # Create schools
        self.school_a = School.objects.create(
            name="School A",
            slug="school-a",
            email="<EMAIL>",
            address_line_1="123 School A Street",
            city="City A",
            state_province="State A",
            postal_code="12345",
            country="Country A",
            academic_year_start=date(2024, 9, 1),
            academic_year_end=date(2025, 6, 30)
        )
        self.school_b = School.objects.create(
            name="School B",
            slug="school-b",
            email="<EMAIL>",
            address_line_1="456 School B Avenue",
            city="City B",
            state_province="State B",
            postal_code="67890",
            country="Country B",
            academic_year_start=date(2024, 9, 1),
            academic_year_end=date(2025, 6, 30)
        )
        
        # Create users
        self.super_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Super",
            last_name="Admin",
            user_type="super_admin",
            is_superuser=True,
            is_staff=True
        )
        
        self.school_a_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="School A",
            last_name="Admin",
            user_type="school_admin",
            school=self.school_a
        )
        
        self.school_b_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="School B",
            last_name="Admin",
            user_type="school_admin",
            school=self.school_b
        )
        
        self.school_a_teacher = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Teacher",
            last_name="A",
            user_type="lecturer",
            school=self.school_a
        )
        
        # Create permissions
        self.permission_1 = Permission.objects.create(
            codename="can_manage_courses",
            name="Can Manage Courses",
            category="academic"
        )

        self.permission_2 = Permission.objects.create(
            codename="can_assign_roles",
            name="Can Assign Roles",
            category="admin"
        )

        # Give school admins the permission to assign roles
        from django.contrib.auth.models import Permission as DjangoPermission
        assign_roles_perm, created = DjangoPermission.objects.get_or_create(
            codename='can_assign_roles',
            name='Can assign roles',
            content_type_id=1  # Generic content type
        )
        self.school_a_admin.user_permissions.add(assign_roles_perm)
        self.school_b_admin.user_permissions.add(assign_roles_perm)
        
        # Create roles
        self.role_a = Role.objects.create(
            name="Department Head",
            description="Head of Department role for School A",
            school=self.school_a,
            role_type="school",
            created_by=self.school_a_admin
        )
        
        self.role_b = Role.objects.create(
            name="Department Head",
            description="Head of Department role for School B",
            school=self.school_b,
            role_type="school",
            created_by=self.school_b_admin
        )
        
        self.system_role = Role.objects.create(
            name="System Administrator",
            description="System-wide administrator role",
            school=None,
            role_type="system",
            is_system_role=True,
            created_by=self.super_admin
        )
    
    def test_school_context_validator_user_access(self):
        """Test SchoolContextValidator for user access validation."""
        # Same school access should be allowed
        self.assertTrue(
            SchoolContextValidator.validate_user_school_access(
                self.school_a_admin, self.school_a_teacher
            )
        )
        
        # Different school access should be denied
        self.assertFalse(
            SchoolContextValidator.validate_user_school_access(
                self.school_a_admin, self.school_b_admin
            )
        )
        
        # Super admin should access any user
        self.assertTrue(
            SchoolContextValidator.validate_user_school_access(
                self.super_admin, self.school_a_teacher
            )
        )
        self.assertTrue(
            SchoolContextValidator.validate_user_school_access(
                self.super_admin, self.school_b_admin
            )
        )
    
    def test_school_context_validator_role_access(self):
        """Test SchoolContextValidator for role access validation."""
        # Same school role access should be allowed
        self.assertTrue(
            SchoolContextValidator.validate_role_school_access(
                self.school_a_admin, self.role_a
            )
        )
        
        # Different school role access should be denied
        self.assertFalse(
            SchoolContextValidator.validate_role_school_access(
                self.school_a_admin, self.role_b
            )
        )
        
        # System role access should be allowed for all users
        self.assertTrue(
            SchoolContextValidator.validate_role_school_access(
                self.school_a_admin, self.system_role
            )
        )
        self.assertTrue(
            SchoolContextValidator.validate_role_school_access(
                self.school_b_admin, self.system_role
            )
        )
        
        # Super admin should access any role
        self.assertTrue(
            SchoolContextValidator.validate_role_school_access(
                self.super_admin, self.role_a
            )
        )
        self.assertTrue(
            SchoolContextValidator.validate_role_school_access(
                self.super_admin, self.role_b
            )
        )
    
    def test_role_manager_school_filtering(self):
        """Test Role manager school-based filtering."""
        # School A admin should only see School A roles + system roles
        school_a_roles = Role.mt_objects.for_user_school(self.school_a_admin)
        role_schools = [role.school for role in school_a_roles]
        
        self.assertIn(self.school_a, role_schools)
        self.assertNotIn(self.school_b, role_schools)
        self.assertIn(None, role_schools)  # System roles
        
        # Super admin should see all roles
        all_roles = Role.mt_objects.for_user_school(self.super_admin)
        self.assertEqual(all_roles.count(), 3)  # 2 school roles + 1 system role
    
    def test_role_assignment_validation(self):
        """Test role assignment validation with multi-tenant constraints."""
        # Valid assignment within same school
        user_role = UserRole.mt_objects.assign_role(
            user=self.school_a_teacher,
            role=self.role_a,
            assigned_by=self.school_a_admin
        )
        self.assertIsNotNone(user_role)
        self.assertTrue(user_role.is_active)
        
        # Invalid assignment across schools should raise error
        with self.assertRaises(ValidationError):
            UserRole.mt_objects.assign_role(
                user=self.school_a_teacher,
                role=self.role_b,  # Role from different school
                assigned_by=self.school_a_admin
            )
        
        # System role assignment should work
        system_user_role = UserRole.mt_objects.assign_role(
            user=self.school_a_teacher,
            role=self.system_role,
            assigned_by=self.school_a_admin
        )
        self.assertIsNotNone(system_user_role)
    
    def test_role_creation_validation(self):
        """Test role creation with multi-tenant validation."""
        # Valid school role creation
        new_role = Role.mt_objects.create_school_role(
            name="Test Role",
            school=self.school_a,
            created_by=self.school_a_admin,
            description="Test role for School A"
        )
        self.assertEqual(new_role.school, self.school_a)
        self.assertEqual(new_role.role_type, 'school')
        
        # Invalid cross-school role creation should raise error
        with self.assertRaises(PermissionDenied):
            Role.mt_objects.create_school_role(
                name="Invalid Role",
                school=self.school_b,  # Different school
                created_by=self.school_a_admin,
                description="This should fail"
            )
        
        # System role creation by super admin
        system_role = Role.mt_objects.create_system_role(
            name="Test System Role",
            created_by=self.super_admin,
            description="Test system role"
        )
        self.assertIsNone(system_role.school)
        self.assertEqual(system_role.role_type, 'system')
        
        # System role creation by non-super admin should fail
        with self.assertRaises(PermissionDenied):
            Role.mt_objects.create_system_role(
                name="Invalid System Role",
                created_by=self.school_a_admin,
                description="This should fail"
            )
    
    def test_accessible_schools(self):
        """Test accessible schools functionality."""
        # Super admin should access all schools
        super_admin_schools = SchoolContextValidator.get_accessible_schools(self.super_admin)
        self.assertEqual(super_admin_schools.count(), 2)
        
        # School admin should only access their school
        school_a_admin_schools = SchoolContextValidator.get_accessible_schools(self.school_a_admin)
        self.assertEqual(school_a_admin_schools.count(), 1)
        self.assertEqual(school_a_admin_schools.first(), self.school_a)
        
        # User without school should access no schools
        user_no_school = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type="staff"
        )
        no_school_access = SchoolContextValidator.get_accessible_schools(user_no_school)
        self.assertEqual(no_school_access.count(), 0)


class CrossSchoolComparisonTestCase(TestCase):
    """
    Test case for cross-school role comparison functionality.
    """
    
    def setUp(self):
        """Set up test data for cross-school comparison."""
        # Create schools
        from datetime import date

        self.school_1 = School.objects.create(
            name="Test School 1",
            slug="test-school-1",
            email="<EMAIL>",
            address_line_1="123 Test Street 1",
            city="Test City 1",
            state_province="Test State 1",
            postal_code="11111",
            country="Test Country 1",
            academic_year_start=date(2024, 9, 1),
            academic_year_end=date(2025, 6, 30)
        )
        self.school_2 = School.objects.create(
            name="Test School 2",
            slug="test-school-2",
            email="<EMAIL>",
            address_line_1="456 Test Avenue 2",
            city="Test City 2",
            state_province="Test State 2",
            postal_code="22222",
            country="Test Country 2",
            academic_year_start=date(2024, 9, 1),
            academic_year_end=date(2025, 6, 30)
        )
        
        # Create super admin
        self.super_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            is_superuser=True,
            user_type="super_admin"
        )
        
        # Create regular admin
        self.regular_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type="school_admin",
            school=self.school_1
        )
        
        # Create permissions
        self.perm1 = Permission.objects.create(
            codename="test_permission_1",
            name="Test Permission 1",
            category="academic"
        )
        self.perm2 = Permission.objects.create(
            codename="test_permission_2", 
            name="Test Permission 2",
            category="admin"
        )
        
        # Create similar roles in both schools
        self.role_1_school_1 = Role.objects.create(
            name="Teacher",
            school=self.school_1,
            role_type="school"
        )
        self.role_1_school_2 = Role.objects.create(
            name="Teacher",
            school=self.school_2,
            role_type="school"
        )
        
        # Add different permissions to each role
        self.role_1_school_1.permissions.add(self.perm1)
        self.role_1_school_2.permissions.add(self.perm1, self.perm2)
    
    def test_super_admin_access_validation(self):
        """Test that only super admins can access cross-school functionality."""
        # Super admin should have access
        try:
            CrossSchoolRoleComparator.validate_super_admin_access(self.super_admin)
        except PermissionDenied:
            self.fail("Super admin should have access")
        
        # Regular admin should not have access
        with self.assertRaises(PermissionDenied):
            CrossSchoolRoleComparator.validate_super_admin_access(self.regular_admin)
    
    def test_role_distribution_by_school(self):
        """Test role distribution statistics across schools."""
        distribution = CrossSchoolRoleComparator.get_role_distribution_by_school(
            self.super_admin
        )
        
        self.assertIn('schools', distribution)
        self.assertIn('summary', distribution)
        self.assertEqual(len(distribution['schools']), 2)
        
        # Check that each school has correct role count
        for school_data in distribution['schools']:
            self.assertIn('school_name', school_data)
            self.assertIn('total_roles', school_data)
            self.assertIn('role_breakdown', school_data)
    
    def test_role_comparison_across_schools(self):
        """Test role comparison functionality."""
        comparison = CrossSchoolRoleComparator.compare_roles_across_schools(
            user=self.super_admin,
            role_names=['Teacher']
        )
        
        self.assertIn('comparison', comparison)
        self.assertIn('summary', comparison)
        
        # Should find Teacher role in both schools
        teacher_comparison = next(
            (item for item in comparison['comparison'] if item['role_name'] == 'Teacher'),
            None
        )
        self.assertIsNotNone(teacher_comparison)
        self.assertEqual(teacher_comparison['instance_count'], 2)
        self.assertTrue(teacher_comparison['permission_variance']['has_variance'])
    
    def test_permission_matrix(self):
        """Test permission matrix generation."""
        matrix = CrossSchoolRoleComparator.get_role_permission_matrix(
            user=self.super_admin,
            role_name='Teacher'
        )
        
        self.assertIn('permission_matrix', matrix)
        self.assertIn('summary', matrix)
        self.assertEqual(matrix['role_name'], 'Teacher')
        self.assertEqual(len(matrix['roles']), 2)
        
        # Check permission variance
        self.assertGreater(matrix['summary']['permission_variance'], 0)
    
    def test_standardization_suggestions(self):
        """Test role standardization suggestions."""
        suggestions = CrossSchoolRoleComparator.suggest_role_standardization(
            user=self.super_admin,
            role_name='Teacher'
        )
        
        self.assertIn('suggestions', suggestions)
        self.assertIn('standardization_needed', suggestions)
        self.assertTrue(suggestions['standardization_needed'])
        self.assertGreater(len(suggestions['suggestions']), 0)
