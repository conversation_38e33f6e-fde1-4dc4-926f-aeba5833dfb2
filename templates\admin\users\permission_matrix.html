{% extends "admin/base_site.html" %}
{% load static %}
{% load rbac_extras %}

{% block title %}{{ title }} | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
.permission-matrix {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.matrix-container {
    overflow-x: auto;
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.matrix-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.matrix-table th,
.matrix-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid #ddd;
    font-size: 12px;
}

.matrix-table th {
    background: #f5f5f5;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
}

.matrix-table th.role-header {
    background: #2196f3;
    color: white;
    writing-mode: vertical-rl;
    text-orientation: mixed;
    min-width: 40px;
    max-width: 40px;
}

.matrix-table th.permission-header {
    background: #4caf50;
    color: white;
    text-align: left;
    min-width: 200px;
    position: sticky;
    left: 0;
    z-index: 11;
}

.matrix-table td.role-name {
    background: #f9f9f9;
    font-weight: bold;
    text-align: left;
    position: sticky;
    left: 0;
    z-index: 9;
}

.permission-cell {
    width: 40px;
    height: 30px;
}

.permission-granted {
    background: #4caf50;
    color: white;
}

.permission-denied {
    background: #f5f5f5;
    color: #999;
}

.category-header {
    background: #ff9800 !important;
    color: white !important;
    font-weight: bold;
    text-align: center !important;
}

.category-section {
    border-top: 3px solid #ff9800;
}

.matrix-legend {
    display: flex;
    gap: 20px;
    margin: 20px 0;
    padding: 15px;
    background: #f5f5f5;
    border-radius: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.legend-granted {
    background: #4caf50;
}

.legend-denied {
    background: #f5f5f5;
    border: 1px solid #ddd;
}

.matrix-controls {
    margin: 20px 0;
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.toggle-button {
    padding: 8px 16px;
    background: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.toggle-button:hover {
    background: #1976d2;
}

.matrix-stats {
    display: flex;
    gap: 30px;
    margin: 20px 0;
    padding: 15px;
    background: #e3f2fd;
    border-radius: 8px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #2196f3;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.school-indicator {
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 2px;
    background: #e0e0e0;
    color: #666;
}

.system-role {
    background: #ff5722;
    color: white;
}

.school-role {
    background: #2196f3;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="permission-matrix">
    <h1>{{ title }}</h1>
    
    <div class="matrix-stats">
        <div class="stat-item">
            <div class="stat-number">{{ roles.count }}</div>
            <div class="stat-label">Roles</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ permissions.count }}</div>
            <div class="stat-label">Permissions</div>
        </div>
        <div class="stat-item">
            <div class="stat-number">{{ permission_categories.keys|length }}</div>
            <div class="stat-label">Categories</div>
        </div>
    </div>
    
    <div class="matrix-legend">
        <div class="legend-item">
            <div class="legend-color legend-granted"></div>
            <span>Permission Granted</span>
        </div>
        <div class="legend-item">
            <div class="legend-color legend-denied"></div>
            <span>Permission Not Granted</span>
        </div>
    </div>
    
    <div class="matrix-controls">
        <input type="text" id="roleFilter" class="filter-input" placeholder="Filter roles...">
        <input type="text" id="permissionFilter" class="filter-input" placeholder="Filter permissions...">
        <button class="toggle-button" onclick="toggleCompactView()">Toggle Compact View</button>
    </div>
    
    <div class="matrix-container">
        <table class="matrix-table" id="matrixTable">
            <thead>
                <tr>
                    <th class="permission-header">Permission</th>
                    {% for role in roles %}
                        <th class="role-header" title="{{ role.name }} ({{ role.school.name|default:'System' }})">
                            {{ role.name|truncatechars:15 }}
                            <div class="school-indicator {% if role.school %}school-role{% else %}system-role{% endif %}">
                                {{ role.school.name|default:'SYS'|truncatechars:8 }}
                            </div>
                        </th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for category, category_permissions in permission_categories.items %}
                    <tr class="category-section">
                        <td class="category-header" colspan="{{ roles.count|add:1 }}">
                            {{ category|upper }} ({{ category_permissions|length }} permissions)
                        </td>
                    </tr>
                    {% for permission in category_permissions %}
                        <tr class="permission-row" data-permission="{{ permission.name|lower }}">
                            <td class="role-name" title="{{ permission.description }}">
                                {{ permission.name }}
                                <div style="font-size: 10px; color: #666;">{{ permission.codename }}</div>
                            </td>
                            {% for role in roles %}
                                {% has_permission_in_role matrix role.id permission.id as has_perm %}
                                <td class="permission-cell {% if has_perm %}permission-granted{% else %}permission-denied{% endif %}"
                                    title="{% if has_perm %}{{ role.name }} has {{ permission.name }}{% else %}{{ role.name }} does not have {{ permission.name }}{% endif %}">
                                    {% if has_perm %}✓{% else %}✗{% endif %}
                                </td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<script>
// Filter functionality
document.getElementById('roleFilter').addEventListener('input', function() {
    filterMatrix();
});

document.getElementById('permissionFilter').addEventListener('input', function() {
    filterMatrix();
});

function filterMatrix() {
    const roleFilter = document.getElementById('roleFilter').value.toLowerCase();
    const permissionFilter = document.getElementById('permissionFilter').value.toLowerCase();
    const rows = document.querySelectorAll('.permission-row');
    
    rows.forEach(function(row) {
        const permissionName = row.dataset.permission;
        const showRow = permissionName.includes(permissionFilter);
        row.style.display = showRow ? '' : 'none';
    });
}

function toggleCompactView() {
    const table = document.getElementById('matrixTable');
    table.classList.toggle('compact-view');
}

// Add custom filter for template
document.addEventListener('DOMContentLoaded', function() {
    // Add lookup filter for nested dictionary access
    if (!window.templateFilters) {
        window.templateFilters = {};
    }
    
    window.templateFilters.lookup = function(dict, key) {
        return dict && dict[key] ? dict[key] : null;
    };
});
</script>
{% endblock %}
