#!/usr/bin/env python
"""
Test Django connection to Supabase PostgreSQL database.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_backend.settings')
django.setup()

from django.db import connection
from schools.models import School
from users.models import User
from courses.models import Course

def test_django_supabase_connection():
    """Test Django connection to Supabase"""
    print("=== Testing Django → Supabase Connection ===\n")
    
    try:
        # Test 1: Database connection
        print("--- Testing Database Connection ---")
        with connection.cursor() as cursor:
            cursor.execute("SELECT current_database(), current_user, version();")
            result = cursor.fetchone()
            print(f"✅ Connected to database: {result[0]}")
            print(f"✅ Connected as user: {result[1]}")
            print(f"✅ PostgreSQL version: {result[2][:50]}...")
        
        # Test 2: Query existing Supabase data through Django ORM
        print("\n--- Testing Django ORM with Supabase Data ---")
        
        # Query schools
        schools = School.objects.all()
        print(f"✅ Found {schools.count()} schools through Django ORM:")
        for school in schools:
            print(f"  - {school.name} ({school.slug}) - {school.subscription_plan}")
        
        # Query users
        users = User.objects.all()
        print(f"✅ Found {users.count()} users through Django ORM:")
        for user in users:
            school_name = user.school.name if user.school else 'No School'
            print(f"  - {user.email} ({user.user_type}) - {school_name}")
        
        # Query courses
        courses = Course.objects.all()
        print(f"✅ Found {courses.count()} courses through Django ORM:")
        for course in courses:
            print(f"  - {course.code}: {course.name} - {course.school.name}")
        
        # Test 3: Multi-tenant filtering
        print("\n--- Testing Multi-Tenant Filtering through Django ---")
        if schools.exists():
            greenwood = schools.filter(slug='greenwood-high').first()
            riverside = schools.filter(slug='riverside-academy').first()
            
            if greenwood:
                greenwood_users = User.objects.filter(school=greenwood)
                print(f"✅ Greenwood High School has {greenwood_users.count()} users")
                for user in greenwood_users:
                    print(f"    - {user.email} ({user.user_type})")
            
            if riverside:
                riverside_users = User.objects.filter(school=riverside)
                print(f"✅ Riverside Academy has {riverside_users.count()} users")
                for user in riverside_users:
                    print(f"    - {user.email} ({user.user_type})")
        
        # Test 4: Test Django migrations status
        print("\n--- Testing Django Migrations Status ---")
        from django.core.management import execute_from_command_line
        import io
        import contextlib
        
        # Capture migration status
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            try:
                execute_from_command_line(['manage.py', 'showmigrations', '--verbosity=0'])
                migration_output = f.getvalue()
                print("✅ Migration status retrieved")
                print("Migration status summary:")
                lines = migration_output.strip().split('\n')
                for line in lines[:10]:  # Show first 10 lines
                    if line.strip():
                        print(f"  {line}")
                if len(lines) > 10:
                    print(f"  ... and {len(lines) - 10} more migration entries")
            except Exception as e:
                print(f"⚠️  Could not get migration status: {e}")
        
        print("\n🎉 Django → Supabase connection successful!")
        print("✅ Django ORM can read existing Supabase data")
        print("✅ Multi-tenant filtering works correctly")
        print("✅ All models are properly connected")
        
        return True
        
    except Exception as e:
        print(f"❌ Django → Supabase connection failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_django_supabase_connection()
