"""
Template tags and filters for RBAC functionality.
"""

from django import template

register = template.Library()


@register.filter
def lookup(dictionary, key):
    """
    Template filter to lookup a value in a dictionary.
    Usage: {{ dict|lookup:key }}
    """
    if dictionary and key in dictionary:
        return dictionary[key]
    return None


@register.filter
def get_item(dictionary, key):
    """
    Alternative lookup filter for nested dictionaries.
    Usage: {{ dict|get_item:key }}
    """
    try:
        return dictionary.get(key)
    except (AttributeError, KeyError):
        return None


@register.simple_tag
def has_permission_in_role(matrix, role_id, permission_id):
    """
    Check if a role has a specific permission in the matrix.
    Usage: {% has_permission_in_role matrix role.id permission.id %}
    """
    try:
        role_perms = matrix.get(role_id, {})
        return role_perms.get(permission_id) is not None
    except (AttributeError, KeyError):
        return False


@register.filter
def role_permission_status(matrix, role_permission_key):
    """
    Get permission status for a role-permission combination.
    Usage: {{ matrix|role_permission_status:"role_id_permission_id" }}
    """
    try:
        role_id, permission_id = role_permission_key.split('_', 1)
        role_perms = matrix.get(role_id, {})
        return role_perms.get(permission_id) is not None
    except (AttributeError, ValueError, KeyError):
        return False


@register.inclusion_tag('admin/users/role_badge.html')
def role_badge(role):
    """
    Render a role badge with appropriate styling.
    Usage: {% role_badge role %}
    """
    return {'role': role}


@register.inclusion_tag('admin/users/permission_badge.html')
def permission_badge(permission):
    """
    Render a permission badge with category styling.
    Usage: {% permission_badge permission %}
    """
    return {'permission': permission}


@register.filter
def multiply(value, arg):
    """
    Multiply two values.
    Usage: {{ value|multiply:arg }}
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0


@register.filter
def percentage(value, total):
    """
    Calculate percentage.
    Usage: {{ value|percentage:total }}
    """
    try:
        if total == 0:
            return 0
        return round((float(value) / float(total)) * 100, 1)
    except (ValueError, TypeError, ZeroDivisionError):
        return 0


@register.simple_tag
def user_role_count(user, role_type=None):
    """
    Get count of roles for a user, optionally filtered by type.
    Usage: {% user_role_count user %} or {% user_role_count user "system" %}
    """
    try:
        user_roles = user.user_roles.filter(is_active=True)
        if role_type:
            user_roles = user_roles.filter(role__role_type=role_type)
        return user_roles.count()
    except AttributeError:
        return 0


@register.simple_tag
def role_user_count(role):
    """
    Get count of active users for a role.
    Usage: {% role_user_count role %}
    """
    try:
        return role.user_roles.filter(is_active=True).count()
    except AttributeError:
        return 0


@register.simple_tag
def role_permission_count(role, granted_only=True):
    """
    Get count of permissions for a role.
    Usage: {% role_permission_count role %} or {% role_permission_count role False %}
    """
    try:
        role_perms = role.role_permissions.all()
        if granted_only:
            role_perms = role_perms.filter(granted=True)
        return role_perms.count()
    except AttributeError:
        return 0


@register.filter
def format_audit_action(action_type):
    """
    Format audit action type for display.
    Usage: {{ log.action_type|format_audit_action }}
    """
    action_map = {
        'role_created': '🎭 Role Created',
        'role_updated': '✏️ Role Updated',
        'role_deleted': '🗑️ Role Deleted',
        'user_role_assigned': '👤 Role Assigned',
        'user_role_removed': '❌ Role Removed',
        'permission_granted': '✅ Permission Granted',
        'permission_revoked': '🚫 Permission Revoked',
        'role_approved': '✔️ Role Approved',
        'role_rejected': '❌ Role Rejected',
    }
    return action_map.get(action_type, action_type.replace('_', ' ').title())


@register.filter
def school_or_system(school):
    """
    Return school name or 'System' if None.
    Usage: {{ role.school|school_or_system }}
    """
    return school.name if school else 'System'


@register.simple_tag
def permission_category_color(category):
    """
    Get color for permission category.
    Usage: {% permission_category_color permission.category %}
    """
    color_map = {
        'academic': '#4caf50',
        'users': '#2196f3',
        'admin': '#ff9800',
        'system': '#f44336',
        'files': '#9c27b0',
        'reports': '#607d8b',
    }
    return color_map.get(category.lower(), '#666666')


@register.simple_tag
def role_type_color(role_type):
    """
    Get color for role type.
    Usage: {% role_type_color role.role_type %}
    """
    color_map = {
        'system': '#ff5722',
        'school': '#2196f3',
        'custom': '#4caf50',
    }
    return color_map.get(role_type.lower(), '#666666')
