#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create test data for the SMS system.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_backend.settings')
django.setup()

from schools.models import School
from users.models import User, Role
from courses.models import Course, Grade, CourseMaterial, Notification
from datetime import date, datetime, timedelta

def create_test_schools():
    """Create test schools"""
    print("Creating test schools...")
    
    # School 1: Greenwood High School
    school1, created = School.objects.get_or_create(
        name="Greenwood High School",
        defaults={
            'slug': 'greenwood-high',
            'email': '<EMAIL>',
            'phone': '******-0101',
            'address_line_1': '123 Education Street',
            'city': 'Springfield',
            'state_province': 'IL',
            'country': 'USA',
            'postal_code': '62701',
            'website': 'https://greenwood.edu',
            'subscription_plan': 'standard',
            'status': 'active',
            'academic_year_start': date(2024, 9, 1),
            'academic_year_end': date(2025, 6, 30),
            'created_by': 'system'
        }
    )
    
    # School 2: Riverside Academy
    school2, created = School.objects.get_or_create(
        name="Riverside Academy",
        defaults={
            'slug': 'riverside-academy',
            'email': '<EMAIL>',
            'phone': '******-0202',
            'address_line_1': '456 River Road',
            'city': 'Riverside',
            'state_province': 'CA',
            'country': 'USA',
            'postal_code': '92501',
            'website': 'https://riverside.edu',
            'subscription_plan': 'premium',
            'status': 'active',
            'academic_year_start': date(2024, 8, 15),
            'academic_year_end': date(2025, 6, 15),
            'created_by': 'system'
        }
    )
    
    print(f"Created schools: {school1.name}, {school2.name}")
    return school1, school2

def create_test_users(school1, school2):
    """Create test users for each school"""
    print("Creating test users...")
    
    # Greenwood High School users
    admin1, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'John',
            'last_name': 'Smith',
            'school': school1,
            'user_type': 'school_admin',
            'is_active': True,
            'phone': '******-0111'
        }
    )
    if created:
        admin1.set_password('admin123')
        admin1.save()
    
    teacher1, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Sarah',
            'last_name': 'Johnson',
            'school': school1,
            'user_type': 'lecturer',
            'is_active': True,
            'employee_id': 'GW001',
            'hire_date': date(2020, 8, 15),
            'department': 'Mathematics'
        }
    )
    if created:
        teacher1.set_password('teacher123')
        teacher1.save()
    
    student1, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Emily',
            'last_name': 'Davis',
            'school': school1,
            'user_type': 'student',
            'is_active': True,
            'student_id': 'GW2024001',
            'enrollment_date': date(2024, 9, 1),
            'date_of_birth': date(2006, 3, 15)
        }
    )
    if created:
        student1.set_password('student123')
        student1.save()
    
    # Riverside Academy users
    admin2, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Michael',
            'last_name': 'Brown',
            'school': school2,
            'user_type': 'school_admin',
            'is_active': True,
            'phone': '******-0222'
        }
    )
    if created:
        admin2.set_password('admin123')
        admin2.save()
    
    teacher2, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Lisa',
            'last_name': 'Wilson',
            'school': school2,
            'user_type': 'lecturer',
            'is_active': True,
            'employee_id': 'RA001',
            'hire_date': date(2019, 8, 20),
            'department': 'Science'
        }
    )
    if created:
        teacher2.set_password('teacher123')
        teacher2.save()
    
    student2, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Alex',
            'last_name': 'Martinez',
            'school': school2,
            'user_type': 'student',
            'is_active': True,
            'student_id': 'RA2024001',
            'enrollment_date': date(2024, 8, 15),
            'date_of_birth': date(2006, 7, 22)
        }
    )
    if created:
        student2.set_password('student123')
        student2.save()
    
    print(f"Created users for {school1.name}: {admin1.email}, {teacher1.email}, {student1.email}")
    print(f"Created users for {school2.name}: {admin2.email}, {teacher2.email}, {student2.email}")
    
    return {
        'school1': {'admin': admin1, 'teacher': teacher1, 'student': student1},
        'school2': {'admin': admin2, 'teacher': teacher2, 'student': student2}
    }

def create_test_courses(school1, school2, users):
    """Create test courses"""
    print("Creating test courses...")
    
    # Greenwood High School courses
    course1, created = Course.objects.get_or_create(
        code='MATH101',
        school=school1,
        defaults={
            'name': 'Algebra I',
            'description': 'Introduction to algebraic concepts and problem solving',
            'credits': 3,
            'department': 'Mathematics',
            'level': 'Grade 9',
            'semester': 'Fall 2024',
            'academic_year': '2024-2025',
            'start_date': date(2024, 9, 1),
            'end_date': date(2024, 12, 20),
            'is_active': True
        }
    )
    
    # Riverside Academy courses
    course2, created = Course.objects.get_or_create(
        code='SCI101',
        school=school2,
        defaults={
            'name': 'Biology I',
            'description': 'Introduction to biological sciences and life processes',
            'credits': 4,
            'department': 'Science',
            'level': 'Grade 9',
            'semester': 'Fall 2024',
            'academic_year': '2024-2025',
            'start_date': date(2024, 8, 15),
            'end_date': date(2024, 12, 15),
            'is_active': True
        }
    )
    
    print(f"Created courses: {course1.code} at {school1.name}, {course2.code} at {school2.name}")
    return course1, course2

def main():
    """Create all test data"""
    print("=== Creating SMS Test Data ===\n")
    
    # Create schools
    school1, school2 = create_test_schools()
    
    # Create users
    users = create_test_users(school1, school2)
    
    # Create courses
    course1, course2 = create_test_courses(school1, school2, users)
    
    print("\n=== Test Data Creation Complete ===")
    print(f"Schools: {School.objects.count()}")
    print(f"Users: {User.objects.count()}")
    print(f"Courses: {Course.objects.count()}")
    
    print("\n=== Test Login Credentials ===")
    print("Super Admin: <EMAIL> / admin123")
    print("Greenwood Admin: <EMAIL> / admin123")
    print("Riverside Admin: <EMAIL> / admin123")
    print("Teachers: <EMAIL>, <EMAIL> / teacher123")
    print("Students: <EMAIL>, <EMAIL> / student123")

if __name__ == '__main__':
    main()
