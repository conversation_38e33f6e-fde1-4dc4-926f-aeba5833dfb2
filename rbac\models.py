from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.core.validators import RegexValidator
from django.utils import timezone
import uuid


class Permission(models.Model):
    """
    Custom permission model for granular access control.
    Extends Django's built-in permission system with categories and descriptions.
    """

    PERMISSION_CATEGORIES = [
        ('academic', 'Academic Management'),
        ('users', 'User Management'),
        ('admin', 'Administrative'),
        ('system', 'System Management'),
        ('reports', 'Reports & Analytics'),
        ('communication', 'Communication'),
        ('files', 'File Management'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Permission identification
    codename = models.CharField(
        max_length=100,
        unique=True,
        validators=[RegexValidator(
            regex=r'^[a-z_]+$',
            message='Codename must contain only lowercase letters and underscores'
        )],
        help_text="Unique permission identifier (e.g., 'can_upload_materials')"
    )
    name = models.CharField(
        max_length=255,
        help_text="Human-readable permission name"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of what this permission allows"
    )

    # Permission categorization
    category = models.CharField(
        max_length=20,
        choices=PERMISSION_CATEGORIES,
        help_text="Permission category for organization"
    )

    # Content type for object-level permissions
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="Content type this permission applies to"
    )

    # System vs custom permissions
    is_system_permission = models.BooleanField(
        default=True,
        help_text="Whether this is a system-defined permission"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_permissions',
        help_text="User who created this permission"
    )

    class Meta:
        db_table = 'rbac_permissions'
        ordering = ['category', 'name']
        indexes = [
            models.Index(fields=['codename']),
            models.Index(fields=['category']),
            models.Index(fields=['content_type']),
            models.Index(fields=['is_system_permission']),
        ]

    def __str__(self):
        return f"{self.name} ({self.codename})"


class Role(models.Model):
    """
    Role model for grouping permissions and assigning to users.
    Supports both system-wide and school-specific roles.
    """

    ROLE_TYPES = [
        ('system', 'System Role'),
        ('school', 'School Role'),
        ('custom', 'Custom Role'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Role identification
    name = models.CharField(
        max_length=100,
        help_text="Role name (e.g., 'Head of Department')"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of the role"
    )

    # Multi-tenant support
    school = models.ForeignKey(
        'schools.School',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='roles',
        help_text="School this role belongs to (null for system roles)"
    )

    # Role classification
    role_type = models.CharField(
        max_length=10,
        choices=ROLE_TYPES,
        default='school',
        help_text="Type of role"
    )

    # Role hierarchy
    parent_role = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='child_roles',
        help_text="Parent role for hierarchy"
    )

    # Role status
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this role is active"
    )
    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is a default role for new users"
    )

    # Permissions
    permissions = models.ManyToManyField(
        Permission,
        through='RolePermission',
        related_name='roles',
        blank=True
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_roles',
        help_text="User who created this role"
    )

    class Meta:
        db_table = 'rbac_roles'
        ordering = ['school', 'name']
        indexes = [
            models.Index(fields=['school', 'name']),
            models.Index(fields=['role_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_default']),
        ]
        constraints = [
            # Ensure role name is unique within a school
            models.UniqueConstraint(
                fields=['school', 'name'],
                condition=models.Q(school__isnull=False),
                name='unique_role_name_per_school'
            ),
            # Ensure system role names are globally unique
            models.UniqueConstraint(
                fields=['name'],
                condition=models.Q(school__isnull=True),
                name='unique_system_role_name'
            ),
        ]

    def __str__(self):
        if self.school:
            return f"{self.name} ({self.school.name})"
        return f"{self.name} (System)"


class RolePermission(models.Model):
    """
    Through model for Role-Permission relationship with additional metadata.
    Allows for explicit permission grants/denies and audit trails.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relationship fields
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name='role_permissions'
    )
    permission = models.ForeignKey(
        Permission,
        on_delete=models.CASCADE,
        related_name='role_permissions'
    )

    # Permission state
    granted = models.BooleanField(
        default=True,
        help_text="Whether permission is granted (True) or denied (False)"
    )

    # Audit fields
    granted_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_role_permissions',
        help_text="User who granted/denied this permission"
    )
    granted_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Optional expiration
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this permission grant expires"
    )

    class Meta:
        db_table = 'rbac_role_permissions'
        unique_together = ['role', 'permission']
        indexes = [
            models.Index(fields=['role', 'granted']),
            models.Index(fields=['permission', 'granted']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        status = "granted" if self.granted else "denied"
        return f"{self.role.name} - {self.permission.name} ({status})"

    @property
    def is_expired(self):
        """Check if this permission grant has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False


class UserRole(models.Model):
    """
    User-Role assignment model with support for temporary roles and audit trails.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relationship fields
    user = models.ForeignKey(
        'users.User',
        on_delete=models.CASCADE,
        related_name='user_roles'
    )
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name='user_roles'
    )

    # Assignment metadata
    assigned_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_user_roles',
        help_text="User who assigned this role"
    )
    assigned_at = models.DateTimeField(auto_now_add=True)

    # Role status
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this role assignment is active"
    )

    # Temporary role support
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this role assignment expires"
    )

    # Approval workflow
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether this role assignment requires approval"
    )
    approved_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_user_roles',
        help_text="User who approved this role assignment"
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this role assignment was approved"
    )

    # Audit fields
    updated_at = models.DateTimeField(auto_now=True)
    deactivated_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='deactivated_user_roles',
        help_text="User who deactivated this role assignment"
    )
    deactivated_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this role assignment was deactivated"
    )

    class Meta:
        db_table = 'rbac_user_roles'
        unique_together = ['user', 'role']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['role', 'is_active']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['requires_approval', 'approved_at']),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.role.name}"

    @property
    def is_expired(self):
        """Check if this role assignment has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

    @property
    def is_pending_approval(self):
        """Check if this role assignment is pending approval"""
        return self.requires_approval and not self.approved_at


class RoleTemplate(models.Model):
    """
    Role template model for creating standardized roles across schools.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Template identification
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Template name (e.g., 'Standard Lecturer')"
    )
    description = models.TextField(
        help_text="Description of the role template"
    )

    # Template configuration
    permissions = models.ManyToManyField(
        Permission,
        related_name='role_templates',
        help_text="Permissions included in this template"
    )

    # Template metadata
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this template is active"
    )
    is_system_template = models.BooleanField(
        default=True,
        help_text="Whether this is a system-provided template"
    )

    # Usage tracking
    usage_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times this template has been used"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_role_templates',
        help_text="User who created this template"
    )

    class Meta:
        db_table = 'rbac_role_templates'
        ordering = ['name']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['is_system_template']),
            models.Index(fields=['usage_count']),
        ]

    def __str__(self):
        return self.name


class RoleAuditLog(models.Model):
    """
    Audit log for tracking role and permission changes.
    """

    ACTION_TYPES = [
        ('role_created', 'Role Created'),
        ('role_updated', 'Role Updated'),
        ('role_deleted', 'Role Deleted'),
        ('permission_granted', 'Permission Granted'),
        ('permission_revoked', 'Permission Revoked'),
        ('user_role_assigned', 'User Role Assigned'),
        ('user_role_removed', 'User Role Removed'),
        ('role_approved', 'Role Assignment Approved'),
        ('role_denied', 'Role Assignment Denied'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Action details
    action_type = models.CharField(
        max_length=20,
        choices=ACTION_TYPES,
        help_text="Type of action performed"
    )
    description = models.TextField(
        help_text="Detailed description of the action"
    )

    # Actor information
    performed_by = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='performed_role_actions',
        help_text="User who performed the action"
    )

    # Target information
    target_user = models.ForeignKey(
        'users.User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='role_audit_logs',
        help_text="User affected by the action"
    )
    target_role = models.ForeignKey(
        Role,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs',
        help_text="Role affected by the action"
    )
    target_permission = models.ForeignKey(
        Permission,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs',
        help_text="Permission affected by the action"
    )

    # Context information
    school = models.ForeignKey(
        'schools.School',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='role_audit_logs',
        help_text="School context for the action"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the user performing the action"
    )
    user_agent = models.TextField(
        blank=True,
        help_text="User agent of the client"
    )

    # Additional data
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional metadata about the action"
    )

    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'rbac_audit_logs'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['action_type', 'created_at']),
            models.Index(fields=['performed_by', 'created_at']),
            models.Index(fields=['target_user', 'created_at']),
            models.Index(fields=['school', 'created_at']),
        ]

    def __str__(self):
        return f"{self.action_type} by {self.performed_by} at {self.created_at}"
