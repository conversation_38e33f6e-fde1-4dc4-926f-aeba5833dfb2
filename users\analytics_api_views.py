"""
API views for advanced analytics and audit system.
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Count, Q, Avg, Sum, F
from django.utils import timezone
from datetime import timedelta, datetime
from django.contrib.auth import get_user_model
from django.core.cache import cache

from .models import RoleAuditLog, Role, UserRole, Permission, RoleTemplate
from .serializers import RoleAuditLogSerializer
from .decorators import RBACPermission
from .multi_tenant_managers import SchoolContextValidator

User = get_user_model()


class AnalyticsViewSet(viewsets.ViewSet):
    """
    ViewSet for advanced analytics and reporting.
    """
    permission_classes = [IsAuthenticated, RBACPermission('can_view_analytics')]

    @action(detail=False, methods=['get'])
    def dashboard_stats(self, request):
        """Get comprehensive dashboard statistics."""
        cache_key = f"analytics_dashboard_{request.user.id}"
        cached_data = cache.get(cache_key)
        
        if cached_data:
            return Response(cached_data)

        user = request.user
        now = timezone.now()
        last_30_days = now - timedelta(days=30)
        last_7_days = now - timedelta(days=7)

        # Base querysets with school filtering
        if user.is_superuser:
            roles_qs = Role.objects.all()
            users_qs = User.objects.all()
            audit_logs_qs = RoleAuditLog.objects.all()
            user_roles_qs = UserRole.objects.all()
        else:
            school = getattr(user, 'school', None)
            roles_qs = Role.objects.filter(Q(school=school) | Q(school__isnull=True))
            users_qs = User.objects.filter(Q(school=school) | Q(school__isnull=True))
            audit_logs_qs = RoleAuditLog.objects.filter(Q(school=school) | Q(school__isnull=True))
            user_roles_qs = UserRole.objects.filter(Q(role__school=school) | Q(role__school__isnull=True))

        # Core statistics
        stats = {
            'total_users': users_qs.filter(is_active=True).count(),
            'total_roles': roles_qs.filter(is_active=True).count(),
            'total_permissions': Permission.objects.count(),
            'active_assignments': user_roles_qs.filter(is_active=True).count(),
            'pending_approvals': user_roles_qs.filter(
                requires_approval=True,
                approved_at__isnull=True,
                is_active=False
            ).count(),
            'expired_assignments': user_roles_qs.filter(
                expires_at__lt=now,
                is_active=True
            ).count(),
        }

        # Activity trends
        activity_trends = {
            'last_7_days': audit_logs_qs.filter(created_at__gte=last_7_days).count(),
            'last_30_days': audit_logs_qs.filter(created_at__gte=last_30_days).count(),
            'role_assignments_7d': audit_logs_qs.filter(
                action_type='user_role_assigned',
                created_at__gte=last_7_days
            ).count(),
            'role_assignments_30d': audit_logs_qs.filter(
                action_type='user_role_assigned',
                created_at__gte=last_30_days
            ).count(),
        }

        # Role distribution
        role_distribution = roles_qs.filter(is_active=True).values('role_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # User type distribution
        user_type_distribution = users_qs.filter(is_active=True).values('user_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # Most active users (by audit log entries)
        most_active_users = audit_logs_qs.filter(
            created_at__gte=last_30_days
        ).values(
            'performed_by__email', 'performed_by__first_name', 'performed_by__last_name'
        ).annotate(
            activity_count=Count('id')
        ).order_by('-activity_count')[:10]

        # Most used roles
        most_used_roles = user_roles_qs.filter(is_active=True).values(
            'role__name', 'role__role_type'
        ).annotate(
            user_count=Count('user', distinct=True)
        ).order_by('-user_count')[:10]

        # Security alerts
        security_alerts = self._get_security_alerts(audit_logs_qs, last_7_days)

        dashboard_data = {
            'stats': stats,
            'activity_trends': activity_trends,
            'role_distribution': list(role_distribution),
            'user_type_distribution': list(user_type_distribution),
            'most_active_users': list(most_active_users),
            'most_used_roles': list(most_used_roles),
            'security_alerts': security_alerts,
            'generated_at': now.isoformat(),
        }

        # Cache for 5 minutes
        cache.set(cache_key, dashboard_data, 300)
        
        return Response(dashboard_data)

    @action(detail=False, methods=['get'])
    def role_usage_analytics(self, request):
        """Get detailed role usage analytics."""
        user = request.user
        
        # Base queryset with school filtering
        if user.is_superuser:
            roles_qs = Role.objects.all()
            user_roles_qs = UserRole.objects.all()
        else:
            school = getattr(user, 'school', None)
            roles_qs = Role.objects.filter(Q(school=school) | Q(school__isnull=True))
            user_roles_qs = UserRole.objects.filter(Q(role__school=school) | Q(role__school__isnull=True))

        # Role usage statistics
        role_stats = roles_qs.filter(is_active=True).annotate(
            active_users=Count('user_roles', filter=Q(user_roles__is_active=True)),
            total_assignments=Count('user_roles'),
            pending_assignments=Count('user_roles', filter=Q(
                user_roles__requires_approval=True,
                user_roles__approved_at__isnull=True
            )),
            permission_count=Count('role_permissions', filter=Q(role_permissions__granted=True))
        ).values(
            'id', 'name', 'role_type', 'description',
            'active_users', 'total_assignments', 'pending_assignments', 'permission_count'
        ).order_by('-active_users')

        # Role template usage
        template_stats = RoleTemplate.objects.filter(is_active=True).values(
            'id', 'name', 'usage_count', 'is_system_template'
        ).order_by('-usage_count')

        # Permission usage across roles
        permission_stats = Permission.objects.annotate(
            role_count=Count('role_permissions', filter=Q(role_permissions__granted=True)),
            user_count=Count('role_permissions__role__user_roles', filter=Q(
                role_permissions__granted=True,
                role_permissions__role__user_roles__is_active=True
            ), distinct=True)
        ).values(
            'id', 'name', 'codename', 'category', 'role_count', 'user_count'
        ).order_by('-role_count')

        return Response({
            'role_stats': list(role_stats),
            'template_stats': list(template_stats),
            'permission_stats': list(permission_stats),
        })

    @action(detail=False, methods=['get'])
    def security_analytics(self, request):
        """Get security-focused analytics."""
        user = request.user
        now = timezone.now()
        last_24_hours = now - timedelta(hours=24)
        last_7_days = now - timedelta(days=7)

        # Base queryset with school filtering
        if user.is_superuser:
            audit_logs_qs = RoleAuditLog.objects.all()
        else:
            school = getattr(user, 'school', None)
            audit_logs_qs = RoleAuditLog.objects.filter(Q(school=school) | Q(school__isnull=True))

        # Failed access attempts (if we track them)
        failed_attempts = audit_logs_qs.filter(
            action_type__in=['role_denied', 'permission_revoked'],
            created_at__gte=last_24_hours
        ).count()

        # Privilege escalations
        privilege_escalations = audit_logs_qs.filter(
            action_type='user_role_assigned',
            created_at__gte=last_7_days,
            target_role__role_type='system'
        ).count()

        # Unusual activity patterns
        unusual_activity = audit_logs_qs.filter(
            created_at__gte=last_24_hours
        ).values('performed_by').annotate(
            action_count=Count('id')
        ).filter(action_count__gt=50)  # More than 50 actions in 24h

        # Recent administrative actions
        admin_actions = audit_logs_qs.filter(
            action_type__in=['role_created', 'role_updated', 'role_deleted'],
            created_at__gte=last_7_days
        ).select_related('performed_by', 'target_role').order_by('-created_at')[:20]

        admin_actions_data = []
        for log in admin_actions:
            admin_actions_data.append({
                'action_type': log.action_type,
                'description': log.description,
                'performed_by': log.performed_by.email if log.performed_by else 'System',
                'target_role': log.target_role.name if log.target_role else None,
                'created_at': log.created_at.isoformat(),
                'ip_address': log.ip_address,
            })

        return Response({
            'failed_attempts_24h': failed_attempts,
            'privilege_escalations_7d': privilege_escalations,
            'unusual_activity_users': list(unusual_activity),
            'recent_admin_actions': admin_actions_data,
            'security_score': self._calculate_security_score(audit_logs_qs, last_7_days),
        })

    @action(detail=False, methods=['get'])
    def compliance_report(self, request):
        """Generate compliance report for audit purposes."""
        user = request.user
        
        # Only allow users with audit permissions
        if not user.is_superuser and not user.has_perm('users.can_view_audit_logs'):
            return Response(
                {'error': 'Insufficient permissions for compliance reporting'},
                status=status.HTTP_403_FORBIDDEN
            )

        report_date = request.query_params.get('date', timezone.now().date())
        if isinstance(report_date, str):
            report_date = datetime.strptime(report_date, '%Y-%m-%d').date()

        start_date = timezone.make_aware(datetime.combine(report_date, datetime.min.time()))
        end_date = start_date + timedelta(days=1)

        # Base queryset with school filtering
        if user.is_superuser:
            audit_logs_qs = RoleAuditLog.objects.all()
            user_roles_qs = UserRole.objects.all()
        else:
            school = getattr(user, 'school', None)
            audit_logs_qs = RoleAuditLog.objects.filter(Q(school=school) | Q(school__isnull=True))
            user_roles_qs = UserRole.objects.filter(Q(role__school=school) | Q(role__school__isnull=True))

        # Compliance metrics
        compliance_data = {
            'report_date': report_date.isoformat(),
            'total_audit_entries': audit_logs_qs.filter(
                created_at__gte=start_date,
                created_at__lt=end_date
            ).count(),
            'role_changes': audit_logs_qs.filter(
                action_type__in=['role_created', 'role_updated', 'role_deleted'],
                created_at__gte=start_date,
                created_at__lt=end_date
            ).count(),
            'permission_changes': audit_logs_qs.filter(
                action_type__in=['permission_granted', 'permission_revoked'],
                created_at__gte=start_date,
                created_at__lt=end_date
            ).count(),
            'user_role_assignments': audit_logs_qs.filter(
                action_type='user_role_assigned',
                created_at__gte=start_date,
                created_at__lt=end_date
            ).count(),
            'approval_actions': audit_logs_qs.filter(
                action_type__in=['role_approved', 'role_denied'],
                created_at__gte=start_date,
                created_at__lt=end_date
            ).count(),
            'active_users_with_roles': user_roles_qs.filter(
                is_active=True,
                user__is_active=True
            ).values('user').distinct().count(),
            'expired_assignments': user_roles_qs.filter(
                expires_at__lt=timezone.now(),
                is_active=True
            ).count(),
        }

        return Response(compliance_data)

    def _get_security_alerts(self, audit_logs_qs, since_date):
        """Generate security alerts based on audit log analysis."""
        alerts = []

        # Check for multiple failed role assignments
        failed_assignments = audit_logs_qs.filter(
            action_type='role_denied',
            created_at__gte=since_date
        ).values('performed_by').annotate(
            count=Count('id')
        ).filter(count__gte=5)

        for failed in failed_assignments:
            alerts.append({
                'type': 'multiple_failed_assignments',
                'severity': 'medium',
                'message': f'User has {failed["count"]} failed role assignments in the last 7 days',
                'user_id': failed['performed_by']
            })

        # Check for privilege escalations
        escalations = audit_logs_qs.filter(
            action_type='user_role_assigned',
            target_role__role_type='system',
            created_at__gte=since_date
        ).count()

        if escalations > 10:
            alerts.append({
                'type': 'high_privilege_escalations',
                'severity': 'high',
                'message': f'{escalations} system role assignments in the last 7 days',
                'count': escalations
            })

        # Check for unusual activity volumes
        high_activity_users = audit_logs_qs.filter(
            created_at__gte=since_date
        ).values('performed_by').annotate(
            count=Count('id')
        ).filter(count__gt=100)

        for user_activity in high_activity_users:
            alerts.append({
                'type': 'unusual_activity_volume',
                'severity': 'low',
                'message': f'User has {user_activity["count"]} audit log entries in the last 7 days',
                'user_id': user_activity['performed_by']
            })

        return alerts

    def _calculate_security_score(self, audit_logs_qs, since_date):
        """Calculate a security score based on recent activity."""
        base_score = 100

        # Deduct points for security issues
        failed_attempts = audit_logs_qs.filter(
            action_type='role_denied',
            created_at__gte=since_date
        ).count()
        
        privilege_escalations = audit_logs_qs.filter(
            action_type='user_role_assigned',
            target_role__role_type='system',
            created_at__gte=since_date
        ).count()

        # Scoring logic
        score = base_score
        score -= min(failed_attempts * 2, 30)  # Max 30 points for failed attempts
        score -= min(privilege_escalations * 5, 25)  # Max 25 points for escalations

        return max(score, 0)  # Ensure score doesn't go below 0
