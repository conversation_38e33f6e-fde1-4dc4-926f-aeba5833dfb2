from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

app_name = 'courses'

router = DefaultRouter()
router.register(r'', views.CourseViewSet, basename='course')
router.register(r'grades', views.GradeViewSet, basename='grade')
router.register(r'materials', views.CourseMaterialViewSet, basename='material')
router.register(r'notifications', views.NotificationViewSet, basename='notification')

urlpatterns = [
    path('', include(router.urls)),
]
