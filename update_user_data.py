#!/usr/bin/env python
"""
Update existing users with gender and NID data.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_backend.settings')
django.setup()

from users.models import User
import random

def update_user_data():
    """Update existing users with gender and NID data"""
    print("=== UPDATING USER DATA WITH GENDER AND NID ===\n")
    
    # Sample NID data (realistic format for different countries)
    sample_nids = {
        '<EMAIL>': '1234567890123',
        '<EMAIL>': '2345678901234', 
        '<EMAIL>': '3456789012345',
        '<EMAIL>': '4567890123456',
        '<EMAIL>': '5678901234567',
        '<EMAIL>': '6789012345678',
        '<EMAIL>': '7890123456789',
        '<EMAIL>': '8901234567890',
        '<EMAIL>': '9012345678901',
        '<EMAIL>': '0123456789012',
    }
    
    # Sample gender data
    sample_genders = {
        '<EMAIL>': 'prefer_not_to_say',
        '<EMAIL>': 'male',
        '<EMAIL>': 'female',
        '<EMAIL>': 'female',
        '<EMAIL>': 'male',
        '<EMAIL>': 'female',
        '<EMAIL>': 'male',
        '<EMAIL>': 'female',
        '<EMAIL>': 'male',
        '<EMAIL>': 'female',
    }
    
    users = User.objects.all()
    updated_count = 0
    
    print("Updating users with gender and NID data...")
    
    for user in users:
        updated = False
        
        # Update gender if not set
        if not user.gender and user.email in sample_genders:
            user.gender = sample_genders[user.email]
            updated = True
            print(f"  Updated gender for {user.get_full_name()}: {user.gender}")
        
        # Update NID if not set
        if not user.nid and user.email in sample_nids:
            user.nid = sample_nids[user.email]
            updated = True
            print(f"  Updated NID for {user.get_full_name()}: {user.nid}")
        
        if updated:
            user.save()
            updated_count += 1
    
    print(f"\n✅ Updated {updated_count} users with gender and NID data")
    
    # Show summary
    print("\n--- UPDATED USER DATA SUMMARY ---")
    for user in User.objects.all().order_by('school__name', 'user_type', 'last_name'):
        school_name = user.school.name if user.school else 'No School'
        gender_display = user.get_gender_display() if user.gender else 'Not specified'
        nid_display = user.nid if user.nid else 'Not provided'
        
        print(f"🏫 {school_name}")
        print(f"  👤 {user.get_full_name()} ({user.user_type})")
        print(f"     📧 {user.email}")
        print(f"     📋 Registration: {user.student_id or user.employee_id or 'N/A'}")
        print(f"     ⚧️  Gender: {gender_display}")
        print(f"     🆔 NID: {nid_display}")
        print()
    
    # Test NID uniqueness
    print("--- TESTING NID UNIQUENESS ---")
    nids = User.objects.filter(nid__isnull=False).exclude(nid='').values_list('nid', flat=True)
    unique_nids = set(nids)
    
    if len(nids) == len(unique_nids):
        print("✅ All NIDs are unique")
    else:
        print("❌ Duplicate NIDs found!")
        duplicates = [nid for nid in nids if list(nids).count(nid) > 1]
        print(f"Duplicates: {duplicates}")
    
    print(f"📊 Total users with NID: {len(nids)}")
    print(f"📊 Unique NIDs: {len(unique_nids)}")
    
    return True

if __name__ == '__main__':
    update_user_data()
