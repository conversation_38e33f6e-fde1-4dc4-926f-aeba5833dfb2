"""
Management command to create standard role templates for the RBAC system.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from users.models import RoleTemplate, User


class Command(BaseCommand):
    help = 'Create standard role templates for the RBAC system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing templates',
        )
        parser.add_argument(
            '--template',
            type=str,
            help='Create only specific template (e.g., "teacher", "student", "admin")',
        )

    def handle(self, *args, **options):
        force = options['force']
        specific_template = options['template']

        self.stdout.write(self.style.SUCCESS('Creating role templates...'))

        # Get or create system user for template creation
        system_user, created = User.objects.get_or_create(
            email='<EMAIL>',
            defaults={
                'first_name': 'System',
                'last_name': 'User',
                'user_type': 'system',
                'is_active': False,
                'is_staff': False,
            }
        )

        templates_to_create = self.get_template_definitions()

        if specific_template:
            if specific_template in templates_to_create:
                templates_to_create = {specific_template: templates_to_create[specific_template]}
            else:
                self.stdout.write(
                    self.style.ERROR(f'Template "{specific_template}" not found. Available: {", ".join(templates_to_create.keys())}')
                )
                return

        created_count = 0
        updated_count = 0

        with transaction.atomic():
            for template_name, template_data in templates_to_create.items():
                try:
                    # Check if template exists
                    existing_template = None
                    try:
                        existing_template = RoleTemplate.objects.get(name=template_name)
                    except RoleTemplate.DoesNotExist:
                        pass

                    if existing_template and not force:
                        self.stdout.write(
                            self.style.WARNING(f'Template "{template_name}" already exists. Use --force to recreate.')
                        )
                        continue

                    # Create or update template
                    if existing_template and force:
                        template = existing_template
                        template.description = template_data['description']
                        template.is_system_template = template_data.get('is_system_template', True)
                        template.is_active = template_data.get('is_active', True)
                        template.save()
                        updated_count += 1
                        action = 'Updated'
                    else:
                        template = RoleTemplate.objects.create(
                            name=template_name,
                            description=template_data['description'],
                            is_system_template=template_data.get('is_system_template', True),
                            is_active=template_data.get('is_active', True),
                            created_by=system_user
                        )
                        created_count += 1
                        action = 'Created'

                    # Clear existing permissions and add new ones
                    template.permissions.clear()

                    # Add permissions
                    permissions_added = 0
                    for perm_codename in template_data['permissions']:
                        try:
                            permission = Permission.objects.get(codename=perm_codename)
                            template.permissions.add(permission)
                            permissions_added += 1
                        except Permission.DoesNotExist:
                            self.stdout.write(
                                self.style.WARNING(f'Permission "{perm_codename}" not found for template "{template_name}"')
                            )

                    self.stdout.write(
                        self.style.SUCCESS(
                            f'{action} template "{template_name}" with {permissions_added} permissions'
                        )
                    )

                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error creating template "{template_name}": {str(e)}')
                    )

        self.stdout.write(
            self.style.SUCCESS(
                f'Template creation complete. Created: {created_count}, Updated: {updated_count}'
            )
        )

    def get_template_definitions(self):
        """Define standard role templates with their permissions."""
        return {
            'school_administrator': {
                'description': 'Full administrative access to school management functions',
                'is_system_template': True,
                'permissions': [
                    'can_manage_schools',
                    'can_manage_users',
                    'can_manage_roles',
                    'can_assign_roles',
                    'can_view_audit_logs',
                    'can_manage_role_templates',
                    'can_manage_workflows',
                    'can_view_analytics',
                    'can_manage_students',
                    'can_manage_teachers',
                    'can_manage_classes',
                    'can_manage_subjects',
                    'can_view_reports',
                    'can_manage_attendance',
                    'can_manage_grades',
                ]
            },
            'teacher': {
                'description': 'Standard teacher role with classroom management capabilities',
                'is_system_template': True,
                'permissions': [
                    'can_view_students',
                    'can_manage_attendance',
                    'can_manage_grades',
                    'can_view_classes',
                    'can_manage_assignments',
                    'can_view_reports',
                    'can_communicate_parents',
                ]
            },
            'student': {
                'description': 'Basic student role with limited access to academic information',
                'is_system_template': True,
                'permissions': [
                    'can_view_own_grades',
                    'can_view_own_attendance',
                    'can_view_assignments',
                    'can_submit_assignments',
                    'can_view_schedule',
                    'can_view_announcements',
                ]
            },
            'parent_guardian': {
                'description': 'Parent/Guardian role with access to child academic information',
                'is_system_template': True,
                'permissions': [
                    'can_view_child_grades',
                    'can_view_child_attendance',
                    'can_view_child_assignments',
                    'can_view_child_schedule',
                    'can_communicate_teachers',
                    'can_view_announcements',
                ]
            },
            'department_head': {
                'description': 'Department head with subject-specific management capabilities',
                'is_system_template': True,
                'permissions': [
                    'can_view_students',
                    'can_manage_attendance',
                    'can_manage_grades',
                    'can_view_classes',
                    'can_manage_assignments',
                    'can_view_reports',
                    'can_manage_teachers',
                    'can_manage_subjects',
                    'can_view_analytics',
                ]
            },
            'librarian': {
                'description': 'Library management role with book and resource access',
                'is_system_template': True,
                'permissions': [
                    'can_manage_library',
                    'can_view_students',
                    'can_view_teachers',
                    'can_manage_books',
                    'can_view_reports',
                ]
            },
            'counselor': {
                'description': 'School counselor with student guidance capabilities',
                'is_system_template': True,
                'permissions': [
                    'can_view_students',
                    'can_view_student_records',
                    'can_manage_counseling',
                    'can_view_reports',
                    'can_communicate_parents',
                ]
            },
            'finance_manager': {
                'description': 'Financial management role with fee and payment access',
                'is_system_template': True,
                'permissions': [
                    'can_manage_fees',
                    'can_view_payments',
                    'can_manage_financial_reports',
                    'can_view_students',
                    'can_communicate_parents',
                ]
            },
            'it_support': {
                'description': 'IT support role with system maintenance capabilities',
                'is_system_template': True,
                'permissions': [
                    'can_manage_system_settings',
                    'can_view_audit_logs',
                    'can_manage_users',
                    'can_view_analytics',
                    'can_manage_backups',
                ]
            },
            'guest_observer': {
                'description': 'Limited guest access for observers and inspectors',
                'is_system_template': True,
                'permissions': [
                    'can_view_classes',
                    'can_view_announcements',
                    'can_view_basic_reports',
                ]
            }
        }
