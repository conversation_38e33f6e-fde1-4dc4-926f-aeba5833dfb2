"""
Management command for RBAC system maintenance and testing.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from django.contrib.auth import get_user_model
from django.db.models import Count, Q
from datetime import timedelta

from users.models import Role<PERSON>uditLog, UserRole, Role, RoleTemplate
from users.audit_management import AuditManager, AuditRetentionPolicy
from users.rbac_utils import RBACManager

User = get_user_model()


class Command(BaseCommand):
    help = 'Perform RBAC system maintenance tasks'

    def add_arguments(self, parser):
        parser.add_argument(
            '--task',
            type=str,
            choices=[
                'cleanup_expired_roles',
                'apply_retention_policy',
                'validate_system_integrity',
                'generate_health_report',
                'test_system_performance',
                'all'
            ],
            default='all',
            help='Specific maintenance task to run'
        )
        
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )
        
        parser.add_argument(
            '--retention-days',
            type=int,
            default=365,
            help='Number of days to retain audit logs'
        )

    def handle(self, *args, **options):
        task = options['task']
        dry_run = options['dry_run']
        retention_days = options['retention_days']

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))

        self.stdout.write(self.style.SUCCESS(f'Starting RBAC maintenance task: {task}'))

        if task == 'all':
            self.cleanup_expired_roles(dry_run)
            self.apply_retention_policy(retention_days, dry_run)
            self.validate_system_integrity()
            self.generate_health_report()
            self.test_system_performance()
        elif task == 'cleanup_expired_roles':
            self.cleanup_expired_roles(dry_run)
        elif task == 'apply_retention_policy':
            self.apply_retention_policy(retention_days, dry_run)
        elif task == 'validate_system_integrity':
            self.validate_system_integrity()
        elif task == 'generate_health_report':
            self.generate_health_report()
        elif task == 'test_system_performance':
            self.test_system_performance()

        self.stdout.write(self.style.SUCCESS('RBAC maintenance completed'))

    def cleanup_expired_roles(self, dry_run=False):
        """Clean up expired role assignments."""
        self.stdout.write('Cleaning up expired role assignments...')
        
        now = timezone.now()
        expired_assignments = UserRole.objects.filter(
            expires_at__lt=now,
            is_active=True
        )
        
        count = expired_assignments.count()
        
        if count == 0:
            self.stdout.write('No expired role assignments found.')
            return
        
        self.stdout.write(f'Found {count} expired role assignments')
        
        if not dry_run:
            with transaction.atomic():
                for assignment in expired_assignments:
                    assignment.is_active = False
                    assignment.deactivated_at = now
                    assignment.save()
                    
                    # Create audit log
                    AuditManager.create_audit_log(
                        action_type='user_role_removed',
                        description=f'Role assignment expired: {assignment.role.name}',
                        target_user=assignment.user,
                        target_role=assignment.role,
                        school=assignment.role.school,
                        metadata={'reason': 'expired', 'expires_at': assignment.expires_at.isoformat()}
                    )
            
            self.stdout.write(self.style.SUCCESS(f'Deactivated {count} expired role assignments'))
        else:
            for assignment in expired_assignments:
                self.stdout.write(f'Would deactivate: {assignment.user.email} - {assignment.role.name}')

    def apply_retention_policy(self, retention_days, dry_run=False):
        """Apply audit log retention policy."""
        self.stdout.write(f'Applying retention policy ({retention_days} days)...')
        
        if dry_run:
            cutoff_date = timezone.now() - timedelta(days=retention_days)
            old_logs_count = RoleAuditLog.objects.filter(created_at__lt=cutoff_date).count()
            self.stdout.write(f'Would delete {old_logs_count} audit logs older than {retention_days} days')
        else:
            results = AuditRetentionPolicy.apply_retention_policy()
            self.stdout.write(
                self.style.SUCCESS(
                    f'Deleted {results["non_critical_deleted"]} non-critical and '
                    f'{results["critical_deleted"]} critical audit logs'
                )
            )

    def validate_system_integrity(self):
        """Validate RBAC system integrity."""
        self.stdout.write('Validating RBAC system integrity...')
        
        issues = []
        
        # Check for orphaned user roles
        orphaned_user_roles = UserRole.objects.filter(
            user__isnull=True
        ).count()
        if orphaned_user_roles > 0:
            issues.append(f'{orphaned_user_roles} orphaned user role assignments')
        
        # Check for roles without permissions
        roles_without_permissions = Role.objects.filter(
            is_active=True,
            role_permissions__isnull=True
        ).distinct().count()
        if roles_without_permissions > 0:
            issues.append(f'{roles_without_permissions} active roles without permissions')
        
        # Check for inactive users with active role assignments
        inactive_users_with_roles = UserRole.objects.filter(
            is_active=True,
            user__is_active=False
        ).count()
        if inactive_users_with_roles > 0:
            issues.append(f'{inactive_users_with_roles} inactive users with active role assignments')
        
        # Check for role templates without permissions
        empty_templates = RoleTemplate.objects.filter(
            is_active=True,
            permissions__isnull=True
        ).distinct().count()
        if empty_templates > 0:
            issues.append(f'{empty_templates} active role templates without permissions')
        
        # Check for duplicate role assignments
        duplicate_assignments = UserRole.objects.values(
            'user', 'role'
        ).annotate(
            count=Count('id')
        ).filter(count__gt=1, is_active=True)
        
        if duplicate_assignments.exists():
            issues.append(f'{duplicate_assignments.count()} users with duplicate role assignments')
        
        if issues:
            self.stdout.write(self.style.WARNING('System integrity issues found:'))
            for issue in issues:
                self.stdout.write(f'  - {issue}')
        else:
            self.stdout.write(self.style.SUCCESS('No system integrity issues found'))

    def generate_health_report(self):
        """Generate RBAC system health report."""
        self.stdout.write('Generating RBAC system health report...')
        
        # Basic statistics
        total_users = User.objects.filter(is_active=True).count()
        total_roles = Role.objects.filter(is_active=True).count()
        total_templates = RoleTemplate.objects.filter(is_active=True).count()
        active_assignments = UserRole.objects.filter(is_active=True).count()
        pending_approvals = UserRole.objects.filter(
            requires_approval=True,
            approved_at__isnull=True,
            is_active=False
        ).count()
        
        # Recent activity
        last_24h = timezone.now() - timedelta(hours=24)
        recent_audit_logs = RoleAuditLog.objects.filter(created_at__gte=last_24h).count()
        
        # Template usage
        most_used_template = RoleTemplate.objects.filter(
            is_active=True
        ).order_by('-usage_count').first()
        
        # Role distribution
        from django.db.models import Count
        role_distribution = Role.objects.filter(
            is_active=True
        ).values('role_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Generate report
        self.stdout.write('\n' + '='*50)
        self.stdout.write('RBAC SYSTEM HEALTH REPORT')
        self.stdout.write('='*50)
        self.stdout.write(f'Generated at: {timezone.now().strftime("%Y-%m-%d %H:%M:%S")}')
        self.stdout.write('')
        
        self.stdout.write('SYSTEM STATISTICS:')
        self.stdout.write(f'  Active Users: {total_users}')
        self.stdout.write(f'  Active Roles: {total_roles}')
        self.stdout.write(f'  Role Templates: {total_templates}')
        self.stdout.write(f'  Active Assignments: {active_assignments}')
        self.stdout.write(f'  Pending Approvals: {pending_approvals}')
        self.stdout.write('')
        
        self.stdout.write('ACTIVITY (Last 24 hours):')
        self.stdout.write(f'  Audit Log Entries: {recent_audit_logs}')
        self.stdout.write('')
        
        if most_used_template:
            self.stdout.write('TEMPLATE USAGE:')
            self.stdout.write(f'  Most Used: {most_used_template.name} ({most_used_template.usage_count} times)')
            self.stdout.write('')
        
        self.stdout.write('ROLE DISTRIBUTION:')
        for dist in role_distribution:
            self.stdout.write(f'  {dist["role_type"].title()}: {dist["count"]}')
        
        self.stdout.write('='*50)

    def test_system_performance(self):
        """Test RBAC system performance."""
        self.stdout.write('Testing RBAC system performance...')
        
        import time
        
        # Test permission check performance
        test_user = User.objects.filter(is_active=True).first()
        if test_user:
            start_time = time.time()
            for _ in range(100):
                RBACManager.user_has_permission(test_user, 'can_manage_roles')
            end_time = time.time()
            
            avg_time = (end_time - start_time) / 100 * 1000  # Convert to milliseconds
            self.stdout.write(f'Permission check average time: {avg_time:.2f}ms')
            
            if avg_time > 10:
                self.stdout.write(self.style.WARNING('Permission checks are slow (>10ms)'))
            else:
                self.stdout.write(self.style.SUCCESS('Permission checks are performing well'))
        
        # Test audit log query performance
        start_time = time.time()
        recent_logs = RoleAuditLog.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).select_related('performed_by', 'target_user', 'target_role')[:100]
        
        # Force evaluation
        list(recent_logs)
        end_time = time.time()
        
        query_time = (end_time - start_time) * 1000
        self.stdout.write(f'Audit log query time: {query_time:.2f}ms')
        
        if query_time > 100:
            self.stdout.write(self.style.WARNING('Audit log queries are slow (>100ms)'))
        else:
            self.stdout.write(self.style.SUCCESS('Audit log queries are performing well'))
        
        # Test role assignment performance
        test_role = Role.objects.filter(is_active=True).first()
        if test_role and test_user:
            start_time = time.time()
            
            # Create temporary assignment for testing
            assignment = RBACManager.assign_role_to_user(
                user=test_user,
                role=test_role,
                assigned_by=test_user
            )
            
            end_time = time.time()
            assignment_time = (end_time - start_time) * 1000
            
            # Clean up test assignment
            assignment.delete()
            
            self.stdout.write(f'Role assignment time: {assignment_time:.2f}ms')
            
            if assignment_time > 50:
                self.stdout.write(self.style.WARNING('Role assignments are slow (>50ms)'))
            else:
                self.stdout.write(self.style.SUCCESS('Role assignments are performing well'))
