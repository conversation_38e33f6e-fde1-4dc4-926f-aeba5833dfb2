#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create a superuser for the SMS system.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_backend.settings')
django.setup()

from users.models import User

def create_superuser():
    """Create a superuser if one doesn't exist"""
    email = '<EMAIL>'
    password = 'admin123'
    
    if User.objects.filter(email=email).exists():
        print(f"Superuser with email {email} already exists.")
        return
    
    try:
        user = User.objects.create_user(
            email=email,
            password=password,
            first_name='Super',
            last_name='Admin',
            user_type='super_admin',
            is_staff=True,
            is_superuser=True,
            is_active=True
        )
        print(f"Superuser created successfully!")
        print(f"Email: {email}")
        print(f"Password: {password}")
        print(f"User ID: {user.id}")
        
    except Exception as e:
        print(f"Error creating superuser: {e}")

if __name__ == '__main__':
    create_superuser()
