# SMS Project - Complete RBAC System Documentation

## Overview

The SMS (School Management System) project features a comprehensive Role-Based Access Control (RBAC) system with multi-tenant support, advanced analytics, audit trails, and workflow management. This documentation covers the complete implementation including all features, APIs, and usage guidelines.

## Table of Contents

1. [Quick Start & Access](#quick-start--access)
2. [System Architecture](#system-architecture)
3. [Core Components](#core-components)
4. [Multi-Tenant Isolation](#multi-tenant-isolation)
5. [Role Templates and Workflows](#role-templates-and-workflows)
6. [Analytics and Audit System](#analytics-and-audit-system)
7. [API Documentation](#api-documentation)
8. [Testing and Maintenance](#testing-and-maintenance)
9. [Security Features](#security-features)
10. [Performance Considerations](#performance-considerations)
11. [Troubleshooting](#troubleshooting)

## Quick Start & Access

### 🚀 Getting Started

To access the SMS RBAC system, you'll need to start the Django development server and access the admin dashboard.

#### 1. Start the Development Server

```bash
# Navigate to project directory
cd sms-project

# Activate virtual environment (if using one)
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Start the Django development server
python manage.py runserver
```

#### 2. Access Points & URLs

| **Service** | **URL** | **Description** |
|-------------|---------|-----------------|
| **Admin Dashboard** | `http://127.0.0.1:8000/admin/` | Complete RBAC management interface |
| **API Root** | `http://127.0.0.1:8000/api/` | REST API endpoints |
| **RBAC API** | `http://127.0.0.1:8000/api/rbac/` | RBAC-specific API endpoints |
| **User Management** | `http://127.0.0.1:8000/api/v1/users/` | User management API |
| **Schools API** | `http://127.0.0.1:8000/api/v1/schools/` | School management API |

#### 3. Default Login Credentials

**Super Administrator Account:**
```
Email: <EMAIL>
Password: admin123
Role: System Administrator
Access Level: Full system access across all schools
```

**Test School Administrator:**
```
Email: <EMAIL>
Password: school123
Role: School Administrator
Access Level: Full access within assigned school
```

**Test Teacher Account:**
```
Email: <EMAIL>
Password: teacher123
Role: Teacher
Access Level: Student and class management within school
```

**Test Student Account:**
```
Email: <EMAIL>
Password: student123
Role: Student
Access Level: Student portal access within school
```

> **⚠️ Security Note:** These are default development credentials. **Change these immediately** in production environments.

#### 🔧 Creating Additional Users

To create more test users or recreate existing ones:

```bash
# Create all default users with test schools
python manage.py create_default_users --create-schools --force

# Create users without schools (uses existing schools)
python manage.py create_default_users --force

# Create users without forcing (skips existing users)
python manage.py create_default_users
```

#### 4. Creating Your First Login

If the default accounts don't exist, create a superuser:

```bash
# Create superuser account
python manage.py createsuperuser

# Follow the prompts to set:
# - Email address
# - First name
# - Last name
# - Password
```

#### 5. Admin Dashboard Features

Once logged in to the admin dashboard (`/admin/`), you'll have access to:

**📊 RBAC Management Sections:**
- **Users** - User account management with role assignments
- **Roles** - Role creation and permission management
- **Role Templates** - Standardized role templates
- **Permissions** - System permission management
- **User Roles** - Role assignment tracking with approval workflows
- **Audit Logs** - Comprehensive activity tracking
- **Schools** - Multi-tenant school management

**🎛️ Enhanced Admin Features:**
- **Role Hierarchy Visualization** - Interactive role structure display
- **Permission Matrix** - Visual permission assignment grid
- **Bulk Operations** - Mass role assignments and updates
- **Analytics Dashboard** - Real-time usage statistics
- **Security Monitoring** - Alert management and security metrics
- **Workflow Management** - Approval queue and temporary roles

#### 6. API Authentication

For API access, you'll need to authenticate using JWT tokens:

```bash
# Get JWT token
curl -X POST http://127.0.0.1:8000/api/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'

# Use token in subsequent requests
curl -X GET http://127.0.0.1:8000/api/rbac/roles/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 7. Quick Setup Commands

```bash
# Apply database migrations
python manage.py migrate

# Create standard role templates
python manage.py create_role_templates

# Run system health check
python manage.py rbac_maintenance --task=generate_health_report

# Load sample data (if available)
python manage.py loaddata sample_schools sample_users
```

#### 8. Development vs Production URLs

**Development (Local):**
- Admin: `http://127.0.0.1:8000/admin/`
- API: `http://127.0.0.1:8000/api/`

**Production (Update these for your deployment):**
- Admin: `https://yourdomain.com/admin/`
- API: `https://yourdomain.com/api/`

> **📝 Note:** Replace `yourdomain.com` with your actual production domain.

#### 9. Mobile App Integration

For React Native mobile app integration:

```javascript
// API Base URL Configuration
const API_BASE_URL = __DEV__
  ? 'http://127.0.0.1:8000/api/'
  : 'https://yourdomain.com/api/';

// Authentication endpoint
const AUTH_ENDPOINT = `${API_BASE_URL}auth/login/`;

// RBAC endpoints
const RBAC_ENDPOINTS = {
  roles: `${API_BASE_URL}rbac/roles/`,
  permissions: `${API_BASE_URL}rbac/permissions/`,
  analytics: `${API_BASE_URL}rbac/analytics/`,
};
```

#### 10. First Steps After Login

1. **Verify System Health** - Check the dashboard for any issues
2. **Review Role Templates** - Ensure standard templates are created
3. **Set Up Schools** - Create your school organizations
4. **Create User Accounts** - Add teachers, students, and staff
5. **Assign Roles** - Apply appropriate roles to users
6. **Test Permissions** - Verify access controls work correctly
7. **Configure Workflows** - Set up approval processes if needed

---

## System Architecture

### Core Models

The RBAC system is built around several key models:

- **User**: Extended Django user model with school association
- **Role**: Defines roles with permissions and school context
- **Permission**: Django permissions with categorization
- **UserRole**: Links users to roles with approval workflow
- **RoleTemplate**: Standardized role definitions for quick creation
- **RoleAuditLog**: Comprehensive audit trail for all RBAC actions

### Multi-Tenant Design

The system implements school-based multi-tenancy where:
- Each school operates as an isolated tenant
- Users can only access data within their school context
- Super admins can access cross-school data
- Role assignments are validated against school boundaries

## Core Components

### 1. Role Management (`users/rbac_utils.py`)

The `RBACManager` class provides centralized role management:

```python
# Create role from template
role = RBACManager.create_role_from_template(
    template_name='teacher',
    role_name='Math Teacher',
    school=school,
    created_by=admin_user
)

# Assign role to user
user_role = RBACManager.assign_role_to_user(
    user=teacher,
    role=role,
    assigned_by=admin_user,
    requires_approval=True
)

# Check permissions
has_permission = RBACManager.user_has_permission(user, 'can_manage_students')
```

### 2. Multi-Tenant Managers (`users/multi_tenant_managers.py`)

Custom managers ensure data isolation:

```python
# School-specific role queryset
school_roles = Role.objects.for_school(school)

# Validate cross-school access
is_valid = SchoolContextValidator.validate_user_school_access(user, school)
```

### 3. Enhanced Admin Interface (`users/admin.py`)

Features include:
- Visual role hierarchy display
- Permission matrix views
- Bulk operations
- Advanced filtering and search
- Real-time statistics dashboard

## Multi-Tenant Isolation

### School Context Validation

All RBAC operations validate school context:

```python
# Automatic validation in role assignment
def assign_role_to_user(user, role, assigned_by, **kwargs):
    # Validates that user and role belong to same school
    if not SchoolContextValidator.validate_role_assignment(user, role):
        raise ValueError("Cross-school role assignment not allowed")
```

### Data Filtering

Querysets automatically filter by school context:

```python
class SchoolFilteredRoleManager(models.Manager):
    def for_school(self, school):
        return self.filter(Q(school=school) | Q(school__isnull=True))
```

## Role Templates and Workflows

### Template System

Role templates provide standardized role creation:

```python
# Create standard templates
python manage.py create_role_templates

# Available templates:
# - school_administrator
# - teacher
# - student
# - parent_guardian
# - department_head
# - librarian
# - counselor
# - finance_manager
# - it_support
# - guest_observer
```

### Approval Workflows

Role assignments can require approval:

```python
# Assign role with approval requirement
user_role = RBACManager.assign_role_to_user(
    user=user,
    role=role,
    assigned_by=admin,
    requires_approval=True
)

# Approve assignment
RBACManager.approve_role_assignment(user_role, approver)
```

### Temporary Roles

Roles can have expiration dates:

```python
# Assign temporary role
user_role = RBACManager.assign_role_to_user(
    user=user,
    role=role,
    assigned_by=admin,
    expires_at=timezone.now() + timedelta(days=30)
)
```

## Analytics and Audit System

### Comprehensive Audit Logging

All RBAC actions are automatically logged:

```python
# Audit log creation
AuditManager.create_audit_log(
    action_type='user_role_assigned',
    description='Role assigned to user',
    performed_by=admin,
    target_user=user,
    target_role=role,
    school=school,
    ip_address=request.META.get('REMOTE_ADDR'),
    metadata={'additional_info': 'value'}
)
```

### Analytics Dashboard

Real-time analytics provide insights:

- User activity trends
- Role usage statistics
- Permission distribution
- Security alerts
- Compliance metrics

### Security Monitoring

Automated security alerts for:
- Multiple failed role assignments
- Privilege escalations
- Unusual activity patterns
- Cross-school access attempts

## API Documentation

### Role Template API

```bash
# List templates
GET /api/rbac/role-templates/

# Create role from template
POST /api/rbac/role-templates/{id}/create_role/
{
    "role_name": "Math Teacher",
    "school_id": "uuid",
    "description": "Mathematics department teacher"
}

# Validate template
POST /api/rbac/role-templates/{id}/validate_template/

# Bulk create roles
POST /api/rbac/role-templates/{id}/bulk_create_roles/
{
    "roles": [
        {"role_name": "Teacher 1", "school_id": "uuid1"},
        {"role_name": "Teacher 2", "school_id": "uuid2"}
    ]
}
```

### Workflow API

```bash
# Get pending approvals
GET /api/rbac/workflows/pending_approvals/

# Bulk approve assignments
POST /api/rbac/workflows/bulk_approve/
{
    "assignment_ids": ["uuid1", "uuid2", "uuid3"]
}

# Workflow statistics
GET /api/rbac/workflows/workflow_statistics/

# Cleanup expired roles
POST /api/rbac/workflows/cleanup_expired_roles/
```

### Analytics API

```bash
# Dashboard statistics
GET /api/rbac/analytics/dashboard_stats/

# Role usage analytics
GET /api/rbac/analytics/role_usage_analytics/

# Security analytics
GET /api/rbac/analytics/security_analytics/

# Compliance report
GET /api/rbac/analytics/compliance_report/?date=2024-01-01
```

## Testing and Maintenance

### Running Tests

```bash
# Run complete RBAC test suite
python manage.py test users.tests.test_rbac_complete_system

# Run multi-tenant isolation tests
python manage.py test users.tests.test_multi_tenant_isolation

# Run performance tests
python manage.py test users.tests.test_rbac_complete_system.RBACPerformanceTest
```

### Maintenance Commands

```bash
# Complete maintenance
python manage.py rbac_maintenance --task=all

# Cleanup expired roles
python manage.py rbac_maintenance --task=cleanup_expired_roles

# Apply retention policy
python manage.py rbac_maintenance --task=apply_retention_policy --retention-days=365

# System integrity check
python manage.py rbac_maintenance --task=validate_system_integrity

# Health report
python manage.py rbac_maintenance --task=generate_health_report

# Performance test
python manage.py rbac_maintenance --task=test_system_performance

# Dry run mode
python manage.py rbac_maintenance --task=all --dry-run
```

### Creating Role Templates

```bash
# Create all standard templates
python manage.py create_role_templates

# Create specific template
python manage.py create_role_templates --template=teacher

# Force recreation
python manage.py create_role_templates --force
```

## Security Features

### Permission Validation

- All API endpoints protected with RBAC permissions
- Multi-layered permission checking
- School context validation
- JWT token enhancement with RBAC claims

### Audit Trail

- Immutable audit logs
- IP address and user agent tracking
- Metadata storage for additional context
- Retention policies for compliance

### Security Alerts

- Real-time monitoring of suspicious activities
- Automated alert generation
- Email notifications for critical events
- Security score calculation

## Performance Considerations

### Database Optimization

- Proper indexing on frequently queried fields
- Select_related and prefetch_related usage
- Efficient queryset design
- Connection pooling

### Caching Strategy

- Permission caching for frequent checks
- Analytics data caching
- Template usage caching
- Redis-based session storage

### Query Optimization

```python
# Efficient role permission checking
def user_has_permission(user, permission_codename):
    return user.user_roles.filter(
        is_active=True,
        role__role_permissions__permission__codename=permission_codename,
        role__role_permissions__granted=True
    ).exists()
```

## Troubleshooting

### Common Issues

1. **Cross-school access denied**
   - Verify user and target resource belong to same school
   - Check super admin permissions for cross-school access

2. **Permission denied errors**
   - Verify user has required role assignments
   - Check role permissions are properly granted
   - Ensure role assignments are active and not expired

3. **Slow permission checks**
   - Review database indexes
   - Check for N+1 query problems
   - Consider permission caching

4. **Audit log growth**
   - Apply retention policies regularly
   - Monitor disk space usage
   - Consider log archiving strategies

### Debug Commands

```bash
# Check user permissions
python manage.py shell
>>> from users.rbac_utils import RBACManager
>>> RBACManager.get_user_permissions(user)

# Validate system integrity
python manage.py rbac_maintenance --task=validate_system_integrity

# Performance testing
python manage.py rbac_maintenance --task=test_system_performance
```

## Conclusion

The SMS RBAC system provides enterprise-grade access control with comprehensive features for multi-tenant environments. The system is designed for scalability, security, and maintainability while providing rich analytics and audit capabilities.

For additional support or feature requests, please refer to the project repository or contact the development team.
