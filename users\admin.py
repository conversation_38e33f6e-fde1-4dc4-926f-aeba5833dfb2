from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.db.models import Count, Q
from django.db import models
from .models import (
    User, Permission, Role, RolePermission, UserRole,
    RoleTemplate, RoleAuditLog
)
from .rbac_utils import RBACManager


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for User model with RBAC features.
    """
    list_display = [
        'email', 'first_name', 'last_name', 'user_type',
        'school', 'is_active', 'role_count', 'last_login'
    ]
    list_filter = [
        'user_type', 'is_active', 'is_staff', 'school',
        'created_at', 'last_login'
    ]
    search_fields = ['email', 'first_name', 'last_name', 'student_id', 'nid']
    readonly_fields = [
        'created_at', 'last_login', 'role_count',
        'permission_summary', 'account_status'
    ]

    fieldsets = (
        ('Personal Information', {
            'fields': ('email', 'first_name', 'last_name', 'gender', 'nid')
        }),
        ('School Information', {
            'fields': ('school', 'user_type', 'student_id')
        }),
        ('Account Status', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'account_status')
        }),
        ('Security', {
            'fields': ('password', 'failed_login_attempts', 'account_locked_until'),
            'classes': ('collapse',)
        }),
        ('RBAC Information', {
            'fields': ('role_count', 'permission_summary'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'last_login'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Filter users based on user's school context and permissions."""
        queryset = super().get_queryset(request).select_related('school').annotate(
            role_count=Count('user_roles', filter=models.Q(user_roles__is_active=True))
        )

        # Super users can see all users
        if request.user.is_superuser:
            return queryset

        # School admins can only see users in their school
        if request.user.school:
            return queryset.filter(school=request.user.school)

        # Users with system-level permissions can see all users
        from .rbac_utils import RBACManager
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return queryset

        # Default: no access
        return queryset.none()

    def has_view_permission(self, request, obj=None):
        """Check if user can view users."""
        if request.user.is_superuser:
            return True

        from .rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school and RBACManager.user_has_permission(request.user, 'can_manage_users', school=request.user.school):
            return True

        return False

    def has_change_permission(self, request, obj=None):
        """Check if user can change users."""
        if request.user.is_superuser:
            return True

        from .rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school:
            if RBACManager.user_has_permission(request.user, 'can_manage_users', school=request.user.school):
                # If editing specific user, ensure it's in the same school
                if obj and obj.school != request.user.school:
                    return False
                return True

        return False

    def has_add_permission(self, request):
        """Check if user can add users."""
        if request.user.is_superuser:
            return True

        from .rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school and RBACManager.user_has_permission(request.user, 'can_manage_users', school=request.user.school):
            return True

        return False

    def has_delete_permission(self, request, obj=None):
        """Check if user can delete users."""
        if request.user.is_superuser:
            return True

        from .rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school:
            if RBACManager.user_has_permission(request.user, 'can_manage_users', school=request.user.school):
                # If deleting specific user, ensure it's in the same school
                if obj and obj.school != request.user.school:
                    return False
                return True

        return False

    def role_count(self, obj):
        """Display number of active roles assigned to this user."""
        count = getattr(obj, 'role_count', obj.user_roles.filter(is_active=True).count())
        if count > 0:
            return format_html(
                '<a href="{}?user__id__exact={}" style="color: blue; font-weight: bold;">{}</a>',
                reverse('admin:users_userrole_changelist'),
                obj.id,
                count
            )
        return format_html('<span style="color: gray;">0</span>')
    role_count.short_description = 'Active Roles'
    role_count.admin_order_field = 'role_count'

    def permission_summary(self, obj):
        """Display a summary of user's permissions."""
        active_roles = obj.user_roles.filter(is_active=True).select_related('role')
        if not active_roles.exists():
            return format_html('<span style="color: gray;">No active roles</span>')

        role_names = [ur.role.name for ur in active_roles[:3]]
        if active_roles.count() > 3:
            role_names.append(f"... and {active_roles.count() - 3} more")

        return format_html(
            '<span style="color: green;">{}</span>',
            ', '.join(role_names)
        )
    permission_summary.short_description = 'Role Summary'

    def account_status(self, obj):
        """Display account status with color coding."""
        if not obj.is_active:
            return format_html('<span style="color: red;">Inactive</span>')
        elif obj.is_account_locked:
            return format_html('<span style="color: orange;">Locked</span>')
        else:
            return format_html('<span style="color: green;">Active</span>')
    account_status.short_description = 'Status'


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    """
    Admin interface for Permission model.
    """
    list_display = [
        'name', 'codename', 'category', 'content_type',
        'is_system_permission', 'role_count', 'created_at'
    ]
    list_filter = [
        'category', 'is_system_permission', 'content_type', 'created_at'
    ]
    search_fields = ['name', 'codename', 'description']
    readonly_fields = ['created_at', 'updated_at', 'role_count']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'codename', 'description')
        }),
        ('Classification', {
            'fields': ('category', 'content_type', 'is_system_permission')
        }),
        ('Usage Statistics', {
            'fields': ('role_count',),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'content_type', 'created_by'
        ).annotate(
            role_count=Count('role_permissions', filter=models.Q(role_permissions__granted=True))
        )

    def role_count(self, obj):
        """Display number of roles using this permission."""
        count = getattr(obj, 'role_count', 0)
        if count > 0:
            return format_html(
                '<a href="{}?permission__id__exact={}" style="color: blue; font-weight: bold;">{}</a>',
                reverse('admin:users_rolepermission_changelist'),
                obj.id,
                count
            )
        return format_html('<span style="color: gray;">0</span>')
    role_count.short_description = 'Roles Using'
    role_count.admin_order_field = 'role_count'


class RolePermissionInline(admin.TabularInline):
    """
    Inline admin for RolePermission model.
    """
    model = RolePermission
    extra = 0
    fields = ['permission', 'granted', 'expires_at', 'granted_by']
    readonly_fields = ['granted_by', 'granted_at']
    autocomplete_fields = ['permission']


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """
    Admin interface for Role model with enhanced features.
    """
    list_display = [
        'name', 'school', 'role_type', 'is_active',
        'is_default', 'permission_count', 'user_count', 'created_at'
    ]
    list_filter = [
        'role_type', 'is_active', 'is_default', 'school', 'created_at'
    ]
    search_fields = ['name', 'description', 'school__name']
    readonly_fields = ['created_at', 'updated_at', 'permission_count', 'user_count']
    inlines = [RolePermissionInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'role_type')
        }),
        ('School Association', {
            'fields': ('school',),
            'description': 'Leave blank for system-wide roles'
        }),
        ('Role Configuration', {
            'fields': ('parent_role', 'is_active', 'is_default')
        }),
        ('Statistics', {
            'fields': ('permission_count', 'user_count'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('school', 'created_by').annotate(
            permission_count=Count('role_permissions', filter=Q(role_permissions__granted=True)),
            user_count=Count('user_roles', filter=Q(user_roles__is_active=True))
        )

    def permission_count(self, obj):
        """Display number of permissions assigned to this role."""
        count = getattr(obj, 'permission_count', 0)
        if count > 0:
            return format_html(
                '<a href="{}?role__id__exact={}" style="color: green; font-weight: bold;">{}</a>',
                reverse('admin:users_rolepermission_changelist'),
                obj.id,
                count
            )
        return format_html('<span style="color: gray;">0</span>')
    permission_count.short_description = 'Permissions'
    permission_count.admin_order_field = 'permission_count'

    def user_count(self, obj):
        """Display number of users assigned to this role."""
        count = getattr(obj, 'user_count', 0)
        if count > 0:
            return format_html(
                '<a href="{}?role__id__exact={}" style="color: blue; font-weight: bold;">{}</a>',
                reverse('admin:users_userrole_changelist'),
                obj.id,
                count
            )
        return format_html('<span style="color: gray;">0</span>')
    user_count.short_description = 'Users'
    user_count.admin_order_field = 'user_count'


@admin.register(RolePermission)
class RolePermissionAdmin(admin.ModelAdmin):
    """
    Admin interface for RolePermission model.
    """
    list_display = [
        'role', 'permission', 'granted', 'granted_by',
        'granted_at', 'expires_at', 'is_expired'
    ]
    list_filter = [
        'granted', 'granted_at', 'expires_at',
        'role__school', 'permission__category'
    ]
    search_fields = [
        'role__name', 'permission__name', 'permission__codename'
    ]
    readonly_fields = ['granted_at', 'updated_at', 'is_expired']
    autocomplete_fields = ['role', 'permission', 'granted_by']

    fieldsets = (
        ('Assignment Details', {
            'fields': ('role', 'permission', 'granted')
        }),
        ('Grant Metadata', {
            'fields': ('granted_by', 'granted_at', 'expires_at')
        }),
        ('Status', {
            'fields': ('is_expired',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'role', 'permission', 'granted_by', 'role__school'
        )

    def is_expired(self, obj):
        """Display expiration status with color coding."""
        if obj.expires_at is None:
            return format_html('<span style="color: gray;">Never</span>')
        elif obj.is_expired:
            return format_html('<span style="color: red;">Expired</span>')
        else:
            return format_html('<span style="color: green;">Valid</span>')
    is_expired.short_description = 'Expiration Status'


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """
    Admin interface for UserRole model.
    """
    list_display = [
        'user', 'role', 'is_active', 'valid_until',
        'requires_approval', 'approval_status', 'assigned_at'
    ]
    list_filter = [
        'is_active', 'requires_approval', 'role__school',
        'assigned_at', 'valid_until'
    ]
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'role__name', 'role__school__name'
    ]
    readonly_fields = [
        'assigned_at', 'updated_at', 'approval_status', 'is_expired'
    ]
    autocomplete_fields = ['user', 'role', 'assigned_by', 'approved_by']

    fieldsets = (
        ('Assignment Details', {
            'fields': ('user', 'role', 'is_active')
        }),
        ('Validity Period', {
            'fields': ('valid_from', 'valid_until', 'is_expired')
        }),
        ('Assignment Metadata', {
            'fields': ('assigned_by', 'assigned_at')
        }),
        ('Approval Workflow', {
            'fields': ('requires_approval', 'approved_by', 'approved_at', 'approval_status'),
            'classes': ('collapse',)
        }),
        ('Deactivation', {
            'fields': ('deactivated_by', 'deactivated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'role', 'role__school', 'assigned_by', 'approved_by'
        )

    def approval_status(self, obj):
        """Display approval status with color coding."""
        if not obj.requires_approval:
            return format_html('<span style="color: gray;">N/A</span>')
        elif obj.approved_at:
            return format_html('<span style="color: green;">✓ Approved</span>')
        else:
            return format_html('<span style="color: orange;">⏳ Pending</span>')
    approval_status.short_description = 'Approval Status'

    def is_expired(self, obj):
        """Display expiration status."""
        if obj.is_expired:
            return format_html('<span style="color: red;">Expired</span>')
        elif obj.valid_until:
            return format_html('<span style="color: green;">Valid until {}</span>', obj.valid_until.strftime('%Y-%m-%d'))
        else:
            return format_html('<span style="color: blue;">No expiration</span>')
    is_expired.short_description = 'Expiration Status'


@admin.register(RoleTemplate)
class RoleTemplateAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for RoleTemplate model with advanced features.
    """
    list_display = [
        'name', 'description_preview', 'is_active', 'is_system_template',
        'usage_count', 'permission_count', 'created_by', 'created_at'
    ]
    list_filter = ['is_active', 'is_system_template', 'created_at', 'permissions__category']
    search_fields = ['name', 'description', 'permissions__name']
    readonly_fields = ['usage_count', 'created_at', 'updated_at', 'permission_count', 'permission_preview']
    filter_horizontal = ['permissions']
    actions = ['duplicate_template', 'validate_templates', 'export_templates']

    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'description')
        }),
        ('Template Configuration', {
            'fields': ('permissions', 'permission_preview', 'is_active', 'is_system_template')
        }),
        ('Usage Statistics', {
            'fields': ('usage_count', 'permission_count'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            permission_count=Count('permissions')
        ).select_related('created_by')

    def description_preview(self, obj):
        """Show truncated description."""
        if len(obj.description) > 50:
            return f"{obj.description[:50]}..."
        return obj.description
    description_preview.short_description = 'Description'

    def permission_count(self, obj):
        """Display number of permissions in this template."""
        count = getattr(obj, 'permission_count', obj.permissions.count())
        return format_html(
            '<span style="color: blue; font-weight: bold;">{}</span>',
            count
        )
    permission_count.short_description = 'Permissions'
    permission_count.admin_order_field = 'permission_count'

    def permission_preview(self, obj):
        """Show preview of permissions."""
        permissions = obj.permissions.all()[:5]
        preview = ", ".join([p.name for p in permissions])
        if obj.permissions.count() > 5:
            preview += f" (+{obj.permissions.count() - 5} more)"
        return preview or "No permissions assigned"
    permission_preview.short_description = 'Permission Preview'

    def duplicate_template(self, request, queryset):
        """Duplicate selected templates."""
        duplicated_count = 0
        for template in queryset:
            # Create duplicate
            new_template = RoleTemplate.objects.create(
                name=f"{template.name} (Copy)",
                description=f"Copy of {template.description}",
                is_active=False,  # Start as inactive
                is_system_template=False,  # Copies are not system templates
                created_by=request.user
            )
            # Copy permissions
            new_template.permissions.set(template.permissions.all())
            duplicated_count += 1

        self.message_user(request, f"Successfully duplicated {duplicated_count} templates.")
    duplicate_template.short_description = "Duplicate selected templates"

    def validate_templates(self, request, queryset):
        """Validate selected templates."""
        validation_results = []
        for template in queryset:
            issues = []

            # Check if template has permissions
            if not template.permissions.exists():
                issues.append("No permissions assigned")

            # Check for conflicting permissions
            permissions = template.permissions.all()
            permission_categories = set(p.category for p in permissions)

            if len(permission_categories) > 3:
                issues.append(f"Too many permission categories ({len(permission_categories)})")

            # Check template usage
            if template.usage_count == 0:
                issues.append("Never used")

            validation_results.append({
                'template': template.name,
                'issues': issues or ['No issues found']
            })

        # Display results
        for result in validation_results:
            issues_text = ", ".join(result['issues'])
            self.message_user(request, f"{result['template']}: {issues_text}")

    validate_templates.short_description = "Validate selected templates"

    def export_templates(self, request, queryset):
        """Export selected templates as JSON."""
        import json
        from django.http import HttpResponse

        templates_data = []
        for template in queryset:
            template_data = {
                'name': template.name,
                'description': template.description,
                'is_system_template': template.is_system_template,
                'permissions': [
                    {
                        'codename': p.codename,
                        'name': p.name,
                        'category': p.category
                    }
                    for p in template.permissions.all()
                ]
            }
            templates_data.append(template_data)

        response = HttpResponse(
            json.dumps(templates_data, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = 'attachment; filename="role_templates.json"'
        return response

    export_templates.short_description = "Export selected templates as JSON"

    def save_model(self, request, obj, form, change):
        if not change:  # Creating new template
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(RoleAuditLog)
class RoleAuditLogAdmin(admin.ModelAdmin):
    """
    Admin interface for RoleAuditLog model.
    """
    list_display = [
        'action_type', 'performed_by', 'target_user',
        'target_role', 'school', 'created_at'
    ]
    list_filter = [
        'action_type', 'school', 'created_at'
    ]
    search_fields = [
        'description', 'performed_by__email', 'target_user__email',
        'target_role__name'
    ]
    readonly_fields = [
        'action_type', 'description', 'performed_by', 'target_user',
        'target_role', 'target_permission', 'school', 'ip_address',
        'user_agent', 'metadata', 'created_at'
    ]

    fieldsets = (
        ('Action Details', {
            'fields': ('action_type', 'description', 'created_at')
        }),
        ('Actor Information', {
            'fields': ('performed_by', 'ip_address', 'user_agent')
        }),
        ('Target Information', {
            'fields': ('target_user', 'target_role', 'target_permission', 'school')
        }),
        ('Additional Data', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'performed_by', 'target_user', 'target_role', 'target_permission', 'school'
        )

    def has_add_permission(self, request):
        """Audit logs should not be manually created."""
        return False

    def has_change_permission(self, request, obj=None):
        """Audit logs should not be modified."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Audit logs should not be deleted."""
        return False


# Custom admin site for RBAC management
class RBACAdminSite(admin.AdminSite):
    """
    Custom admin site for RBAC management with enhanced features.
    """
    site_header = 'RBAC Management Dashboard'
    site_title = 'RBAC Admin'
    index_title = 'Role-Based Access Control Management'

    def index(self, request, extra_context=None):
        """
        Enhanced admin index with RBAC statistics.
        """
        extra_context = extra_context or {}

        # Get RBAC statistics
        from django.db.models import Count, Q

        stats = {
            'total_users': User.objects.filter(is_active=True).count(),
            'total_roles': Role.objects.filter(is_active=True).count(),
            'total_permissions': Permission.objects.count(),
            'active_assignments': UserRole.objects.filter(is_active=True).count(),
            'pending_approvals': UserRole.objects.filter(
                requires_approval=True,
                approved_at__isnull=True,
                is_active=False
            ).count(),
            'system_roles': Role.objects.filter(role_type='system').count(),
            'school_roles': Role.objects.filter(role_type='school').count(),
            'custom_roles': Role.objects.filter(role_type='custom').count(),
        }

        # Recent activity
        recent_logs = RoleAuditLog.objects.select_related(
            'performed_by', 'target_user', 'target_role'
        ).order_by('-created_at')[:10]

        extra_context.update({
            'rbac_stats': stats,
            'recent_activity': recent_logs,
        })

        return super().index(request, extra_context)


# Create custom admin site instance
rbac_admin_site = RBACAdminSite(name='rbac_admin')

# Register models with custom admin site
rbac_admin_site.register(User, UserAdmin)
rbac_admin_site.register(Permission, PermissionAdmin)
rbac_admin_site.register(Role, RoleAdmin)
rbac_admin_site.register(RolePermission, RolePermissionAdmin)
rbac_admin_site.register(UserRole, UserRoleAdmin)
rbac_admin_site.register(RoleTemplate, RoleTemplateAdmin)
rbac_admin_site.register(RoleAuditLog, RoleAuditLogAdmin)
