"""
Management command to test admin permissions for school administrators.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from users.admin import UserAdmin
from schools.admin import SchoolAdmin
from courses.admin import CourseAdmin
from users.models import User, Role, UserRole
from schools.models import School
from courses.models import Course
from users.rbac_utils import RBACManager

User = get_user_model()


class Command(BaseCommand):
    help = 'Test admin permissions for school administrators'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing admin permissions...'))
        
        # Test school admin permissions
        self.test_school_admin_permissions()
        
        # Test system admin permissions
        self.test_system_admin_permissions()
        
        self.stdout.write(self.style.SUCCESS('Admin permission tests completed!'))

    def test_school_admin_permissions(self):
        """Test permissions for school administrators."""
        self.stdout.write('\n🏫 Testing School Administrator Permissions...')
        
        # Get a school admin user
        school_admin = User.objects.filter(
            user_type='admin',
            school__isnull=False
        ).first()
        
        if not school_admin:
            self.stdout.write(self.style.ERROR('No school admin found!'))
            return
        
        self.stdout.write(f'Testing with user: {school_admin.email} (School: {school_admin.school.name})')
        
        # Create mock request
        factory = RequestFactory()
        request = factory.get('/admin/')
        request.user = school_admin
        
        # Test User admin permissions
        user_admin = UserAdmin(User, None)
        self.test_model_admin_permissions(user_admin, request, 'Users', school_admin.school)
        
        # Test School admin permissions
        school_admin_instance = SchoolAdmin(School, None)
        self.test_model_admin_permissions(school_admin_instance, request, 'Schools', school_admin.school)
        
        # Test Course admin permissions
        course_admin = CourseAdmin(Course, None)
        self.test_model_admin_permissions(course_admin, request, 'Courses', school_admin.school)
        
        # Test queryset filtering
        self.test_queryset_filtering(school_admin)

    def test_system_admin_permissions(self):
        """Test permissions for system administrators."""
        self.stdout.write('\n🌐 Testing System Administrator Permissions...')
        
        # Get a system admin user
        system_admin = User.objects.filter(user_type='system').first()
        
        if not system_admin:
            self.stdout.write(self.style.ERROR('No system admin found!'))
            return
        
        self.stdout.write(f'Testing with user: {system_admin.email}')
        
        # Create mock request
        factory = RequestFactory()
        request = factory.get('/admin/')
        request.user = system_admin
        
        # Test User admin permissions
        user_admin = UserAdmin(User, None)
        self.test_model_admin_permissions(user_admin, request, 'Users', None)
        
        # Test School admin permissions
        school_admin_instance = SchoolAdmin(School, None)
        self.test_model_admin_permissions(school_admin_instance, request, 'Schools', None)

    def test_model_admin_permissions(self, admin_instance, request, model_name, user_school):
        """Test permissions for a specific model admin."""
        self.stdout.write(f'\n  📋 Testing {model_name} Admin:')
        
        # Test view permission
        has_view = admin_instance.has_view_permission(request)
        self.stdout.write(f'    View Permission: {"✅" if has_view else "❌"}')
        
        # Test add permission
        has_add = admin_instance.has_add_permission(request)
        self.stdout.write(f'    Add Permission: {"✅" if has_add else "❌"}')
        
        # Test change permission
        has_change = admin_instance.has_change_permission(request)
        self.stdout.write(f'    Change Permission: {"✅" if has_change else "❌"}')
        
        # Test delete permission
        has_delete = admin_instance.has_delete_permission(request)
        self.stdout.write(f'    Delete Permission: {"✅" if has_delete else "❌"}')
        
        # Test queryset count
        queryset = admin_instance.get_queryset(request)
        count = queryset.count()
        self.stdout.write(f'    Accessible Records: {count}')
        
        # For school admins, verify they only see their school's data
        if user_school and hasattr(queryset.model, 'school'):
            school_filtered_count = queryset.filter(school=user_school).count()
            if count == school_filtered_count:
                self.stdout.write(f'    ✅ Correctly filtered to school: {user_school.name}')
            else:
                self.stdout.write(f'    ⚠️  Potential data leak: seeing {count} records, expected {school_filtered_count}')

    def test_queryset_filtering(self, user):
        """Test that querysets are properly filtered by school."""
        self.stdout.write(f'\n  🔍 Testing Queryset Filtering for {user.email}:')
        
        # Test user queryset
        user_count = User.objects.filter(school=user.school).count()
        total_users = User.objects.count()
        self.stdout.write(f'    Users in school: {user_count}/{total_users}')
        
        # Test course queryset
        course_count = Course.objects.filter(school=user.school).count()
        total_courses = Course.objects.count()
        self.stdout.write(f'    Courses in school: {course_count}/{total_courses}')
        
        # Test RBAC permissions
        permissions = RBACManager.get_user_permissions(user, user.school)
        self.stdout.write(f'    RBAC Permissions: {len(permissions)}')
        
        # List key permissions
        key_permissions = [
            'can_manage_users',
            'can_view_users',
            'can_manage_school_settings',
            'can_manage_courses'
        ]
        
        for perm in key_permissions:
            has_perm = RBACManager.user_has_permission(user, perm, user.school)
            self.stdout.write(f'      {perm}: {"✅" if has_perm else "❌"}')

        # Test role assignments
        user_roles = UserRole.objects.filter(user=user, is_active=True)
        self.stdout.write(f'    Active Roles: {user_roles.count()}')
        
        for user_role in user_roles:
            self.stdout.write(f'      - {user_role.role.name} (School: {user_role.role.school.name if user_role.role.school else "System"})')

        self.stdout.write('\n' + '='*60)
        self.stdout.write('🎯 PERMISSION TEST SUMMARY')
        self.stdout.write('='*60)
        
        # Summary for school admin
        if user.school:
            self.stdout.write(f'👤 User: {user.email}')
            self.stdout.write(f'🏫 School: {user.school.name}')
            self.stdout.write(f'🔐 Active Roles: {user_roles.count()}')
            self.stdout.write(f'📋 RBAC Permissions: {len(permissions)}')
            
            # Check if user can access admin
            can_access_admin = any([
                RBACManager.user_has_permission(user, 'can_manage_users', user.school),
                RBACManager.user_has_permission(user, 'can_view_users', user.school),
                RBACManager.user_has_permission(user, 'can_manage_school_settings', user.school)
            ])
            
            self.stdout.write(f'🚪 Can Access Admin: {"✅ YES" if can_access_admin else "❌ NO"}')
            
            if can_access_admin:
                self.stdout.write('\n✅ School administrator should now be able to access Django admin!')
                self.stdout.write('🔗 Login URL: http://127.0.0.1:8000/admin/')
                self.stdout.write(f'📧 Email: {user.email}')
                self.stdout.write('🔑 Password: Check COMPREHENSIVE_TEST_CREDENTIALS.md')
            else:
                self.stdout.write('\n❌ School administrator still cannot access admin. Check role assignments.')
        
        self.stdout.write('='*60)
