from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.core.validators import EmailValidator, RegexValidator
from django.core.exceptions import ValidationError
from django.utils import timezone
import uuid
import re
from datetime import datetime


class UserManager(models.Manager):
    """Custom user manager for the User model"""

    def normalize_email(self, email):
        """Normalize the email address by lowercasing the domain part"""
        email = email or ''
        try:
            email_name, domain_part = email.strip().rsplit('@', 1)
        except ValueError:
            pass
        else:
            email = email_name + '@' + domain_part.lower()
        return email

    def get_by_natural_key(self, username):
        """Get user by email (natural key)"""
        return self.get(**{self.model.USERNAME_FIELD: username})

    def create_user(self, email, password=None, **extra_fields):
        """Create and return a regular user with an email and password"""
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """Create and return a superuser with an email and password"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('user_type', 'super_admin')

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self.create_user(email, password, **extra_fields)


class User(AbstractBaseUser, PermissionsMixin):
    """
    Custom User model for the SMS system supporting multi-tenant architecture.
    Users belong to schools and have different types (student, lecturer, admin, etc.)
    """

    # User Types
    USER_TYPES = [
        ('super_admin', 'Super Administrator'),
        ('school_admin', 'School Administrator'),
        ('lecturer', 'Lecturer'),
        ('student', 'Student'),
        ('staff', 'Staff Member'),
        ('parent', 'Parent/Guardian'),
    ]

    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Authentication fields
    email = models.EmailField(unique=True, validators=[EmailValidator()])
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)

    # Personal Information
    first_name = models.CharField(max_length=150, help_text="User's first name")
    last_name = models.CharField(max_length=150, help_text="User's last name")
    middle_name = models.CharField(max_length=150, blank=True, help_text="User's middle name")
    date_of_birth = models.DateField(null=True, blank=True, help_text="Date of birth")
    phone = models.CharField(max_length=20, blank=True, help_text="Phone number")

    # School Association (Multi-tenant key)
    school = models.ForeignKey(
        'schools.School',
        on_delete=models.CASCADE,
        related_name='users',
        null=True,
        blank=True,
        help_text="School this user belongs to (null for super admins)"
    )

    # User Classification
    user_type = models.CharField(
        max_length=20,
        choices=USER_TYPES,
        default='student',
        help_text="Type of user in the system"
    )

    # Student-specific fields
    student_id = models.CharField(
        max_length=50,
        blank=True,
        help_text="Unique student identifier within the school"
    )
    enrollment_date = models.DateField(null=True, blank=True, help_text="Date of enrollment")
    graduation_date = models.DateField(null=True, blank=True, help_text="Expected/actual graduation date")

    # Staff-specific fields
    employee_id = models.CharField(
        max_length=50,
        blank=True,
        help_text="Unique employee identifier within the school"
    )
    hire_date = models.DateField(null=True, blank=True, help_text="Date of hire")
    department = models.CharField(max_length=100, blank=True, help_text="Department or faculty")

    # Personal Information
    GENDER_CHOICES = [
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other'),
        ('prefer_not_to_say', 'Prefer not to say'),
    ]

    gender = models.CharField(
        max_length=20,
        choices=GENDER_CHOICES,
        blank=True,
        help_text="Gender identity"
    )

    nid = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="National Identification Number (NID) - must be unique across all users"
    )

    # Profile Information
    avatar = models.URLField(blank=True, help_text="Profile picture URL")
    bio = models.TextField(blank=True, help_text="User biography")

    # Security and Access
    email_verified = models.BooleanField(default=False, help_text="Email verification status")
    last_login_ip = models.GenericIPAddressField(null=True, blank=True, help_text="Last login IP address")
    failed_login_attempts = models.PositiveIntegerField(default=0, help_text="Failed login attempt count")
    account_locked_until = models.DateTimeField(null=True, blank=True, help_text="Account lock expiry time")

    # Audit Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_users',
        help_text="User who created this account"
    )

    objects = UserManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name']

    class Meta:
        db_table = 'users'
        ordering = ['last_name', 'first_name']
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['school', 'user_type']),
            models.Index(fields=['student_id']),
            models.Index(fields=['employee_id']),
            models.Index(fields=['nid']),
            models.Index(fields=['gender']),
            models.Index(fields=['created_at']),
        ]
        constraints = [
            # Ensure student_id is unique within a school
            models.UniqueConstraint(
                fields=['school', 'student_id'],
                condition=models.Q(student_id__isnull=False) & ~models.Q(student_id=''),
                name='unique_student_id_per_school'
            ),
            # Ensure employee_id is unique within a school
            models.UniqueConstraint(
                fields=['school', 'employee_id'],
                condition=models.Q(employee_id__isnull=False) & ~models.Q(employee_id=''),
                name='unique_employee_id_per_school'
            ),
            # Ensure NID is unique across all users (when provided)
            models.UniqueConstraint(
                fields=['nid'],
                condition=models.Q(nid__isnull=False) & ~models.Q(nid=''),
                name='unique_nid_global'
            ),
        ]

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        """Return the user's full name"""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}".strip()
        return f"{self.first_name} {self.last_name}".strip()

    def get_short_name(self):
        """Return the user's short name"""
        return self.first_name

    @property
    def is_student(self):
        """Check if user is a student"""
        return self.user_type == 'student'

    @property
    def is_lecturer(self):
        """Check if user is a lecturer"""
        return self.user_type == 'lecturer'

    @property
    def is_school_admin(self):
        """Check if user is a school administrator"""
        return self.user_type == 'school_admin'

    def generate_student_registration_number(self):
        """
        Generate a unique student registration number for this school.
        Format: {SCHOOL_CODE}{YEAR}{SEQUENCE}
        Example: *********, *********
        """
        if not self.school:
            raise ValidationError("Cannot generate registration number without a school")

        if self.user_type != 'student':
            raise ValidationError("Registration numbers are only for students")

        # Get school code from slug (first 2-3 letters, uppercase)
        school_code = self._get_school_code()

        # Get current academic year
        current_year = datetime.now().year

        # Find the next sequence number for this school and year
        sequence = self._get_next_sequence_number(school_code, current_year)

        # Format: SCHOOLCODE + YEAR + 3-digit sequence
        registration_number = f"{school_code}{current_year}{sequence:03d}"

        return registration_number

    def _get_school_code(self):
        """Extract school code from school slug"""
        if not self.school or not self.school.slug:
            raise ValidationError("School must have a valid slug")

        # Convert slug to school code
        slug = self.school.slug.upper()

        # Extract meaningful letters from slug
        if 'GREENWOOD' in slug:
            return 'GW'
        elif 'RIVERSIDE' in slug:
            return 'RA'
        else:
            # Generic approach: take first letter of each word
            words = slug.replace('-', ' ').split()
            if len(words) >= 2:
                return ''.join(word[0] for word in words[:2])
            else:
                return slug[:2]

    def _get_next_sequence_number(self, school_code, year):
        """Get the next available sequence number for registration"""
        # Pattern to match registration numbers for this school and year
        pattern = f"^{school_code}{year}(\\d{{3}})$"

        # Find all existing registration numbers for this school and year
        existing_numbers = User.objects.filter(
            school=self.school,
            user_type='student',
            student_id__regex=pattern
        ).values_list('student_id', flat=True)

        # Extract sequence numbers
        sequences = []
        for reg_num in existing_numbers:
            match = re.match(pattern, reg_num)
            if match:
                sequences.append(int(match.group(1)))

        # Find the next available sequence number
        if not sequences:
            return 1

        sequences.sort()
        next_seq = 1
        for seq in sequences:
            if seq == next_seq:
                next_seq += 1
            else:
                break

        return next_seq

    def assign_registration_number(self):
        """Assign a registration number to this student if they don't have one"""
        if self.user_type == 'student' and not self.student_id:
            self.student_id = self.generate_student_registration_number()
            return self.student_id
        return self.student_id

    def save(self, *args, **kwargs):
        """Override save to auto-assign registration number for new students"""
        # Auto-assign registration number for new students
        if self.user_type == 'student' and not self.student_id and self.school:
            try:
                self.student_id = self.generate_student_registration_number()
            except ValidationError:
                pass  # Let the validation happen in clean()

        super().save(*args, **kwargs)

    @property
    def is_super_admin(self):
        """Check if user is a super administrator"""
        return self.user_type == 'super_admin'

    @property
    def is_account_locked(self):
        """Check if account is currently locked"""
        if self.account_locked_until:
            return timezone.now() < self.account_locked_until
        return False

    def has_module_perms(self, app_label):
        """
        Check if user has permissions for a specific Django app module.
        This integrates with our RBAC system for Django admin access.
        """
        # Superusers have access to everything
        if self.is_superuser:
            return True

        # Check if user is staff (required for admin access)
        if not self.is_staff:
            return False

        # Import here to avoid circular imports
        from .rbac_utils import RBACManager

        # Define app-specific permissions that grant module access
        app_permissions = {
            'users': ['can_manage_users', 'can_view_users'],
            'schools': ['can_manage_school_settings', 'can_view_school_settings'],
            'courses': ['can_manage_courses', 'can_view_courses'],
            'auth': ['can_manage_users'],  # Django's auth app
        }

        # Get permissions for this app
        required_permissions = app_permissions.get(app_label, [])

        if not required_permissions:
            # For unknown apps, check if user has any admin permissions
            return any([
                RBACManager.user_has_permission(self, 'can_manage_users', self.school),
                RBACManager.user_has_permission(self, 'can_manage_school_settings', self.school),
                RBACManager.user_has_permission(self, 'can_manage_schools', None),  # System-level
            ])

        # Check if user has any of the required permissions for this app
        for permission in required_permissions:
            # Check school-level permission first
            if self.school and RBACManager.user_has_permission(self, permission, self.school):
                return True
            # Check system-level permission
            if RBACManager.user_has_permission(self, permission, None):
                return True

        return False


class Permission(models.Model):
    """
    Custom permission model for granular access control.
    Extends Django's built-in permission system with categories and descriptions.
    """

    PERMISSION_CATEGORIES = [
        ('academic', 'Academic Management'),
        ('users', 'User Management'),
        ('admin', 'Administrative'),
        ('system', 'System Management'),
        ('reports', 'Reports & Analytics'),
        ('communication', 'Communication'),
        ('files', 'File Management'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Permission identification
    codename = models.CharField(
        max_length=100,
        unique=True,
        validators=[RegexValidator(
            regex=r'^[a-z_]+$',
            message='Codename must contain only lowercase letters and underscores'
        )],
        help_text="Unique permission identifier (e.g., 'can_upload_materials')"
    )
    name = models.CharField(
        max_length=255,
        help_text="Human-readable permission name"
    )
    description = models.TextField(
        blank=True,
        help_text="Detailed description of what this permission allows"
    )

    # Permission categorization
    category = models.CharField(
        max_length=20,
        choices=PERMISSION_CATEGORIES,
        help_text="Permission category for organization"
    )

    # Content type for object-level permissions
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='custom_permissions',
        help_text="Content type this permission applies to"
    )

    # System vs custom permissions
    is_system_permission = models.BooleanField(
        default=True,
        help_text="Whether this is a system-defined permission"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_permissions',
        help_text="User who created this permission"
    )

    class Meta:
        db_table = 'permissions'
        ordering = ['category', 'name']
        indexes = [
            models.Index(fields=['codename']),
            models.Index(fields=['category']),
            models.Index(fields=['content_type']),
            models.Index(fields=['is_system_permission']),
        ]

    def __str__(self):
        return f"{self.name} ({self.codename})"


class Role(models.Model):
    """
    Role model for RBAC system. Supports both system-wide and school-specific roles.
    """

    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Role Information
    name = models.CharField(max_length=100, help_text="Role name")
    description = models.TextField(blank=True, help_text="Role description")

    # School Association (null for system-wide roles)
    school = models.ForeignKey(
        'schools.School',
        on_delete=models.CASCADE,
        related_name='roles',
        null=True,
        blank=True,
        help_text="School this role belongs to (null for system roles)"
    )

    # Role Configuration
    role_type = models.CharField(
        max_length=10,
        choices=[
            ('system', 'System Role'),
            ('school', 'School Role'),
            ('custom', 'Custom Role'),
        ],
        default='school',
        help_text="Type of role"
    )
    is_system_role = models.BooleanField(
        default=False,
        help_text="Whether this is a system-wide role"
    )
    is_active = models.BooleanField(default=True, help_text="Whether this role is active")
    is_default = models.BooleanField(
        default=False,
        help_text="Whether this is a default role for new users"
    )

    # Role hierarchy
    parent_role = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='child_roles',
        help_text="Parent role for hierarchy"
    )

    # Permissions relationship
    permissions = models.ManyToManyField(
        Permission,
        through='RolePermission',
        related_name='assigned_roles',
        blank=True,
        help_text="Permissions assigned to this role"
    )

    # Audit Fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_roles',
        help_text="User who created this role"
    )

    class Meta:
        db_table = 'roles'
        ordering = ['name']
        indexes = [
            models.Index(fields=['school', 'name']),
            models.Index(fields=['is_system_role']),
            models.Index(fields=['is_active']),
        ]
        constraints = [
            # Ensure role name is unique within a school (or globally for system roles)
            models.UniqueConstraint(
                fields=['school', 'name'],
                condition=models.Q(school__isnull=False),
                name='unique_role_name_per_school'
            ),
            models.UniqueConstraint(
                fields=['name'],
                condition=models.Q(school__isnull=True, is_system_role=True),
                name='unique_system_role_name'
            ),
        ]

    def __str__(self):
        if self.school:
            return f"{self.name} ({self.school.name})"
        return f"{self.name} (System)"

    def clean(self):
        """Validate role data for multi-tenant constraints."""
        super().clean()

        # Validate system role constraints
        if self.is_system_role and self.school is not None:
            raise ValidationError("System roles cannot be associated with a school")

        if not self.is_system_role and self.school is None and self.role_type != 'system':
            raise ValidationError("Non-system roles must be associated with a school")

    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        super().save(*args, **kwargs)


# Add multi-tenant manager to Role model
from .multi_tenant_managers import RoleManager
Role.add_to_class('mt_objects', RoleManager())


class RolePermission(models.Model):
    """
    Through model for Role-Permission relationship with additional metadata.
    Allows for explicit permission grants/denies and audit trails.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relationship fields
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name='role_permissions'
    )
    permission = models.ForeignKey(
        Permission,
        on_delete=models.CASCADE,
        related_name='role_permissions'
    )

    # Permission state
    granted = models.BooleanField(
        default=True,
        help_text="Whether permission is granted (True) or denied (False)"
    )

    # Audit fields
    granted_by = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_role_permissions',
        help_text="User who granted/denied this permission"
    )
    granted_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Optional expiration
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this permission grant expires"
    )

    class Meta:
        db_table = 'role_permissions'
        unique_together = ['role', 'permission']
        indexes = [
            models.Index(fields=['role', 'granted']),
            models.Index(fields=['permission', 'granted']),
            models.Index(fields=['expires_at']),
        ]

    def __str__(self):
        status = "granted" if self.granted else "denied"
        return f"{self.role.name} - {self.permission.name} ({status})"

    @property
    def is_expired(self):
        """Check if this permission grant has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False


class UserRole(models.Model):
    """
    Junction table for User-Role many-to-many relationship with additional metadata.
    """

    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Relationships
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='user_roles')

    # Assignment metadata
    assigned_at = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_roles',
        help_text="User who assigned this role"
    )

    # Role validity
    valid_from = models.DateTimeField(default=timezone.now, help_text="Role valid from date")
    valid_until = models.DateTimeField(null=True, blank=True, help_text="Role expiry date")
    is_active = models.BooleanField(default=True, help_text="Whether this role assignment is active")

    # Approval workflow
    requires_approval = models.BooleanField(
        default=False,
        help_text="Whether this role assignment requires approval"
    )
    approved_by = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_user_roles',
        help_text="User who approved this role assignment"
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this role assignment was approved"
    )

    # Deactivation tracking
    deactivated_by = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='deactivated_user_roles',
        help_text="User who deactivated this role assignment"
    )
    deactivated_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this role assignment was deactivated"
    )

    # Additional metadata
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'user_roles'
        ordering = ['-assigned_at']
        indexes = [
            models.Index(fields=['user', 'role']),
            models.Index(fields=['assigned_at']),
            models.Index(fields=['is_active']),
            models.Index(fields=['requires_approval', 'approved_at']),
            models.Index(fields=['valid_until']),
        ]
        constraints = [
            # Ensure a user can't have the same role assigned multiple times (if active)
            models.UniqueConstraint(
                fields=['user', 'role'],
                condition=models.Q(is_active=True),
                name='unique_active_user_role'
            ),
        ]

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.role.name}"

    @property
    def is_valid(self):
        """Check if role assignment is currently valid"""
        now = timezone.now()
        if not self.is_active:
            return False
        if self.valid_until and now > self.valid_until:
            return False
        if self.requires_approval and not self.approved_at:
            return False
        return now >= self.valid_from

    @property
    def is_expired(self):
        """Check if this role assignment has expired"""
        if self.valid_until:
            return timezone.now() > self.valid_until
        return False

    @property
    def is_pending_approval(self):
        """Check if this role assignment is pending approval"""
        return self.requires_approval and not self.approved_at

    def clean(self):
        """Validate user role assignment for multi-tenant constraints."""
        super().clean()

        # Validate school context alignment
        if self.user and self.role:
            user_school = getattr(self.user, 'school', None)
            role_school = self.role.school

            # For school-specific roles, user must be in the same school
            if role_school and user_school != role_school:
                raise ValidationError(
                    f"Cannot assign school role '{self.role.name}' to user from different school"
                )

    def save(self, *args, **kwargs):
        """Override save to ensure data consistency."""
        self.clean()
        super().save(*args, **kwargs)


# Add multi-tenant manager to UserRole model
from .multi_tenant_managers import UserRoleManager
UserRole.add_to_class('mt_objects', UserRoleManager())


class RoleTemplate(models.Model):
    """
    Role template model for creating standardized roles across schools.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Template identification
    name = models.CharField(
        max_length=100,
        unique=True,
        help_text="Template name (e.g., 'Standard Lecturer')"
    )
    description = models.TextField(
        help_text="Description of the role template"
    )

    # Template configuration
    permissions = models.ManyToManyField(
        Permission,
        related_name='role_templates',
        help_text="Permissions included in this template"
    )

    # Template metadata
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this template is active"
    )
    is_system_template = models.BooleanField(
        default=True,
        help_text="Whether this is a system-provided template"
    )

    # Usage tracking
    usage_count = models.PositiveIntegerField(
        default=0,
        help_text="Number of times this template has been used"
    )

    # Audit fields
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_role_templates',
        help_text="User who created this template"
    )

    class Meta:
        db_table = 'role_templates'
        ordering = ['name']
        indexes = [
            models.Index(fields=['is_active']),
            models.Index(fields=['is_system_template']),
            models.Index(fields=['usage_count']),
        ]

    def __str__(self):
        return self.name


class RoleAuditLog(models.Model):
    """
    Audit log for tracking role and permission changes.
    """

    ACTION_TYPES = [
        ('role_created', 'Role Created'),
        ('role_updated', 'Role Updated'),
        ('role_deleted', 'Role Deleted'),
        ('permission_granted', 'Permission Granted'),
        ('permission_revoked', 'Permission Revoked'),
        ('user_role_assigned', 'User Role Assigned'),
        ('user_role_removed', 'User Role Removed'),
        ('role_approved', 'Role Assignment Approved'),
        ('role_denied', 'Role Assignment Denied'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Action details
    action_type = models.CharField(
        max_length=20,
        choices=ACTION_TYPES,
        help_text="Type of action performed"
    )
    description = models.TextField(
        help_text="Detailed description of the action"
    )

    # Actor information
    performed_by = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='performed_role_actions',
        help_text="User who performed the action"
    )

    # Target information
    target_user = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='role_audit_logs',
        help_text="User affected by the action"
    )
    target_role = models.ForeignKey(
        Role,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs',
        help_text="Role affected by the action"
    )
    target_permission = models.ForeignKey(
        Permission,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='audit_logs',
        help_text="Permission affected by the action"
    )

    # Context information
    school = models.ForeignKey(
        'schools.School',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='role_audit_logs',
        help_text="School context for the action"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the user performing the action"
    )
    user_agent = models.TextField(
        blank=True,
        help_text="User agent of the client"
    )

    # Additional data
    metadata = models.JSONField(
        default=dict,
        blank=True,
        help_text="Additional metadata about the action"
    )

    # Timestamp
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'role_audit_logs'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['action_type', 'created_at']),
            models.Index(fields=['performed_by', 'created_at']),
            models.Index(fields=['target_user', 'created_at']),
            models.Index(fields=['school', 'created_at']),
        ]

    def __str__(self):
        return f"{self.action_type} by {self.performed_by} at {self.created_at}"
