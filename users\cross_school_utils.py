"""
Cross-school utilities for super administrators to compare and manage roles across schools.
"""

import logging
from collections import defaultdict
from django.db.models import Count, Q, Prefetch
from django.core.exceptions import PermissionDenied
from django.contrib.auth import get_user_model
from .models import Role, UserRole, Permission, RolePermission
from .rbac_utils import RBACManager

User = get_user_model()
logger = logging.getLogger(__name__)


class CrossSchoolRoleComparator:
    """
    Utility class for comparing roles across different schools.
    Only accessible to super administrators.
    """
    
    @staticmethod
    def validate_super_admin_access(user):
        """
        Validate that user has super admin access for cross-school operations.
        
        Args:
            user: User instance
            
        Raises:
            PermissionDenied: If user doesn't have required permissions
        """
        if not user.is_superuser:
            # Check if user has cross-school management permission
            if not RBACManager.user_has_permission(user, 'can_manage_schools'):
                raise PermissionDenied("Insufficient permissions for cross-school operations")
    
    @classmethod
    def get_role_distribution_by_school(cls, user):
        """
        Get role distribution statistics across all schools.
        
        Args:
            user: User requesting the data (must be super admin)
            
        Returns:
            dict: Role distribution data by school
        """
        cls.validate_super_admin_access(user)
        
        from schools.models import School
        
        # Get all schools with role counts
        schools = School.objects.annotate(
            total_roles=Count('roles', filter=Q(roles__is_active=True)),
            system_roles=Count('roles', filter=Q(roles__is_active=True, roles__role_type='system')),
            school_roles=Count('roles', filter=Q(roles__is_active=True, roles__role_type='school')),
            custom_roles=Count('roles', filter=Q(roles__is_active=True, roles__role_type='custom')),
            total_users=Count('users', filter=Q(users__is_active=True)),
            users_with_roles=Count(
                'users__user_roles', 
                filter=Q(users__user_roles__is_active=True),
                distinct=True
            )
        ).order_by('name')
        
        distribution = []
        for school in schools:
            distribution.append({
                'school_id': str(school.id),
                'school_name': school.name,
                'total_roles': school.total_roles,
                'role_breakdown': {
                    'system': school.system_roles,
                    'school': school.school_roles,
                    'custom': school.custom_roles,
                },
                'user_stats': {
                    'total_users': school.total_users,
                    'users_with_roles': school.users_with_roles,
                    'coverage_percentage': round(
                        (school.users_with_roles / school.total_users * 100) 
                        if school.total_users > 0 else 0, 2
                    )
                }
            })
        
        return {
            'schools': distribution,
            'summary': {
                'total_schools': len(distribution),
                'total_roles': sum(s['total_roles'] for s in distribution),
                'total_users': sum(s['user_stats']['total_users'] for s in distribution),
                'average_roles_per_school': round(
                    sum(s['total_roles'] for s in distribution) / len(distribution)
                    if distribution else 0, 2
                )
            }
        }
    
    @classmethod
    def compare_roles_across_schools(cls, user, role_names=None, school_ids=None):
        """
        Compare specific roles across multiple schools.
        
        Args:
            user: User requesting the comparison (must be super admin)
            role_names: List of role names to compare (optional)
            school_ids: List of school IDs to include (optional)
            
        Returns:
            dict: Role comparison data
        """
        cls.validate_super_admin_access(user)
        
        from schools.models import School
        
        # Build base queryset
        roles_query = Role.objects.filter(is_active=True).select_related('school')
        
        # Filter by role names if provided
        if role_names:
            roles_query = roles_query.filter(name__in=role_names)
        
        # Filter by schools if provided
        if school_ids:
            roles_query = roles_query.filter(
                Q(school_id__in=school_ids) | Q(school__isnull=True)
            )
        
        # Get roles with permission counts
        roles = roles_query.annotate(
            permission_count=Count('permissions', filter=Q(role_permissions__granted=True)),
            user_count=Count('user_roles', filter=Q(user_roles__is_active=True))
        ).order_by('name', 'school__name')
        
        # Group roles by name for comparison
        role_comparison = defaultdict(list)
        
        for role in roles:
            role_data = {
                'role_id': str(role.id),
                'school_id': str(role.school.id) if role.school else None,
                'school_name': role.school.name if role.school else 'System',
                'role_type': role.role_type,
                'permission_count': role.permission_count,
                'user_count': role.user_count,
                'is_system_role': role.is_system_role,
                'created_at': role.created_at.isoformat(),
            }
            role_comparison[role.name].append(role_data)
        
        # Calculate comparison metrics
        comparison_results = []
        for role_name, role_instances in role_comparison.items():
            # Find variations in permissions
            permission_counts = [r['permission_count'] for r in role_instances]
            
            comparison_results.append({
                'role_name': role_name,
                'instances': role_instances,
                'instance_count': len(role_instances),
                'schools_with_role': [r['school_name'] for r in role_instances],
                'permission_variance': {
                    'min': min(permission_counts) if permission_counts else 0,
                    'max': max(permission_counts) if permission_counts else 0,
                    'avg': round(sum(permission_counts) / len(permission_counts), 2) 
                         if permission_counts else 0,
                    'has_variance': len(set(permission_counts)) > 1
                },
                'total_users': sum(r['user_count'] for r in role_instances)
            })
        
        return {
            'comparison': comparison_results,
            'summary': {
                'total_role_types': len(comparison_results),
                'roles_with_variance': len([r for r in comparison_results 
                                          if r['permission_variance']['has_variance']]),
                'most_common_role': max(comparison_results, 
                                      key=lambda x: x['instance_count'])['role_name']
                                    if comparison_results else None
            }
        }
    
    @classmethod
    def get_role_permission_matrix(cls, user, role_name, school_ids=None):
        """
        Get detailed permission matrix for a specific role across schools.
        
        Args:
            user: User requesting the matrix (must be super admin)
            role_name: Name of the role to analyze
            school_ids: List of school IDs to include (optional)
            
        Returns:
            dict: Permission matrix data
        """
        cls.validate_super_admin_access(user)
        
        # Get roles with the specified name
        roles_query = Role.objects.filter(
            name=role_name,
            is_active=True
        ).select_related('school').prefetch_related(
            Prefetch(
                'role_permissions',
                queryset=RolePermission.objects.select_related('permission')
            )
        )
        
        if school_ids:
            roles_query = roles_query.filter(
                Q(school_id__in=school_ids) | Q(school__isnull=True)
            )
        
        roles = list(roles_query)
        
        if not roles:
            return {
                'role_name': role_name,
                'roles': [],
                'permission_matrix': {},
                'summary': {
                    'total_roles': 0,
                    'unique_permissions': 0,
                    'common_permissions': []
                }
            }
        
        # Build permission matrix
        all_permissions = set()
        role_permissions = {}
        
        for role in roles:
            school_key = f"{role.school.name}" if role.school else "System"
            role_permissions[school_key] = {
                'role_id': str(role.id),
                'school_id': str(role.school.id) if role.school else None,
                'permissions': {}
            }
            
            # Get permissions for this role
            for rp in role.role_permissions.filter(granted=True):
                permission_code = rp.permission.codename
                all_permissions.add(permission_code)
                role_permissions[school_key]['permissions'][permission_code] = {
                    'granted': rp.granted,
                    'granted_at': rp.granted_at.isoformat(),
                    'permission_name': rp.permission.name,
                    'category': rp.permission.category
                }
        
        # Create matrix showing which schools have which permissions
        permission_matrix = {}
        for permission in sorted(all_permissions):
            permission_matrix[permission] = {}
            for school_key in role_permissions:
                permission_matrix[permission][school_key] = (
                    permission in role_permissions[school_key]['permissions']
                )
        
        # Find common permissions (present in all roles)
        common_permissions = []
        if role_permissions:
            for permission in all_permissions:
                if all(permission_matrix[permission][school] for school in role_permissions):
                    common_permissions.append(permission)
        
        return {
            'role_name': role_name,
            'roles': [
                {
                    'school': school,
                    'role_id': data['role_id'],
                    'school_id': data['school_id'],
                    'permission_count': len(data['permissions'])
                }
                for school, data in role_permissions.items()
            ],
            'permission_matrix': permission_matrix,
            'role_permissions': role_permissions,
            'summary': {
                'total_roles': len(roles),
                'unique_permissions': len(all_permissions),
                'common_permissions': common_permissions,
                'permission_variance': len(all_permissions) - len(common_permissions)
            }
        }
    
    @classmethod
    def suggest_role_standardization(cls, user, role_name):
        """
        Suggest standardization for a role across schools.
        
        Args:
            user: User requesting suggestions (must be super admin)
            role_name: Name of the role to standardize
            
        Returns:
            dict: Standardization suggestions
        """
        cls.validate_super_admin_access(user)
        
        # Get permission matrix for the role
        matrix_data = cls.get_role_permission_matrix(user, role_name)
        
        if not matrix_data['roles']:
            return {
                'role_name': role_name,
                'suggestions': [],
                'standardization_needed': False
            }
        
        suggestions = []
        permission_matrix = matrix_data['permission_matrix']
        role_permissions = matrix_data['role_permissions']
        
        # Analyze permission discrepancies
        for permission, school_matrix in permission_matrix.items():
            schools_with_permission = [school for school, has_perm in school_matrix.items() if has_perm]
            schools_without_permission = [school for school, has_perm in school_matrix.items() if not has_perm]
            
            if schools_without_permission and schools_with_permission:
                # Permission exists in some schools but not others
                majority_has_permission = len(schools_with_permission) > len(schools_without_permission)
                
                if majority_has_permission:
                    suggestions.append({
                        'type': 'add_permission',
                        'permission': permission,
                        'action': f"Add '{permission}' to schools: {', '.join(schools_without_permission)}",
                        'affected_schools': schools_without_permission,
                        'justification': f"Majority of schools ({len(schools_with_permission)}/{len(school_matrix)}) have this permission"
                    })
                else:
                    suggestions.append({
                        'type': 'remove_permission',
                        'permission': permission,
                        'action': f"Consider removing '{permission}' from schools: {', '.join(schools_with_permission)}",
                        'affected_schools': schools_with_permission,
                        'justification': f"Minority of schools ({len(schools_with_permission)}/{len(school_matrix)}) have this permission"
                    })
        
        return {
            'role_name': role_name,
            'suggestions': suggestions,
            'standardization_needed': len(suggestions) > 0,
            'summary': {
                'total_suggestions': len(suggestions),
                'add_permissions': len([s for s in suggestions if s['type'] == 'add_permission']),
                'remove_permissions': len([s for s in suggestions if s['type'] == 'remove_permission']),
                'affected_schools': len(set(
                    school for s in suggestions for school in s['affected_schools']
                ))
            }
        }
