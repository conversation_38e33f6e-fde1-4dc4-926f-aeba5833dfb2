#!/usr/bin/env python
"""
Script to test the SMS API endpoints.
"""
import requests
import json

BASE_URL = 'http://127.0.0.1:8000/api/v1'

def test_jwt_login():
    """Test JWT login endpoint"""
    print("Testing JWT Login...")
    
    login_data = {
        'email': '<EMAIL>',
        'password': 'admin123'
    }
    
    response = requests.post(f'{BASE_URL}/token/', json=login_data)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        tokens = response.json()
        print("Login successful!")
        print(f"Access Token: {tokens.get('access', 'N/A')[:50]}...")
        print(f"Refresh Token: {tokens.get('refresh', 'N/A')[:50]}...")
        return tokens.get('access')
    else:
        print(f"Login failed: {response.text}")
        return None

def test_schools_endpoint(access_token):
    """Test schools endpoint with authentication"""
    print("\nTesting Schools Endpoint...")
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(f'{BASE_URL}/schools/', headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        schools = response.json()
        print(f"Schools retrieved: {len(schools.get('results', []))} schools")
        print(f"Response: {json.dumps(schools, indent=2)}")
    else:
        print(f"Schools request failed: {response.text}")

def test_users_endpoint(access_token):
    """Test users endpoint with authentication"""
    print("\nTesting Users Endpoint...")
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    response = requests.get(f'{BASE_URL}/users/', headers=headers)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        users = response.json()
        print(f"Users retrieved: {len(users.get('results', []))} users")
        print(f"Response: {json.dumps(users, indent=2)}")
    else:
        print(f"Users request failed: {response.text}")

def test_multi_tenant_access():
    """Test multi-tenant data isolation"""
    print("\n=== Testing Multi-Tenant Data Isolation ===")

    # Test with Greenwood admin
    print("\n--- Testing Greenwood Admin Access ---")
    greenwood_token = test_specific_login('<EMAIL>', 'admin123')
    if greenwood_token:
        test_schools_endpoint(greenwood_token)
        test_users_endpoint(greenwood_token)

    # Test with Riverside admin
    print("\n--- Testing Riverside Admin Access ---")
    riverside_token = test_specific_login('<EMAIL>', 'admin123')
    if riverside_token:
        test_schools_endpoint(riverside_token)
        test_users_endpoint(riverside_token)

def test_specific_login(email, password):
    """Test login with specific credentials"""
    print(f"Testing login for {email}...")

    login_data = {
        'email': email,
        'password': password
    }

    response = requests.post(f'{BASE_URL}/token/', json=login_data)
    print(f"Status Code: {response.status_code}")

    if response.status_code == 200:
        tokens = response.json()
        print("Login successful!")
        return tokens.get('access')
    else:
        print(f"Login failed: {response.text}")
        return None

def test_api_endpoints():
    """Test all API endpoints"""
    print("=== SMS API Testing ===\n")

    # Test super admin JWT login
    print("--- Testing Super Admin Access ---")
    access_token = test_jwt_login()

    if access_token:
        # Test authenticated endpoints
        test_schools_endpoint(access_token)
        test_users_endpoint(access_token)

        # Test multi-tenant isolation
        test_multi_tenant_access()
    else:
        print("Cannot test authenticated endpoints without valid token")

if __name__ == '__main__':
    test_api_endpoints()
