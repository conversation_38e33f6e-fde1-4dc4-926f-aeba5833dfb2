"""
Management command to create a comprehensive set of test users for RBAC system demonstration.
Creates users with diverse roles, permissions, and multi-tenant scenarios.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.contrib.auth.hashers import make_password
from schools.models import School
from users.models import Role, UserRole, RoleTemplate
from users.rbac_utils import RBACManager
import uuid
from datetime import date, datetime

User = get_user_model()


class Command(BaseCommand):
    help = 'Create comprehensive test users for RBAC system demonstration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear all existing users before creating new ones'
        )
        
        parser.add_argument(
            '--create-schools',
            action='store_true',
            help='Create test schools if they don\'t exist'
        )

    def handle(self, *args, **options):
        clear_existing = options['clear_existing']
        create_schools = options['create_schools']

        self.stdout.write(self.style.SUCCESS('Creating comprehensive test user environment...'))

        with transaction.atomic():
            # Clear existing data if requested
            if clear_existing:
                self.clear_existing_data()

            # Create test schools
            if create_schools:
                self.create_test_schools()

            # Create comprehensive user set
            self.create_system_administrators()
            self.create_school_administrators()
            self.create_teachers()
            self.create_students()
            self.create_staff_members()
            self.create_special_cases()

        self.stdout.write(self.style.SUCCESS('Comprehensive test environment created!'))
        self.display_all_credentials()

    def clear_existing_data(self):
        """Clear existing test data."""
        self.stdout.write('Clearing existing test data...')
        
        # Clear user roles first (foreign key constraints)
        UserRole.objects.all().delete()
        
        # Clear users (except superusers to avoid locking out)
        User.objects.filter(is_superuser=False).delete()
        
        # Clear test schools
        School.objects.filter(name__icontains='test').delete()
        
        self.stdout.write('Existing test data cleared.')

    def create_test_schools(self):
        """Create diverse test schools for multi-tenant testing."""
        self.stdout.write('Creating test schools...')
        
        schools_data = [
            {
                'name': 'Greenwood Elementary School',
                'slug': 'greenwood-elementary',
                'address_line_1': '123 Oak Street',
                'city': 'Springfield',
                'state_province': 'Illinois',
                'postal_code': '62701',
                'country': 'United States',
                'phone': '******-555-0101',
                'email': '<EMAIL>',
                'website': 'https://greenwood-elementary.edu',
                'subscription_plan': 'basic',
                'status': 'active'
            },
            {
                'name': 'Riverside High School',
                'slug': 'riverside-high',
                'address_line_1': '456 River Road',
                'city': 'Riverside',
                'state_province': 'California',
                'postal_code': '92503',
                'country': 'United States',
                'phone': '******-555-0202',
                'email': '<EMAIL>',
                'website': 'https://riverside-high.edu',
                'subscription_plan': 'premium',
                'status': 'active'
            },
            {
                'name': 'Metropolitan University Prep',
                'slug': 'metro-university-prep',
                'address_line_1': '789 University Avenue',
                'city': 'Metro City',
                'state_province': 'New York',
                'postal_code': '10001',
                'country': 'United States',
                'phone': '******-555-0303',
                'email': '<EMAIL>',
                'website': 'https://metro-prep.edu',
                'subscription_plan': 'enterprise',
                'status': 'active'
            },
            {
                'name': 'Sunset Community College',
                'slug': 'sunset-community',
                'address_line_1': '321 Sunset Boulevard',
                'city': 'Los Angeles',
                'state_province': 'California',
                'postal_code': '90028',
                'country': 'United States',
                'phone': '******-555-0404',
                'email': '<EMAIL>',
                'website': 'https://sunset-college.edu',
                'subscription_plan': 'standard',
                'status': 'pending'
            }
        ]

        for school_data in schools_data:
            school_data.update({
                'academic_year_start': date(2024, 9, 1),
                'academic_year_end': date(2025, 6, 30),
            })
            
            school, created = School.objects.get_or_create(
                name=school_data['name'],
                defaults=school_data
            )
            
            if created:
                self.stdout.write(f'Created school: {school.name}')

    def create_system_administrators(self):
        """Create system-level administrators."""
        self.stdout.write('Creating system administrators...')
        
        # Super Administrator
        super_admin = self.create_user({
            'email': '<EMAIL>',
            'password': 'SuperAdmin123!',
            'first_name': 'System',
            'last_name': 'Administrator',
            'user_type': 'system',
            'is_superuser': True,
            'is_staff': True,
            'is_active': True
        })

        # System Manager
        system_manager = self.create_user({
            'email': '<EMAIL>',
            'password': 'Manager123!',
            'first_name': 'System',
            'last_name': 'Manager',
            'user_type': 'system',
            'is_staff': True,
            'is_active': True
        })

        # System Auditor (read-only access)
        system_auditor = self.create_user({
            'email': '<EMAIL>',
            'password': 'Auditor123!',
            'first_name': 'System',
            'last_name': 'Auditor',
            'user_type': 'system',
            'is_staff': True,
            'is_active': True
        })

    def create_school_administrators(self):
        """Create school-level administrators for each school."""
        self.stdout.write('Creating school administrators...')
        
        schools = School.objects.all()
        
        for i, school in enumerate(schools):
            # Primary School Administrator
            admin = self.create_user({
                'email': f'admin@{school.slug}.edu',
                'password': f'Admin{i+1}23!',
                'first_name': f'{school.name.split()[0]}',
                'last_name': 'Administrator',
                'user_type': 'admin',
                'school': school,
                'is_staff': True,
                'is_active': True
            })
            
            # Assistant Administrator
            assistant = self.create_user({
                'email': f'assistant@{school.slug}.edu',
                'password': f'Assistant{i+1}23!',
                'first_name': f'{school.name.split()[0]}',
                'last_name': 'Assistant',
                'user_type': 'admin',
                'school': school,
                'is_staff': True,
                'is_active': True
            })

    def create_teachers(self):
        """Create teachers for each school."""
        self.stdout.write('Creating teachers...')
        
        schools = School.objects.all()
        teacher_subjects = ['Mathematics', 'English', 'Science', 'History', 'Art', 'Physical Education']
        
        for school in schools:
            for i, subject in enumerate(teacher_subjects):
                teacher = self.create_user({
                    'email': f'{subject.lower().replace(" ", "")}@{school.slug}.edu',
                    'password': f'Teacher{i+1}23!',
                    'first_name': f'{subject}',
                    'last_name': 'Teacher',
                    'user_type': 'teacher',
                    'school': school,
                    'department': subject,
                    'is_active': True
                })

    def create_students(self):
        """Create students for each school."""
        self.stdout.write('Creating students...')
        
        schools = School.objects.all()
        student_names = [
            ('Alice', 'Johnson'), ('Bob', 'Smith'), ('Carol', 'Davis'),
            ('David', 'Wilson'), ('Emma', 'Brown'), ('Frank', 'Miller'),
            ('Grace', 'Taylor'), ('Henry', 'Anderson'), ('Ivy', 'Thomas'),
            ('Jack', 'Jackson')
        ]
        
        for school in schools:
            for i, (first_name, last_name) in enumerate(student_names):
                student = self.create_user({
                    'email': f'{first_name.lower()}.{last_name.lower()}@{school.slug}.edu',
                    'password': f'Student{i+1}23!',
                    'first_name': first_name,
                    'last_name': last_name,
                    'user_type': 'student',
                    'school': school,
                    'student_id': f'{school.slug.upper()[:3]}{1000+i}',
                    'enrollment_date': date(2024, 9, 1),
                    'is_active': True
                })

    def create_staff_members(self):
        """Create various staff members."""
        self.stdout.write('Creating staff members...')
        
        schools = School.objects.all()
        staff_roles = [
            ('librarian', 'Library'), ('counselor', 'Counseling'),
            ('nurse', 'Health'), ('security', 'Security'),
            ('maintenance', 'Facilities')
        ]
        
        for school in schools:
            for role, dept in staff_roles:
                staff = self.create_user({
                    'email': f'{role}@{school.slug}.edu',
                    'password': f'{role.title()}123!',
                    'first_name': f'{school.name.split()[0]}',
                    'last_name': f'{role.title()}',
                    'user_type': 'staff',
                    'school': school,
                    'department': dept,
                    'is_active': True
                })

    def create_special_cases(self):
        """Create special test cases for edge scenarios."""
        self.stdout.write('Creating special test cases...')
        
        school = School.objects.first()
        
        # Inactive user
        inactive_user = self.create_user({
            'email': '<EMAIL>',
            'password': 'Inactive123!',
            'first_name': 'Inactive',
            'last_name': 'User',
            'user_type': 'student',
            'school': school,
            'is_active': False
        })
        
        # User with multiple roles (if supported)
        multi_role_user = self.create_user({
            'email': '<EMAIL>',
            'password': 'MultiRole123!',
            'first_name': 'Multi',
            'last_name': 'Role',
            'user_type': 'teacher',
            'school': school,
            'is_active': True
        })

    def create_user(self, user_data):
        """Helper method to create a user with error handling."""
        try:
            email = user_data['email']
            
            # Check if user already exists
            if User.objects.filter(email=email).exists():
                self.stdout.write(f'User {email} already exists, skipping...')
                return User.objects.get(email=email)
            
            # Hash the password
            password = user_data.pop('password')
            user_data['password'] = make_password(password)
            
            # Create the user
            user = User.objects.create(**user_data)
            
            self.stdout.write(f'Created user: {email}')
            return user
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error creating user {user_data.get("email", "unknown")}: {str(e)}')
            )
            return None

    def display_all_credentials(self):
        """Display all created credentials organized by role."""
        self.stdout.write('\n' + '='*80)
        self.stdout.write(self.style.SUCCESS('COMPREHENSIVE TEST USER CREDENTIALS'))
        self.stdout.write('='*80)
        
        # System Administrators
        self.stdout.write('\n🔴 SYSTEM ADMINISTRATORS:')
        system_users = User.objects.filter(user_type='system')
        for user in system_users:
            self.stdout.write(f'  📧 {user.email} | 🔒 Password: [See creation logs]')
            self.stdout.write(f'     Name: {user.first_name} {user.last_name}')
            self.stdout.write(f'     Access: {"Superuser" if user.is_superuser else "System Manager"}')
        
        # School Administrators
        self.stdout.write('\n🟡 SCHOOL ADMINISTRATORS:')
        admin_users = User.objects.filter(user_type='admin')
        for user in admin_users:
            self.stdout.write(f'  📧 {user.email} | 🏫 {user.school.name if user.school else "No School"}')
        
        # Teachers
        self.stdout.write('\n🟢 TEACHERS:')
        teacher_users = User.objects.filter(user_type='teacher')[:10]  # Show first 10
        for user in teacher_users:
            self.stdout.write(f'  📧 {user.email} | 🏫 {user.school.name if user.school else "No School"}')
        
        # Students
        self.stdout.write('\n🔵 STUDENTS:')
        student_users = User.objects.filter(user_type='student')[:10]  # Show first 10
        for user in student_users:
            self.stdout.write(f'  📧 {user.email} | 🏫 {user.school.name if user.school else "No School"}')
        
        # Staff
        self.stdout.write('\n🟣 STAFF MEMBERS:')
        staff_users = User.objects.filter(user_type='staff')[:10]  # Show first 10
        for user in staff_users:
            self.stdout.write(f'  📧 {user.email} | 🏫 {user.school.name if user.school else "No School"}')
        
        self.stdout.write('\n' + '='*80)
        self.stdout.write('🔗 Admin Dashboard: http://127.0.0.1:8000/admin/')
        self.stdout.write('📊 API Root: http://127.0.0.1:8000/api/')
        self.stdout.write('='*80)
        
        # Statistics
        total_users = User.objects.count()
        total_schools = School.objects.count()
        self.stdout.write(f'\n📊 STATISTICS:')
        self.stdout.write(f'   Total Users: {total_users}')
        self.stdout.write(f'   Total Schools: {total_schools}')
        self.stdout.write(f'   System Admins: {User.objects.filter(user_type="system").count()}')
        self.stdout.write(f'   School Admins: {User.objects.filter(user_type="admin").count()}')
        self.stdout.write(f'   Teachers: {User.objects.filter(user_type="teacher").count()}')
        self.stdout.write(f'   Students: {User.objects.filter(user_type="student").count()}')
        self.stdout.write(f'   Staff: {User.objects.filter(user_type="staff").count()}')
        
        self.stdout.write(
            self.style.WARNING(
                '\n⚠️  SECURITY WARNING: Change all passwords in production!'
            )
        )
