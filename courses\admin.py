from django.contrib import admin
from django.db.models import Count
from .models import Course


@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    """
    Enhanced admin interface for Course model with RBAC features.
    """
    list_display = [
        'code', 'name', 'department', 'level', 'semester',
        'academic_year', 'school', 'is_active', 'created_at'
    ]
    list_filter = [
        'department', 'level', 'semester', 'academic_year',
        'is_active', 'school', 'created_at'
    ]
    search_fields = ['code', 'name', 'description', 'department']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('code', 'name', 'description')
        }),
        ('Academic Details', {
            'fields': ('department', 'level', 'semester', 'academic_year')
        }),
        ('Schedule', {
            'fields': ('start_date', 'end_date')
        }),
        ('School Association', {
            'fields': ('school',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Audit Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Filter courses based on user's school context and permissions."""
        queryset = super().get_queryset(request).select_related('school')

        # Super users can see all courses
        if request.user.is_superuser:
            return queryset

        # Users with system-level permissions can see all courses
        from users.rbac_utils import RBACManager
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return queryset

        # School admins can only see courses in their school
        if request.user.school:
            return queryset.filter(school=request.user.school)

        # Default: no access
        return queryset.none()

    def has_view_permission(self, request, obj=None):
        """Check if user can view courses."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school:
            if obj and obj.school != request.user.school:
                return False
            return RBACManager.user_has_permission(request.user, 'can_view_courses', school=request.user.school)

        return False

    def has_change_permission(self, request, obj=None):
        """Check if user can change courses."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school:
            if obj and obj.school != request.user.school:
                return False
            return RBACManager.user_has_permission(request.user, 'can_manage_courses', school=request.user.school)

        return False

    def has_add_permission(self, request):
        """Check if user can add courses."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school and RBACManager.user_has_permission(request.user, 'can_manage_courses', school=request.user.school):
            return True

        return False

    def has_delete_permission(self, request, obj=None):
        """Check if user can delete courses."""
        if request.user.is_superuser:
            return True

        from users.rbac_utils import RBACManager

        # Check for system-level permissions
        if RBACManager.user_has_permission(request.user, 'can_manage_schools', school=None):
            return True

        # Check for school-level permissions
        if request.user.school:
            if obj and obj.school != request.user.school:
                return False
            return RBACManager.user_has_permission(request.user, 'can_manage_courses', school=request.user.school)

        return False

    def save_model(self, request, obj, form, change):
        """Automatically set the school for new courses."""
        if not change and not obj.school and request.user.school:
            obj.school = request.user.school
        super().save_model(request, obj, form, change)
