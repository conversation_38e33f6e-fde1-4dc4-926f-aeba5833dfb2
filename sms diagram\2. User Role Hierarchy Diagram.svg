<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-791" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 1945.056396484375 850.2500610351562" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-791{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-791 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-791 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-791 .error-icon{fill:#a44141;}#graph-791 .error-text{fill:#ddd;stroke:#ddd;}#graph-791 .edge-thickness-normal{stroke-width:1px;}#graph-791 .edge-thickness-thick{stroke-width:3.5px;}#graph-791 .edge-pattern-solid{stroke-dasharray:0;}#graph-791 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-791 .edge-pattern-dashed{stroke-dasharray:3;}#graph-791 .edge-pattern-dotted{stroke-dasharray:2;}#graph-791 .marker{fill:lightgrey;stroke:lightgrey;}#graph-791 .marker.cross{stroke:lightgrey;}#graph-791 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-791 p{margin:0;}#graph-791 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-791 .cluster-label text{fill:#F9FFFE;}#graph-791 .cluster-label span{color:#F9FFFE;}#graph-791 .cluster-label span p{background-color:transparent;}#graph-791 .label text,#graph-791 span{fill:#ccc;color:#ccc;}#graph-791 .node rect,#graph-791 .node circle,#graph-791 .node ellipse,#graph-791 .node polygon,#graph-791 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-791 .rough-node .label text,#graph-791 .node .label text,#graph-791 .image-shape .label,#graph-791 .icon-shape .label{text-anchor:middle;}#graph-791 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-791 .rough-node .label,#graph-791 .node .label,#graph-791 .image-shape .label,#graph-791 .icon-shape .label{text-align:center;}#graph-791 .node.clickable{cursor:pointer;}#graph-791 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-791 .arrowheadPath{fill:lightgrey;}#graph-791 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-791 .flowchart-link{stroke:lightgrey;fill:none;}#graph-791 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-791 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-791 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-791 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-791 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-791 .cluster text{fill:#F9FFFE;}#graph-791 .cluster span{color:#F9FFFE;}#graph-791 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-791 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-791 rect.text{fill:none;stroke-width:0;}#graph-791 .icon-shape,#graph-791 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-791 .icon-shape p,#graph-791 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-791 .icon-shape rect,#graph-791 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-791 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-791 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-791 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-791 .superAdmin&gt;*{fill:#1E3A8A!important;stroke:#1E40AF!important;color:#fff!important;}#graph-791 .superAdmin span{fill:#1E3A8A!important;stroke:#1E40AF!important;color:#fff!important;}#graph-791 .superAdmin tspan{fill:#fff!important;}#graph-791 .schoolAdmin&gt;*{fill:#3B82F6!important;stroke:#2563EB!important;color:#fff!important;}#graph-791 .schoolAdmin span{fill:#3B82F6!important;stroke:#2563EB!important;color:#fff!important;}#graph-791 .schoolAdmin tspan{fill:#fff!important;}#graph-791 .orgRoles&gt;*{fill:#10B981!important;stroke:#059669!important;color:#fff!important;}#graph-791 .orgRoles span{fill:#10B981!important;stroke:#059669!important;color:#fff!important;}#graph-791 .orgRoles tspan{fill:#fff!important;}#graph-791 .lecturer&gt;*{fill:#F59E0B!important;stroke:#D97706!important;color:#fff!important;}#graph-791 .lecturer span{fill:#F59E0B!important;stroke:#D97706!important;color:#fff!important;}#graph-791 .lecturer tspan{fill:#fff!important;}#graph-791 .student&gt;*{fill:#8B5CF6!important;stroke:#7C3AED!important;color:#fff!important;}#graph-791 .student span{fill:#8B5CF6!important;stroke:#7C3AED!important;color:#fff!important;}#graph-791 .student tspan{fill:#fff!important;}</style><g><marker id="graph-791_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-791_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-791_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-791_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-791_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-791_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M1719.088,58.289L1614.97,69.074C1510.852,79.86,1302.617,101.43,1198.499,117.715C1094.382,134,1094.382,145,1094.382,150.5L1094.382,156" id="L_SUPER_SCHOOL_ADMIN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M998.637,206.571L854.411,217.976C710.185,229.381,421.733,252.19,277.507,269.095C133.281,286,133.281,297,133.281,302.5L133.281,308" id="L_SCHOOL_ADMIN_HOD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M998.637,209.719L901.457,220.599C804.276,231.48,609.915,253.24,512.735,269.62C415.555,286,415.555,297,415.555,302.5L415.555,308" id="L_SCHOOL_ADMIN_COORD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M998.637,216.909L946.876,226.591C895.115,236.273,791.593,255.636,739.831,270.818C688.07,286,688.07,297,688.07,302.5L688.07,308" id="L_SCHOOL_ADMIN_ICT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1013.253,238L1000.424,244.167C987.596,250.333,961.94,262.667,949.112,274.333C936.284,286,936.284,297,936.284,302.5L936.284,308" id="L_SCHOOL_ADMIN_STEM_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1140.115,238L1147.346,244.167C1154.577,250.333,1169.04,262.667,1176.271,274.333C1183.503,286,1183.503,297,1183.503,302.5L1183.503,308" id="L_SCHOOL_ADMIN_SUPERVISOR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1190.127,225.013L1220.79,233.345C1251.453,241.676,1312.78,258.338,1343.443,279.336C1374.107,300.333,1374.107,325.667,1374.107,351C1374.107,376.333,1374.107,401.667,1294.338,424.999C1214.569,448.332,1055.03,469.663,975.261,480.329L895.492,490.995" id="L_SCHOOL_ADMIN_LECTURER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1190.127,218.357L1236.823,227.797C1283.519,237.238,1376.911,256.119,1423.607,278.226C1470.304,300.333,1470.304,325.667,1470.304,351C1470.304,376.333,1470.304,401.667,1470.304,427C1470.304,452.333,1470.304,477.667,1470.304,503C1470.304,528.333,1470.304,553.667,1437.5,575.023C1404.696,596.379,1339.089,613.758,1306.285,622.447L1273.481,631.137" id="L_SCHOOL_ADMIN_STUDENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M88.866,390L81.844,396.167C74.821,402.333,60.775,414.667,165.283,432.001C269.79,449.336,492.851,471.672,604.382,482.84L715.912,494.008" id="L_HOD_LECTURER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M415.555,390L415.555,396.167C415.555,402.333,415.555,414.667,465.623,430.586C515.692,446.506,615.829,466.012,665.897,475.765L715.966,485.518" id="L_COORD_LECTURER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1183.503,390L1183.503,396.167C1183.503,402.333,1183.503,414.667,1135.494,430.491C1087.485,446.316,991.467,465.632,943.458,475.289L895.449,484.947" id="L_SUPERVISOR_LECTURER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M719.892,519.738L669.253,529.615C618.614,539.492,517.335,559.246,579.552,580.301C641.77,601.355,867.483,623.711,980.34,634.889L1093.197,646.066" id="L_LECTURER_STUDENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1828.072,86L1828.072,92.167C1828.072,98.333,1828.072,110.667,1828.072,129.492C1828.072,148.317,1828.072,173.633,1828.072,186.292L1828.072,198.95" id="SUPER-cyclic-special-1" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1828.072,199.05L1828.072,211.708C1828.072,224.367,1828.072,249.683,1836.12,275C1844.168,300.317,1860.264,325.633,1868.312,338.292L1876.36,350.95" id="SUPER-cyclic-special-mid" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1876.424,350.95L1884.472,338.292C1892.52,325.633,1908.616,300.317,1916.664,274.992C1924.713,249.667,1924.713,224.333,1924.713,199C1924.713,173.667,1924.713,148.333,1917.395,129.912C1910.078,111.491,1895.443,99.982,1888.125,94.227L1880.808,88.473" id="SUPER-cyclic-special-2" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1190.127,212.891L1261.473,223.243C1332.819,233.594,1475.512,254.297,1546.858,277.307C1618.205,300.317,1618.205,325.633,1618.205,338.292L1618.205,350.95" id="SCHOOL_ADMIN-cyclic-special-1" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1618.205,351.05L1618.205,363.708C1618.205,376.367,1618.205,401.683,1624.952,427C1631.699,452.317,1645.193,477.633,1651.94,490.292L1658.687,502.95" id="SCHOOL_ADMIN-cyclic-special-mid" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1658.75,502.95L1668.04,490.292C1677.329,477.633,1695.907,452.317,1705.197,426.992C1714.486,401.667,1714.486,376.333,1714.486,351C1714.486,325.667,1714.486,300.333,1627.754,277.037C1541.023,253.74,1367.56,232.481,1280.828,221.851L1194.097,211.221" id="SCHOOL_ADMIN-cyclic-special-2" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M118.213,390L115.83,396.167C113.447,402.333,108.682,414.667,106.299,433.492C103.917,452.317,103.917,477.633,103.917,490.292L103.917,502.95" id="HOD-cyclic-special-1" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M103.917,503.05L103.917,515.708C103.917,528.367,103.917,553.683,113.57,579C123.223,604.317,142.53,629.633,152.184,642.292L161.837,654.95" id="HOD-cyclic-special-mid" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M161.913,654.95L171.567,642.292C181.22,629.633,200.527,604.317,210.18,578.992C219.833,553.667,219.833,528.333,219.833,503C219.833,477.667,219.833,452.333,213.311,433.94C206.79,415.546,193.746,404.093,187.224,398.366L180.702,392.639" id="HOD-cyclic-special-2" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M891.528,523.201L931.035,532.501C970.543,541.801,1049.558,560.4,1073.705,582.36C1097.853,604.32,1067.133,629.639,1051.773,642.299L1036.413,654.959" id="LECTURER-cyclic-special-1" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1036.363,655.05L1036.363,667.708C1036.363,680.367,1036.363,705.683,1048.639,724.512C1060.916,743.342,1085.469,755.683,1097.745,761.854L1110.022,768.025" id="LECTURER-cyclic-special-mid" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1110.122,768.045L1166.819,761.87C1223.516,755.696,1336.91,743.348,1393.607,724.507C1450.304,705.667,1450.304,680.333,1450.304,655C1450.304,629.667,1450.304,604.333,1357.836,580.764C1265.369,557.196,1080.435,535.391,987.967,524.489L895.5,513.587" id="LECTURER-cyclic-special-2" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path><path d="M1157.884,694L1153.85,700.167C1149.816,706.333,1141.749,718.667,1125.438,731.004C1109.128,743.342,1084.575,755.683,1072.298,761.854L1060.022,768.025" id="STUDENT-cyclic-special-1" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1059.972,768.1L1059.972,774.267C1059.972,780.433,1059.972,792.767,1080.534,805.106C1101.097,817.445,1142.221,829.79,1162.784,835.962L1183.346,842.135" id="STUDENT-cyclic-special-mid" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style=""></path><path d="M1183.446,842.141L1216.154,835.967C1248.862,829.794,1314.278,817.447,1346.986,805.098C1379.694,792.75,1379.694,780.4,1379.694,768.05C1379.694,755.7,1379.694,743.35,1361.969,730.313C1344.244,717.275,1308.795,703.55,1291.07,696.688L1273.345,689.825" id="STUDENT-cyclic-special-2" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-791_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel" transform="translate(1094.3817714694887, 123)"><g class="label" transform="translate(-58.84375, -12)"><foreignObject width="117.6875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Manages Schools</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(133.28125762939453, 275)"><g class="label" transform="translate(-48.234375, -12)"><foreignObject width="96.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Creates Roles</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(415.5546989440918, 275)"><g class="label" transform="translate(-48.234375, -12)"><foreignObject width="96.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Creates Roles</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(688.0703239440918, 275)"><g class="label" transform="translate(-48.234375, -12)"><foreignObject width="96.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Creates Roles</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(936.283863067627, 275)"><g class="label" transform="translate(-48.234375, -12)"><foreignObject width="96.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Creates Roles</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1183.502613067627, 275)"><g class="label" transform="translate(-48.234375, -12)"><foreignObject width="96.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Creates Roles</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1374.1067810058594, 351)"><g class="label" transform="translate(-51.61979293823242, -12)"><foreignObject width="103.23958587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Manages Users</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1470.303656006232, 427)"><g class="label" transform="translate(-51.61979293823242, -12)"><foreignObject width="103.23958587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Manages Users</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(46.72917175292969, 427)"><g class="label" transform="translate(-37.1875, -12)"><foreignObject width="74.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Supervises</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(415.5546989440918, 427)"><g class="label" transform="translate(-42.567710876464844, -12)"><foreignObject width="85.13542175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Coordinates</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1183.502613067627, 427)"><g class="label" transform="translate(-30.4375, -12)"><foreignObject width="60.875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Monitors</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(416.0562595371157, 579)"><g class="label" transform="translate(-28.151042938232422, -12)"><foreignObject width="56.302085876464844" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Teaches</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1828.071882629767, 275)"><g class="label" transform="translate(-76.640625, -12)"><foreignObject width="153.28125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Platform-wide Access</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1618.2046989444643, 427)"><g class="label" transform="translate(-76.28125, -12)"><foreignObject width="152.5625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>School-scoped Access</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(103.91667175292969, 579)"><g class="label" transform="translate(-95.91667175292969, -12)"><foreignObject width="191.83334350585938" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Department-scoped Access</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1036.3630233760923, 731)"><g class="label" transform="translate(-77.31771087646484, -12)"><foreignObject width="154.6354217529297" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Course-scoped Access</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1059.9718788135797, 805.1000000014901)"><g class="label" transform="translate(-74.36979675292969, -12)"><foreignObject width="148.73959350585938" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Personal Data Access</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default superAdmin" id="flowchart-SUPER-0" transform="translate(1828.071882629767, 47)"><rect class="basic label-container" style="fill:#1E3A8A !important;stroke:#1E40AF !important" x="-108.984375" y="-39" width="217.96875" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-78.984375, -24)"><rect></rect><foreignObject width="157.96875" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Super Admin<br/>Platform Management</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-SCHOOL_ADMIN-1" transform="translate(1094.3817714694887, 199)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-95.74479675292969" y="-39" width="191.48959350585938" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-65.74479675292969, -24)"><rect></rect><foreignObject width="131.48959350585938" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>School Admin<br/>Institution Control</p></span></div></foreignObject></g></g><g class="node default orgRoles" id="flowchart-HOD-2" transform="translate(133.28125762939453, 351)"><rect class="basic label-container" style="fill:#10B981 !important;stroke:#059669 !important" x="-103.08854675292969" y="-39" width="206.17709350585938" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-73.08854675292969, -24)"><rect></rect><foreignObject width="146.17709350585938" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Head of Department<br/>Academic Oversight</p></span></div></foreignObject></g></g><g class="node default orgRoles" id="flowchart-COORD-3" transform="translate(415.5546989440918, 351)"><rect class="basic label-container" style="fill:#10B981 !important;stroke:#059669 !important" x="-117.53646087646484" y="-39" width="235.0729217529297" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-87.53646087646484, -24)"><rect></rect><foreignObject width="175.0729217529297" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Department Coordinator<br/>Operations Management</p></span></div></foreignObject></g></g><g class="node default orgRoles" id="flowchart-ICT-4" transform="translate(688.0703239440918, 351)"><rect class="basic label-container" style="fill:#10B981 !important;stroke:#059669 !important" x="-104.97917175292969" y="-39" width="209.95834350585938" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-74.97917175292969, -24)"><rect></rect><foreignObject width="149.95834350585938" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>ICT Club Coordinator<br/>Technology Activities</p></span></div></foreignObject></g></g><g class="node default orgRoles" id="flowchart-STEM-5" transform="translate(936.283863067627, 351)"><rect class="basic label-container" style="fill:#10B981 !important;stroke:#059669 !important" x="-93.234375" y="-39" width="186.46875" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-63.234375, -24)"><rect></rect><foreignObject width="126.46875" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>STEM Coordinator<br/>Science Programs</p></span></div></foreignObject></g></g><g class="node default orgRoles" id="flowchart-SUPERVISOR-6" transform="translate(1183.502613067627, 351)"><rect class="basic label-container" style="fill:#10B981 !important;stroke:#059669 !important" x="-103.984375" y="-39" width="207.96875" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-73.984375, -24)"><rect></rect><foreignObject width="147.96875" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Academic Supervisor<br/>Quality Assurance</p></span></div></foreignObject></g></g><g class="node default lecturer" id="flowchart-LECTURER-7" transform="translate(805.7098983768374, 503)"><rect class="basic label-container" style="fill:#F59E0B !important;stroke:#D97706 !important" x="-85.81771087646484" y="-39" width="171.6354217529297" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-55.817710876464844, -24)"><rect></rect><foreignObject width="111.63542175292969" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Lecturer<br/>Course Delivery</p></span></div></foreignObject></g></g><g class="node default student" id="flowchart-STUDENT-8" transform="translate(1183.3958435058594, 655)"><rect class="basic label-container" style="fill:#8B5CF6 !important;stroke:#7C3AED !important" x="-86.21875" y="-39" width="172.4375" height="78"></rect><g class="label" style="color:#fff !important" transform="translate(-56.21875, -24)"><rect></rect><foreignObject width="112.4375" height="48"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Student<br/>Learning Access</p></span></div></foreignObject></g></g><g class="label edgeLabel" id="SUPER---SUPER---1" transform="translate(1828.071882629767, 199)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="SUPER---SUPER---2" transform="translate(1876.392195129767, 351)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="SCHOOL_ADMIN---SCHOOL_ADMIN---1" transform="translate(1618.2046989444643, 351)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="SCHOOL_ADMIN---SCHOOL_ADMIN---2" transform="translate(1658.713550567627, 503)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="HOD---HOD---1" transform="translate(103.91667175292969, 503)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="HOD---HOD---2" transform="translate(161.87500762939453, 655)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="LECTURER---LECTURER---1" transform="translate(1036.3630233760923, 655)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="LECTURER---LECTURER---2" transform="translate(1110.0718788150698, 768.0500000007451)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="STUDENT---STUDENT---1" transform="translate(1059.9718788135797, 768.0500000007451)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g><g class="label edgeLabel" id="STUDENT---STUDENT---2" transform="translate(1183.3958435058594, 842.1500000022352)"><rect width="0.1" height="0.1"></rect><g class="label" style="" transform="translate(0, 0)"><rect></rect><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 10px; text-align: center;"><span class="nodeLabel"></span></div></foreignObject></g></g></g></g></g></svg>