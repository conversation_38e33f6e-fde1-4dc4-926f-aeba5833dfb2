<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-882" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 2189.583251953125 1080.37890625" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-882{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-882 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-882 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-882 .error-icon{fill:#a44141;}#graph-882 .error-text{fill:#ddd;stroke:#ddd;}#graph-882 .edge-thickness-normal{stroke-width:1px;}#graph-882 .edge-thickness-thick{stroke-width:3.5px;}#graph-882 .edge-pattern-solid{stroke-dasharray:0;}#graph-882 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-882 .edge-pattern-dashed{stroke-dasharray:3;}#graph-882 .edge-pattern-dotted{stroke-dasharray:2;}#graph-882 .marker{fill:lightgrey;stroke:lightgrey;}#graph-882 .marker.cross{stroke:lightgrey;}#graph-882 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-882 p{margin:0;}#graph-882 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-882 .cluster-label text{fill:#F9FFFE;}#graph-882 .cluster-label span{color:#F9FFFE;}#graph-882 .cluster-label span p{background-color:transparent;}#graph-882 .label text,#graph-882 span{fill:#ccc;color:#ccc;}#graph-882 .node rect,#graph-882 .node circle,#graph-882 .node ellipse,#graph-882 .node polygon,#graph-882 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-882 .rough-node .label text,#graph-882 .node .label text,#graph-882 .image-shape .label,#graph-882 .icon-shape .label{text-anchor:middle;}#graph-882 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-882 .rough-node .label,#graph-882 .node .label,#graph-882 .image-shape .label,#graph-882 .icon-shape .label{text-align:center;}#graph-882 .node.clickable{cursor:pointer;}#graph-882 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-882 .arrowheadPath{fill:lightgrey;}#graph-882 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-882 .flowchart-link{stroke:lightgrey;fill:none;}#graph-882 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-882 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-882 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-882 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-882 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-882 .cluster text{fill:#F9FFFE;}#graph-882 .cluster span{color:#F9FFFE;}#graph-882 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-882 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-882 rect.text{fill:none;stroke-width:0;}#graph-882 .icon-shape,#graph-882 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-882 .icon-shape p,#graph-882 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-882 .icon-shape rect,#graph-882 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-882 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-882 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-882 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-882 .frontend&gt;*{fill:#E3F2FD!important;stroke:#1976D2!important;color:#000!important;}#graph-882 .frontend span{fill:#E3F2FD!important;stroke:#1976D2!important;color:#000!important;}#graph-882 .frontend tspan{fill:#000!important;}#graph-882 .backend&gt;*{fill:#E8F5E8!important;stroke:#388E3C!important;color:#000!important;}#graph-882 .backend span{fill:#E8F5E8!important;stroke:#388E3C!important;color:#000!important;}#graph-882 .backend tspan{fill:#000!important;}#graph-882 .database&gt;*{fill:#FFF3E0!important;stroke:#F57C00!important;color:#000!important;}#graph-882 .database span{fill:#FFF3E0!important;stroke:#F57C00!important;color:#000!important;}#graph-882 .database tspan{fill:#000!important;}#graph-882 .external&gt;*{fill:#FCE4EC!important;stroke:#C2185B!important;color:#000!important;}#graph-882 .external span{fill:#FCE4EC!important;stroke:#C2185B!important;color:#000!important;}#graph-882 .external tspan{fill:#000!important;}</style><g><marker id="graph-882_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-882_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-882_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-882_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-882_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-882_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"><g class="cluster" id="subGraph3" data-look="classic"><rect style="" x="8" y="944.3790130615234" width="1419.374994277954" height="128"></rect><g class="cluster-label" transform="translate(656.3229112625122, 944.3790130615234)"><foreignObject width="122.72917175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>External Services</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph2" data-look="classic"><rect style="" x="690.7525978088379" y="716" width="776.5599002838135" height="154.37901306152344"></rect><g class="cluster-label" transform="translate(1024.2044229507446, 716)"><foreignObject width="109.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Database Layer</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph1" data-look="classic"><rect style="" x="33.16145706176758" y="8" width="1392.255205154419" height="634"></rect><g class="cluster-label" transform="translate(667.8775987625122, 8)"><foreignObject width="122.82292175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Backend Services</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph0" data-look="classic"><rect style="" x="1445.4166622161865" y="185" width="736.1666717529297" height="128"></rect><g class="cluster-label" transform="translate(1759.0781230926514, 185)"><foreignObject width="108.84375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Frontend Layer</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path d="M1572.536,288L1572.536,292.167C1572.536,296.333,1572.536,304.667,1386.201,315C1199.865,325.333,827.194,337.667,708.838,353.963C590.482,370.259,726.44,390.518,794.419,400.647L862.398,410.777" id="L_WEB_API_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M1819.083,288L1819.083,292.167C1819.083,296.333,1819.083,304.667,1666.327,315C1513.571,325.333,1208.059,337.667,1059.913,349.483C911.768,361.3,920.989,372.601,925.599,378.251L930.21,383.901" id="L_MOBILE_API_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M2060.047,288L2060.047,292.167C2060.047,296.333,2060.047,304.667,1942.605,315C1825.163,325.333,1590.28,337.667,1424.721,353.19C1259.163,368.713,1162.93,387.426,1114.814,396.783L1066.697,406.139" id="L_RESPONSIVE_API_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M1062.771,463.775L1079.334,470.146C1095.898,476.516,1129.024,489.258,1145.588,501.129C1162.151,513,1162.151,524,1162.151,529.5L1162.151,535" id="L_API_AUTH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M881.488,465L868.353,471.167C855.217,477.333,828.946,489.667,815.81,508.5C802.674,527.333,802.674,552.667,802.674,576C802.674,599.333,802.674,620.667,802.674,637.5C802.674,654.333,802.674,666.667,802.674,679C802.674,691.333,802.674,703.667,802.674,713.333C802.674,723,802.674,730,802.674,733.5L802.674,737" id="L_API_DB_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M964.562,465L964.562,471.167C964.562,477.333,964.562,489.667,964.562,508.5C964.562,527.333,964.562,552.667,964.562,576C964.562,599.333,964.562,620.667,964.562,637.5C964.562,654.333,964.562,666.667,964.562,679C964.562,691.333,964.562,703.667,967.671,714.175C970.78,724.683,976.998,733.366,980.107,737.707L983.216,742.048" id="L_API_CACHE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M1062.771,445.021L1111.803,454.517C1160.835,464.014,1258.899,483.007,1307.931,505.17C1356.964,527.333,1356.964,552.667,1356.964,576C1356.964,599.333,1356.964,620.667,1356.964,637.5C1356.964,654.333,1356.964,666.667,1356.964,679C1356.964,691.333,1356.964,703.667,1356.964,713.365C1356.964,723.064,1356.964,730.128,1356.964,733.66L1356.964,737.192" id="L_API_AUDIT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M866.354,445.685L819.53,455.071C772.705,464.457,679.056,483.228,632.231,498.114C585.406,513,585.406,524,585.406,529.5L585.406,535" id="L_API_STORAGE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M1162.151,617L1162.151,621.167C1162.151,625.333,1162.151,633.667,1162.151,644C1162.151,654.333,1162.151,666.667,1162.151,679C1162.151,691.333,1162.151,703.667,1150.747,716.273C1139.343,728.878,1116.536,741.757,1105.132,748.196L1093.728,754.635" id="L_AUTH_CACHE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M433.953,86.135L512.856,96.446C591.76,106.757,749.566,127.378,828.469,143.856C907.372,160.333,907.372,172.667,1002.216,187.959C1097.06,203.251,1286.747,221.502,1381.591,230.628L1476.435,239.753" id="L_NOTIFY_WEB_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M334.882,111L336.321,117.167C337.76,123.333,340.638,135.667,342.077,148C343.516,160.333,343.516,172.667,343.516,189.5C343.516,206.333,343.516,227.667,343.516,249C343.516,270.333,343.516,291.667,343.516,308.5C343.516,325.333,343.516,337.667,343.516,356.5C343.516,375.333,343.516,400.667,343.516,426C343.516,451.333,343.516,476.667,343.516,502C343.516,527.333,343.516,552.667,343.516,576C343.516,599.333,343.516,620.667,343.516,637.5C343.516,654.333,343.516,666.667,343.516,679C343.516,691.333,343.516,703.667,343.516,722.698C343.516,741.73,343.516,767.46,343.516,793.19C343.516,818.919,343.516,844.649,343.516,863.681C343.516,882.712,343.516,895.046,343.516,907.379C343.516,919.712,343.516,932.046,343.516,941.712C343.516,951.379,343.516,958.379,343.516,961.879L343.516,965.379" id="L_NOTIFY_FCM_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M220.709,111L204.096,117.167C187.482,123.333,154.254,135.667,137.64,148C121.026,160.333,121.026,172.667,121.026,189.5C121.026,206.333,121.026,227.667,121.026,249C121.026,270.333,121.026,291.667,121.026,308.5C121.026,325.333,121.026,337.667,121.026,356.5C121.026,375.333,121.026,400.667,121.026,426C121.026,451.333,121.026,476.667,121.026,502C121.026,527.333,121.026,552.667,121.026,576C121.026,599.333,121.026,620.667,121.026,637.5C121.026,654.333,121.026,666.667,121.026,679C121.026,691.333,121.026,703.667,121.026,722.698C121.026,741.73,121.026,767.46,121.026,793.19C121.026,818.919,121.026,844.649,121.026,863.681C121.026,882.712,121.026,895.046,121.026,907.379C121.026,919.712,121.026,932.046,121.026,941.712C121.026,951.379,121.026,958.379,121.026,961.879L121.026,965.379" id="L_NOTIFY_EMAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M585.406,617L585.406,621.167C585.406,625.333,585.406,633.667,585.406,644C585.406,654.333,585.406,666.667,585.406,679C585.406,691.333,585.406,703.667,585.406,722.698C585.406,741.73,585.406,767.46,585.406,793.19C585.406,818.919,585.406,844.649,585.406,863.681C585.406,882.712,585.406,895.046,585.406,907.379C585.406,919.712,585.406,932.046,585.406,941.712C585.406,951.379,585.406,958.379,585.406,961.879L585.406,965.379" id="L_STORAGE_CDN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M802.674,845.379L802.674,849.546C802.674,853.712,802.674,862.046,802.674,872.379C802.674,882.712,802.674,895.046,802.674,907.379C802.674,919.712,802.674,932.046,823.814,944.462C844.954,956.877,887.234,969.376,908.373,975.625L929.513,981.874" id="L_DB_BACKUP_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path><path d="M1356.964,845.187L1356.964,849.386C1356.964,853.584,1356.964,861.982,1356.964,872.347C1356.964,882.712,1356.964,895.046,1356.964,907.379C1356.964,919.712,1356.964,932.046,1315.624,946.045C1274.284,960.044,1191.604,975.709,1150.265,983.541L1108.925,991.374" id="L_AUDIT_BACKUP_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-882_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel" transform="translate(454.52342987060547, 350)"><g class="label" transform="translate(-50.020835876464844, -12)"><foreignObject width="100.04167175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>REST API Calls</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(902.5468673706055, 350)"><g class="label" transform="translate(-50.020835876464844, -12)"><foreignObject width="100.04167175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>REST API Calls</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1355.3958263397217, 350)"><g class="label" transform="translate(-50.020835876464844, -12)"><foreignObject width="100.04167175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>REST API Calls</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1162.1510372161865, 502)"><g class="label" transform="translate(-53.234375, -12)"><foreignObject width="106.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>JWT Validation</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(802.6744728088379, 578)"><g class="label" transform="translate(-60.828125, -12)"><foreignObject width="121.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>CRUD Operations</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(964.5624942779541, 578)"><g class="label" transform="translate(-51.22916793823242, -12)"><foreignObject width="102.45833587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Cache Queries</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1356.9635372161865, 578)"><g class="label" transform="translate(-48.453125, -12)"><foreignObject width="96.90625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Audit Logging</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(585.4062423706055, 502)"><g class="label" transform="translate(-54.49479293823242, -12)"><foreignObject width="108.98958587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>File Operations</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1162.1510372161865, 679)"><g class="label" transform="translate(-47.18229293823242, -12)"><foreignObject width="94.36458587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>User Sessions</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(907.3723888397217, 148)"><g class="label" transform="translate(-66.140625, -12)"><foreignObject width="132.28125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Real-time Updates</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(343.51561737060547, 502)"><g class="label" transform="translate(-51.1875, -12)"><foreignObject width="102.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Push Messages</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(121.02603912353516, 502)"><g class="label" transform="translate(-42.703125, -12)"><foreignObject width="85.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Email Alerts</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(585.4062423706055, 793.1895065307617)"><g class="label" transform="translate(-44.859375, -12)"><foreignObject width="89.71875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>File Delivery</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(802.6744728088379, 907.3790130615234)"><g class="label" transform="translate(-44.09375, -12)"><foreignObject width="88.1875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Backup Sync</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1356.9635372161865, 907.3790130615234)"><g class="label" transform="translate(-52.708335876464844, -12)"><foreignObject width="105.41667175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Secure Archive</p></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default frontend" id="flowchart-WEB-0" transform="translate(1572.5364513397217, 249)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#1976D2 !important" x="-92.11979293823242" y="-39" width="184.23958587646484" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-62.11979293823242, -24)"><rect></rect><foreignObject width="124.23958587646484" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Next.js Web App<br/>Admin Dashboard</p></span></div></foreignObject></g></g><g class="node default frontend" id="flowchart-MOBILE-1" transform="translate(1819.0833263397217, 249)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#1976D2 !important" x="-104.42708587646484" y="-39" width="208.8541717529297" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-74.42708587646484, -24)"><rect></rect><foreignObject width="148.8541717529297" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>React Native/Flutter<br/>Mobile App</p></span></div></foreignObject></g></g><g class="node default frontend" id="flowchart-RESPONSIVE-2" transform="translate(2060.0468730926514, 249)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#1976D2 !important" x="-86.53646087646484" y="-39" width="173.0729217529297" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-56.536460876464844, -24)"><rect></rect><foreignObject width="113.07292175292969" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Responsive Web<br/>All Users</p></span></div></foreignObject></g></g><g class="node default backend" id="flowchart-API-3" transform="translate(964.5624942779541, 426)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#388E3C !important" x="-98.20833587646484" y="-39" width="196.4166717529297" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-68.20833587646484, -24)"><rect></rect><foreignObject width="136.4166717529297" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Django REST API<br/>Laravel Alternative</p></span></div></foreignObject></g></g><g class="node default backend" id="flowchart-AUTH-4" transform="translate(1162.1510372161865, 578)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#388E3C !important" x="-111.359375" y="-39" width="222.71875" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-81.359375, -24)"><rect></rect><foreignObject width="162.71875" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Authentication Service<br/>Supabase Auth/JWT</p></span></div></foreignObject></g></g><g class="node default backend" id="flowchart-NOTIFY-5" transform="translate(325.7812385559082, 72)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#388E3C !important" x="-108.171875" y="-39" width="216.34375" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-78.171875, -24)"><rect></rect><foreignObject width="156.34375" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Notification Service<br/>Firebase + WebSocket</p></span></div></foreignObject></g></g><g class="node default backend" id="flowchart-STORAGE-6" transform="translate(585.4062423706055, 578)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#388E3C !important" x="-105.45833587646484" y="-39" width="210.9166717529297" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-75.45833587646484, -24)"><rect></rect><foreignObject width="150.9166717529297" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>File Storage Service<br/>Supabase Buckets/S3</p></span></div></foreignObject></g></g><g class="node default database" id="flowchart-DB-7" transform="translate(802.6744728088379, 793.1895065307617)"><path d="M0,13.793006836265832 a76.921875,13.793006836265832 0,0,0 153.84375,0 a76.921875,13.793006836265832 0,0,0 -153.84375,0 l0,76.79300683626583 a76.921875,13.793006836265832 0,0,0 153.84375,0 l0,-76.79300683626583" class="basic label-container" style="fill:#FFF3E0 !important;stroke:#F57C00 !important" transform="translate(-76.921875, -52.18951025439875)"></path><g class="label" style="color:#000 !important" transform="translate(-69.421875, -14)"><rect></rect><foreignObject width="138.84375" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>PostgreSQL<br/>Supabase/MongoDB</p></span></div></foreignObject></g></g><g class="node default database" id="flowchart-CACHE-8" transform="translate(1025.447910308838, 793.1895065307617)"><path d="M0,12.725543144715846 a64.796875,12.725543144715846 0,0,0 129.59375,0 a64.796875,12.725543144715846 0,0,0 -129.59375,0 l0,75.72554314471584 a64.796875,12.725543144715846 0,0,0 129.59375,0 l0,-75.72554314471584" class="basic label-container" style="fill:#FFF3E0 !important;stroke:#F57C00 !important" transform="translate(-64.796875, -50.58831471707377)"></path><g class="label" style="color:#000 !important" transform="translate(-57.296875, -14)"><rect></rect><foreignObject width="114.59375" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Redis Cache<br/>Session &amp; Query</p></span></div></foreignObject></g></g><g class="node default database" id="flowchart-AUDIT-9" transform="translate(1356.9635372161865, 793.1895065307617)"><path d="M0,13.665130371202036 a75.34896087646484,13.665130371202036 0,0,0 150.6979217529297,0 a75.34896087646484,13.665130371202036 0,0,0 -150.6979217529297,0 l0,76.66513037120204 a75.34896087646484,13.665130371202036 0,0,0 150.6979217529297,0 l0,-76.66513037120204" class="basic label-container" style="fill:#FFF3E0 !important;stroke:#F57C00 !important" transform="translate(-75.34896087646484, -51.99769555680305)"></path><g class="label" style="color:#000 !important" transform="translate(-67.84896087646484, -14)"><rect></rect><foreignObject width="135.6979217529297" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Audit Logs<br/>Immutable Storage</p></span></div></foreignObject></g></g><g class="node default external" id="flowchart-EMAIL-10" transform="translate(121.02603912353516, 1008.3790130615234)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-78.02604293823242" y="-39" width="156.05208587646484" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-48.02604293823242, -24)"><rect></rect><foreignObject width="96.05208587646484" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Email Service<br/>SMTP</p></span></div></foreignObject></g></g><g class="node default external" id="flowchart-FCM-11" transform="translate(343.51561737060547, 1008.3790130615234)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-94.46354675292969" y="-39" width="188.92709350585938" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-64.46354675292969, -24)"><rect></rect><foreignObject width="128.92709350585938" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Push Notifications<br/>Firebase FCM</p></span></div></foreignObject></g></g><g class="node default external" id="flowchart-CDN-12" transform="translate(585.4062423706055, 1008.3790130615234)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-97.42708587646484" y="-39" width="194.8541717529297" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-67.42708587646484, -24)"><rect></rect><foreignObject width="134.8541717529297" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>File CDN<br/>Global Distribution</p></span></div></foreignObject></g></g><g class="node default external" id="flowchart-BACKUP-13" transform="translate(1019.1718673706055, 1008.3790130615234)"><rect class="basic label-container" style="fill:#FCE4EC !important;stroke:#C2185B !important" x="-85.82291793823242" y="-39" width="171.64583587646484" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-55.82291793823242, -24)"><rect></rect><foreignObject width="111.64583587646484" height="48"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Backup Storage<br/>Offsite Security</p></span></div></foreignObject></g></g></g></g></g></svg>