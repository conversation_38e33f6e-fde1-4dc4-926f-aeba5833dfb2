from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    User, Role, UserRole, Permission, RolePermission,
    RoleTemplate, RoleAuditLog
)


class UserSerializer(serializers.ModelSerializer):
    """
    Serializer for User model with full details.
    """
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'first_name', 'last_name', 'middle_name',
            'full_name', 'date_of_birth', 'phone', 'school', 'user_type',
            'student_id', 'enrollment_date', 'graduation_date',
            'employee_id', 'hire_date', 'department', 'avatar', 'bio',
            'email_verified', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'full_name', 'email_verified'
        ]
        extra_kwargs = {
            'password': {'write_only': True}
        }
    
    def get_full_name(self, obj):
        """Get user's full name"""
        return obj.get_full_name()


class UserListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for User list views.
    """
    full_name = serializers.SerializerMethodField()
    school_name = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'email', 'full_name', 'user_type', 'school_name',
            'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'full_name', 'school_name']
    
    def get_full_name(self, obj):
        """Get user's full name"""
        return obj.get_full_name()
    
    def get_school_name(self, obj):
        """Get school name"""
        return obj.school.name if obj.school else None


class RoleSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for Role model with RBAC features.
    """
    school_name = serializers.CharField(source='school.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    parent_role_name = serializers.CharField(source='parent_role.name', read_only=True)
    permission_count = serializers.SerializerMethodField()
    user_count = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = [
            'id', 'name', 'description', 'role_type', 'school', 'school_name',
            'parent_role', 'parent_role_name', 'is_active', 'is_default',
            'is_system_role', 'created_by', 'created_by_name', 'created_at',
            'updated_at', 'permission_count', 'user_count'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']

    def get_permission_count(self, obj):
        """Get count of granted permissions for this role."""
        return obj.role_permissions.filter(granted=True).count()

    def get_user_count(self, obj):
        """Get count of active users assigned to this role."""
        return obj.user_roles.filter(is_active=True).count()


class UserRoleSerializer(serializers.ModelSerializer):
    """
    Serializer for UserRole model.
    """
    user_name = serializers.SerializerMethodField()
    role_name = serializers.SerializerMethodField()
    
    class Meta:
        model = UserRole
        fields = [
            'id', 'user', 'role', 'user_name', 'role_name',
            'assigned_at', 'assigned_by', 'valid_from', 'valid_until',
            'is_active', 'is_valid'
        ]
        read_only_fields = ['id', 'assigned_at', 'user_name', 'role_name', 'is_valid']
    
    def get_user_name(self, obj):
        """Get user's full name"""
        return obj.user.get_full_name()
    
    def get_role_name(self, obj):
        """Get role name"""
        return obj.role.name


class PermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for Permission model.
    """
    content_type_name = serializers.CharField(source='content_type.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = Permission
        fields = [
            'id', 'codename', 'name', 'description', 'category',
            'content_type', 'content_type_name', 'is_system_permission',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'created_by']


class RolePermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for RolePermission model.
    """
    permission_name = serializers.CharField(source='permission.name', read_only=True)
    permission_codename = serializers.CharField(source='permission.codename', read_only=True)
    granted_by_name = serializers.CharField(source='granted_by.get_full_name', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)

    class Meta:
        model = RolePermission
        fields = [
            'id', 'permission', 'permission_name', 'permission_codename',
            'granted', 'granted_by', 'granted_by_name', 'granted_at',
            'expires_at', 'is_expired'
        ]
        read_only_fields = ['id', 'granted_at', 'granted_by']


class RoleTemplateSerializer(serializers.ModelSerializer):
    """
    Serializer for RoleTemplate model.
    """
    permissions = PermissionSerializer(many=True, read_only=True)
    permission_count = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = RoleTemplate
        fields = [
            'id', 'name', 'description', 'permissions', 'permission_count',
            'is_active', 'is_system_template', 'usage_count', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'usage_count', 'created_at', 'updated_at']

    def get_permission_count(self, obj):
        """Get count of permissions in this template."""
        return obj.permissions.count()


class RoleAuditLogSerializer(serializers.ModelSerializer):
    """
    Serializer for RoleAuditLog model.
    """
    performed_by_name = serializers.CharField(source='performed_by.get_full_name', read_only=True)
    target_user_name = serializers.CharField(source='target_user.get_full_name', read_only=True)
    target_role_name = serializers.CharField(source='target_role.name', read_only=True)
    school_name = serializers.CharField(source='school.name', read_only=True)

    class Meta:
        model = RoleAuditLog
        fields = [
            'id', 'action_type', 'description', 'performed_by', 'performed_by_name',
            'target_user', 'target_user_name', 'target_role', 'target_role_name',
            'target_permission', 'school', 'school_name', 'ip_address',
            'user_agent', 'metadata', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
