"""
Debug script to check current admin user session
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_project.settings')
django.setup()

from django.contrib.sessions.models import Session
from django.contrib.auth import get_user_model
from django.utils import timezone
from users.rbac_utils import RBACManager

User = get_user_model()

print("🔍 DEBUGGING ADMIN USER SESSION")
print("=" * 50)

# Get all active sessions
active_sessions = Session.objects.filter(expire_date__gte=timezone.now())
print(f"Active sessions: {active_sessions.count()}")

for session in active_sessions:
    session_data = session.get_decoded()
    user_id = session_data.get('_auth_user_id')
    
    if user_id:
        try:
            user = User.objects.get(pk=user_id)
            print(f"\n👤 Session User: {user.email}")
            print(f"   Name: {user.first_name} {user.last_name}")
            print(f"   User Type: {user.user_type}")
            print(f"   School: {user.school}")
            print(f"   is_staff: {user.is_staff}")
            print(f"   is_active: {user.is_active}")
            print(f"   is_superuser: {user.is_superuser}")
            
            # Check RBAC permissions
            if user.school:
                permissions = RBACManager.get_user_permissions(user, user.school)
                print(f"   RBAC Permissions: {len(permissions)}")
                
                key_perms = ['can_manage_users', 'can_view_users', 'can_manage_school_settings']
                for perm in key_perms:
                    has_perm = RBACManager.user_has_permission(user, perm, user.school)
                    print(f"     {perm}: {'✅' if has_perm else '❌'}")
            
        except User.DoesNotExist:
            print(f"   User ID {user_id} not found")

print("\n" + "=" * 50)

# Check specific Greenwood admin
greenwood_admin = User.objects.filter(email='<EMAIL>').first()
if greenwood_admin:
    print(f"\n🏫 GREENWOOD ADMIN CHECK")
    print(f"Email: {greenwood_admin.email}")
    print(f"Display Name: {greenwood_admin.first_name} {greenwood_admin.last_name}")
    print(f"Username: {greenwood_admin.username if hasattr(greenwood_admin, 'username') else 'N/A'}")
    print(f"is_staff: {greenwood_admin.is_staff}")
    print(f"is_active: {greenwood_admin.is_active}")
    
    # Check if this user has any active sessions
    user_sessions = []
    for session in active_sessions:
        session_data = session.get_decoded()
        if session_data.get('_auth_user_id') == str(greenwood_admin.id):
            user_sessions.append(session)
    
    print(f"Active sessions for this user: {len(user_sessions)}")

print("\n" + "=" * 50)
print("🔧 SUGGESTED FIXES:")
print("1. Clear browser cache and cookies")
print("2. Logout and login again")
print("3. Check if correct user is logged in")
print("4. Restart Django server")
