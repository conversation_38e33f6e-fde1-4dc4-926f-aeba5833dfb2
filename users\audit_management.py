"""
Advanced audit management system with retention policies and monitoring.
"""

from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth import get_user_model
from typing import List, Dict, Any, Optional
import logging

from .models import RoleAuditLog, Role, UserRole

User = get_user_model()
logger = logging.getLogger(__name__)


class AuditManager:
    """
    Centralized audit management with retention policies and monitoring.
    """

    @staticmethod
    def create_audit_log(
        action_type: str,
        description: str,
        performed_by: Optional[User] = None,
        target_user: Optional[User] = None,
        target_role: Optional[Role] = None,
        target_permission=None,
        school=None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> RoleAuditLog:
        """
        Create a comprehensive audit log entry.
        """
        try:
            audit_log = RoleAuditLog.objects.create(
                action_type=action_type,
                description=description,
                performed_by=performed_by,
                target_user=target_user,
                target_role=target_role,
                target_permission=target_permission,
                school=school,
                ip_address=ip_address,
                user_agent=user_agent,
                metadata=metadata or {}
            )
            
            # Check for security alerts
            AuditManager._check_security_alerts(audit_log)
            
            return audit_log
            
        except Exception as e:
            logger.error(f"Failed to create audit log: {str(e)}")
            raise

    @staticmethod
    def cleanup_old_logs(retention_days: int = 365) -> int:
        """
        Clean up audit logs older than retention period.
        """
        cutoff_date = timezone.now() - timedelta(days=retention_days)
        
        # Count logs to be deleted
        old_logs = RoleAuditLog.objects.filter(created_at__lt=cutoff_date)
        count = old_logs.count()
        
        if count > 0:
            logger.info(f"Cleaning up {count} audit logs older than {retention_days} days")
            old_logs.delete()
            
            # Create audit log for cleanup action
            AuditManager.create_audit_log(
                action_type='audit_cleanup',
                description=f'Cleaned up {count} audit logs older than {retention_days} days',
                metadata={'retention_days': retention_days, 'deleted_count': count}
            )
        
        return count

    @staticmethod
    def get_audit_statistics(days: int = 30) -> Dict[str, Any]:
        """
        Get comprehensive audit statistics for the specified period.
        """
        since_date = timezone.now() - timedelta(days=days)
        
        # Base statistics
        total_logs = RoleAuditLog.objects.filter(created_at__gte=since_date).count()
        
        # Action type breakdown
        action_breakdown = RoleAuditLog.objects.filter(
            created_at__gte=since_date
        ).values('action_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # User activity
        user_activity = RoleAuditLog.objects.filter(
            created_at__gte=since_date,
            performed_by__isnull=False
        ).values(
            'performed_by__email',
            'performed_by__first_name',
            'performed_by__last_name'
        ).annotate(
            action_count=Count('id')
        ).order_by('-action_count')[:10]
        
        # School activity (if multi-tenant)
        school_activity = RoleAuditLog.objects.filter(
            created_at__gte=since_date,
            school__isnull=False
        ).values('school__name').annotate(
            action_count=Count('id')
        ).order_by('-action_count')[:10]
        
        # Security events
        security_events = RoleAuditLog.objects.filter(
            created_at__gte=since_date,
            action_type__in=['role_denied', 'permission_revoked', 'user_role_removed']
        ).count()
        
        # Daily activity trend
        daily_activity = []
        for i in range(days):
            day_start = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=i)
            day_end = day_start + timedelta(days=1)
            
            day_count = RoleAuditLog.objects.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).count()
            
            daily_activity.append({
                'date': day_start.date().isoformat(),
                'count': day_count
            })
        
        return {
            'period_days': days,
            'total_logs': total_logs,
            'action_breakdown': list(action_breakdown),
            'user_activity': list(user_activity),
            'school_activity': list(school_activity),
            'security_events': security_events,
            'daily_activity': daily_activity,
            'generated_at': timezone.now().isoformat()
        }

    @staticmethod
    def search_audit_logs(
        action_types: Optional[List[str]] = None,
        user_email: Optional[str] = None,
        target_user_email: Optional[str] = None,
        role_name: Optional[str] = None,
        school_name: Optional[str] = None,
        date_from: Optional[timezone.datetime] = None,
        date_to: Optional[timezone.datetime] = None,
        ip_address: Optional[str] = None,
        limit: int = 100
    ) -> List[RoleAuditLog]:
        """
        Advanced search functionality for audit logs.
        """
        queryset = RoleAuditLog.objects.all()
        
        # Apply filters
        if action_types:
            queryset = queryset.filter(action_type__in=action_types)
        
        if user_email:
            queryset = queryset.filter(performed_by__email__icontains=user_email)
        
        if target_user_email:
            queryset = queryset.filter(target_user__email__icontains=target_user_email)
        
        if role_name:
            queryset = queryset.filter(target_role__name__icontains=role_name)
        
        if school_name:
            queryset = queryset.filter(school__name__icontains=school_name)
        
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
        
        if date_to:
            queryset = queryset.filter(created_at__lte=date_to)
        
        if ip_address:
            queryset = queryset.filter(ip_address=ip_address)
        
        return queryset.select_related(
            'performed_by', 'target_user', 'target_role', 'school'
        ).order_by('-created_at')[:limit]

    @staticmethod
    def generate_compliance_report(
        start_date: timezone.datetime,
        end_date: timezone.datetime,
        school=None
    ) -> Dict[str, Any]:
        """
        Generate a comprehensive compliance report for audit purposes.
        """
        queryset = RoleAuditLog.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        if school:
            queryset = queryset.filter(Q(school=school) | Q(school__isnull=True))
        
        # Compliance metrics
        report = {
            'report_period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'school': school.name if school else 'All Schools'
            },
            'summary': {
                'total_audit_entries': queryset.count(),
                'unique_users': queryset.values('performed_by').distinct().count(),
                'unique_target_users': queryset.values('target_user').distinct().count(),
                'roles_affected': queryset.values('target_role').distinct().count(),
            },
            'action_breakdown': list(
                queryset.values('action_type').annotate(
                    count=Count('id')
                ).order_by('-count')
            ),
            'user_activity': list(
                queryset.filter(performed_by__isnull=False).values(
                    'performed_by__email',
                    'performed_by__first_name',
                    'performed_by__last_name'
                ).annotate(
                    action_count=Count('id')
                ).order_by('-action_count')[:20]
            ),
            'security_events': {
                'failed_assignments': queryset.filter(action_type='role_denied').count(),
                'permission_revocations': queryset.filter(action_type='permission_revoked').count(),
                'role_removals': queryset.filter(action_type='user_role_removed').count(),
            },
            'approval_workflow': {
                'approvals': queryset.filter(action_type='role_approved').count(),
                'denials': queryset.filter(action_type='role_denied').count(),
            },
            'data_integrity': {
                'logs_with_ip': queryset.filter(ip_address__isnull=False).count(),
                'logs_with_metadata': queryset.exclude(metadata={}).count(),
                'system_actions': queryset.filter(performed_by__isnull=True).count(),
            }
        }
        
        return report

    @staticmethod
    def _check_security_alerts(audit_log: RoleAuditLog):
        """
        Check for security alerts based on audit log patterns.
        """
        try:
            # Check for multiple failed attempts
            if audit_log.action_type == 'role_denied' and audit_log.performed_by:
                recent_failures = RoleAuditLog.objects.filter(
                    action_type='role_denied',
                    performed_by=audit_log.performed_by,
                    created_at__gte=timezone.now() - timedelta(hours=1)
                ).count()
                
                if recent_failures >= 5:
                    AuditManager._send_security_alert(
                        'Multiple Failed Role Assignments',
                        f'User {audit_log.performed_by.email} has {recent_failures} failed role assignments in the last hour.',
                        audit_log
                    )
            
            # Check for privilege escalation
            if (audit_log.action_type == 'user_role_assigned' and 
                audit_log.target_role and 
                audit_log.target_role.role_type == 'system'):
                
                AuditManager._send_security_alert(
                    'Privilege Escalation Detected',
                    f'System role "{audit_log.target_role.name}" assigned to user {audit_log.target_user.email if audit_log.target_user else "Unknown"}.',
                    audit_log
                )
            
            # Check for unusual activity volume
            if audit_log.performed_by:
                recent_activity = RoleAuditLog.objects.filter(
                    performed_by=audit_log.performed_by,
                    created_at__gte=timezone.now() - timedelta(hours=1)
                ).count()
                
                if recent_activity >= 50:
                    AuditManager._send_security_alert(
                        'Unusual Activity Volume',
                        f'User {audit_log.performed_by.email} has performed {recent_activity} actions in the last hour.',
                        audit_log
                    )
                    
        except Exception as e:
            logger.error(f"Error checking security alerts: {str(e)}")

    @staticmethod
    def _send_security_alert(alert_type: str, message: str, audit_log: RoleAuditLog):
        """
        Send security alert notifications.
        """
        try:
            # Log the security alert
            logger.warning(f"SECURITY ALERT - {alert_type}: {message}")
            
            # Create audit log for the alert
            AuditManager.create_audit_log(
                action_type='security_alert',
                description=f'{alert_type}: {message}',
                metadata={
                    'alert_type': alert_type,
                    'original_log_id': str(audit_log.id),
                    'severity': 'medium'
                }
            )
            
            # Send email notification if configured
            if hasattr(settings, 'SECURITY_ALERT_EMAILS') and settings.SECURITY_ALERT_EMAILS:
                try:
                    send_mail(
                        subject=f'SMS Security Alert: {alert_type}',
                        message=f'{message}\n\nTime: {audit_log.created_at}\nIP: {audit_log.ip_address or "Unknown"}',
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=settings.SECURITY_ALERT_EMAILS,
                        fail_silently=True
                    )
                except Exception as e:
                    logger.error(f"Failed to send security alert email: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error sending security alert: {str(e)}")


class AuditRetentionPolicy:
    """
    Manage audit log retention policies.
    """
    
    DEFAULT_RETENTION_DAYS = 365
    CRITICAL_ACTION_RETENTION_DAYS = 2555  # 7 years for critical actions
    
    CRITICAL_ACTIONS = [
        'role_created', 'role_deleted', 'user_role_assigned', 
        'user_role_removed', 'permission_granted', 'permission_revoked'
    ]

    @classmethod
    def apply_retention_policy(cls) -> Dict[str, int]:
        """
        Apply retention policy to audit logs.
        """
        results = {}
        
        # Clean up non-critical logs
        non_critical_cutoff = timezone.now() - timedelta(days=cls.DEFAULT_RETENTION_DAYS)
        non_critical_logs = RoleAuditLog.objects.filter(
            created_at__lt=non_critical_cutoff
        ).exclude(action_type__in=cls.CRITICAL_ACTIONS)
        
        results['non_critical_deleted'] = non_critical_logs.count()
        non_critical_logs.delete()
        
        # Clean up old critical logs
        critical_cutoff = timezone.now() - timedelta(days=cls.CRITICAL_ACTION_RETENTION_DAYS)
        critical_logs = RoleAuditLog.objects.filter(
            created_at__lt=critical_cutoff,
            action_type__in=cls.CRITICAL_ACTIONS
        )
        
        results['critical_deleted'] = critical_logs.count()
        critical_logs.delete()
        
        # Log retention policy application
        AuditManager.create_audit_log(
            action_type='retention_policy_applied',
            description=f'Applied retention policy: {results["non_critical_deleted"]} non-critical and {results["critical_deleted"]} critical logs deleted',
            metadata=results
        )
        
        return results
