"""
Management command to create comprehensive default user accounts for testing and development.
Supports both local SQLite and Supabase PostgreSQL databases.
"""

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.db import transaction
from django.contrib.auth.hashers import make_password
from schools.models import School
from users.models import Role, UserRole, RoleTemplate
from users.rbac_utils import RBACManager
import uuid
from datetime import date, datetime

User = get_user_model()


class Command(BaseCommand):
    help = 'Create default user accounts for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreation of existing users'
        )
        
        parser.add_argument(
            '--create-schools',
            action='store_true',
            help='Create test schools if they don\'t exist'
        )

    def handle(self, *args, **options):
        force = options['force']
        create_schools = options['create_schools']

        self.stdout.write(self.style.SUCCESS('Creating default user accounts...'))

        with transaction.atomic():
            # Create test schools if requested
            if create_schools:
                self.create_test_schools()

            # Create default users
            self.create_super_admin(force)
            self.create_school_admin(force)
            self.create_teacher(force)
            self.create_student(force)

        self.stdout.write(self.style.SUCCESS('Default user creation completed!'))
        self.display_credentials()

    def create_test_schools(self):
        """Create test schools for development."""
        self.stdout.write('Creating test schools...')

        from datetime import date

        # Test School 1
        school1, created = School.objects.get_or_create(
            name="Test School Primary",
            defaults={
                'slug': 'test-school-primary',
                'address_line_1': '123 Education Street',
                'city': 'Learning City',
                'state_province': 'Education State',
                'postal_code': '12345',
                'country': 'Test Country',
                'phone': '******-0123',
                'email': '<EMAIL>',
                'website': 'https://testschoolprimary.edu',
                'academic_year_start': date(2024, 9, 1),
                'academic_year_end': date(2025, 6, 30),
                'status': 'active',
                'subscription_plan': 'standard'
            }
        )
        if created:
            self.stdout.write(f'Created school: {school1.name}')

        # Test School 2
        school2, created = School.objects.get_or_create(
            name="Test School Secondary",
            defaults={
                'slug': 'test-school-secondary',
                'address_line_1': '456 Knowledge Avenue',
                'city': 'Study Town',
                'state_province': 'Learning State',
                'postal_code': '67890',
                'country': 'Test Country',
                'phone': '******-0456',
                'email': '<EMAIL>',
                'website': 'https://testschoolsecondary.edu',
                'academic_year_start': date(2024, 9, 1),
                'academic_year_end': date(2025, 6, 30),
                'status': 'active',
                'subscription_plan': 'premium'
            }
        )
        if created:
            self.stdout.write(f'Created school: {school2.name}')

    def create_super_admin(self, force=False):
        """Create super administrator account."""
        email = '<EMAIL>'
        
        if User.objects.filter(email=email).exists():
            if not force:
                self.stdout.write(f'Super admin {email} already exists. Use --force to recreate.')
                return
            else:
                User.objects.filter(email=email).delete()
                self.stdout.write(f'Deleted existing super admin: {email}')

        user = User.objects.create_user(
            email=email,
            password='admin123',
            first_name='System',
            last_name='Administrator',
            user_type='system',
            is_superuser=True,
            is_staff=True,
            is_active=True
        )
        
        self.stdout.write(self.style.SUCCESS(f'Created super admin: {email}'))
        return user

    def create_school_admin(self, force=False):
        """Create school administrator account."""
        email = '<EMAIL>'
        
        # Get or create a test school
        school = self.get_or_create_default_school()
        
        if User.objects.filter(email=email).exists():
            if not force:
                self.stdout.write(f'School admin {email} already exists. Use --force to recreate.')
                return
            else:
                User.objects.filter(email=email).delete()
                self.stdout.write(f'Deleted existing school admin: {email}')

        user = User.objects.create_user(
            email=email,
            password='school123',
            first_name='School',
            last_name='Administrator',
            user_type='admin',
            school=school,
            is_staff=True,
            is_active=True
        )

        # Create and assign school administrator role
        self.assign_school_admin_role(user, school)
        
        self.stdout.write(self.style.SUCCESS(f'Created school admin: {email}'))
        return user

    def create_teacher(self, force=False):
        """Create teacher account."""
        email = '<EMAIL>'
        
        # Get or create a test school
        school = self.get_or_create_default_school()
        
        if User.objects.filter(email=email).exists():
            if not force:
                self.stdout.write(f'Teacher {email} already exists. Use --force to recreate.')
                return
            else:
                User.objects.filter(email=email).delete()
                self.stdout.write(f'Deleted existing teacher: {email}')

        user = User.objects.create_user(
            email=email,
            password='teacher123',
            first_name='John',
            last_name='Teacher',
            user_type='teacher',
            school=school,
            is_active=True
        )

        # Create and assign teacher role
        self.assign_teacher_role(user, school)
        
        self.stdout.write(self.style.SUCCESS(f'Created teacher: {email}'))
        return user

    def create_student(self, force=False):
        """Create student account."""
        email = '<EMAIL>'
        
        # Get or create a test school
        school = self.get_or_create_default_school()
        
        if User.objects.filter(email=email).exists():
            if not force:
                self.stdout.write(f'Student {email} already exists. Use --force to recreate.')
                return
            else:
                User.objects.filter(email=email).delete()
                self.stdout.write(f'Deleted existing student: {email}')

        user = User.objects.create_user(
            email=email,
            password='student123',
            first_name='Jane',
            last_name='Student',
            user_type='student',
            school=school,
            is_active=True
        )

        # Create and assign student role
        self.assign_student_role(user, school)
        
        self.stdout.write(self.style.SUCCESS(f'Created student: {email}'))
        return user

    def get_or_create_default_school(self):
        """Get or create a default test school."""
        from datetime import date

        school, created = School.objects.get_or_create(
            name="Default Test School",
            defaults={
                'slug': 'default-test-school',
                'address_line_1': '789 Default Street',
                'city': 'Test City',
                'state_province': 'Default State',
                'postal_code': '00000',
                'country': 'Test Country',
                'phone': '******-0789',
                'email': '<EMAIL>',
                'website': 'https://defaulttestschool.edu',
                'academic_year_start': date(2024, 9, 1),
                'academic_year_end': date(2025, 6, 30),
                'status': 'active',
                'subscription_plan': 'basic'
            }
        )

        if created:
            self.stdout.write(f'Created default school: {school.name}')

        return school

    def assign_school_admin_role(self, user, school):
        """Assign school administrator role to user."""
        try:
            # Try to use existing template
            template = RoleTemplate.objects.filter(name__icontains='administrator').first()
            if template:
                role = RBACManager.create_role_from_template(
                    template_name=template.name,
                    role_name=f'{school.name} Administrator',
                    school=school,
                    created_by=user,
                    description=f'Administrator role for {school.name}'
                )
            else:
                # Create role manually
                role = Role.objects.create(
                    name=f'{school.name} Administrator',
                    description=f'Administrator role for {school.name}',
                    school=school,
                    role_type='school',
                    created_by=user
                )

            # Assign role to user
            RBACManager.assign_role_to_user(
                user=user,
                role=role,
                assigned_by=user,
                requires_approval=False
            )
            
            self.stdout.write(f'Assigned administrator role to {user.email}')
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'Could not assign administrator role: {str(e)}')
            )

    def assign_teacher_role(self, user, school):
        """Assign teacher role to user."""
        try:
            # Try to use existing template
            template = RoleTemplate.objects.filter(name__icontains='teacher').first()
            if template:
                role = RBACManager.create_role_from_template(
                    template_name=template.name,
                    role_name=f'{school.name} Teacher',
                    school=school,
                    created_by=user,
                    description=f'Teacher role for {school.name}'
                )
            else:
                # Create role manually
                role = Role.objects.create(
                    name=f'{school.name} Teacher',
                    description=f'Teacher role for {school.name}',
                    school=school,
                    role_type='school',
                    created_by=user
                )

            # Assign role to user
            RBACManager.assign_role_to_user(
                user=user,
                role=role,
                assigned_by=user,
                requires_approval=False
            )
            
            self.stdout.write(f'Assigned teacher role to {user.email}')
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'Could not assign teacher role: {str(e)}')
            )

    def assign_student_role(self, user, school):
        """Assign student role to user."""
        try:
            # Try to use existing template
            template = RoleTemplate.objects.filter(name__icontains='student').first()
            if template:
                role = RBACManager.create_role_from_template(
                    template_name=template.name,
                    role_name=f'{school.name} Student',
                    school=school,
                    created_by=user,
                    description=f'Student role for {school.name}'
                )
            else:
                # Create role manually
                role = Role.objects.create(
                    name=f'{school.name} Student',
                    description=f'Student role for {school.name}',
                    school=school,
                    role_type='school',
                    created_by=user
                )

            # Assign role to user
            RBACManager.assign_role_to_user(
                user=user,
                role=role,
                assigned_by=user,
                requires_approval=False
            )
            
            self.stdout.write(f'Assigned student role to {user.email}')
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'Could not assign student role: {str(e)}')
            )

    def display_credentials(self):
        """Display all created credentials."""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.SUCCESS('DEFAULT LOGIN CREDENTIALS'))
        self.stdout.write('='*60)
        
        credentials = [
            {
                'title': 'Super Administrator',
                'email': '<EMAIL>',
                'password': 'admin123',
                'access': 'Full system access across all schools'
            },
            {
                'title': 'School Administrator',
                'email': '<EMAIL>',
                'password': 'school123',
                'access': 'Full access within assigned school'
            },
            {
                'title': 'Teacher Account',
                'email': '<EMAIL>',
                'password': 'teacher123',
                'access': 'Student and class management'
            },
            {
                'title': 'Student Account',
                'email': '<EMAIL>',
                'password': 'student123',
                'access': 'Student portal access'
            }
        ]
        
        for cred in credentials:
            self.stdout.write(f"\n{cred['title']}:")
            self.stdout.write(f"  📧 Email: {cred['email']}")
            self.stdout.write(f"  🔒 Password: {cred['password']}")
            self.stdout.write(f"  🎯 Access: {cred['access']}")
        
        self.stdout.write('\n' + '='*60)
        self.stdout.write('🔗 Admin Dashboard: http://127.0.0.1:8000/admin/')
        self.stdout.write('📊 API Root: http://127.0.0.1:8000/api/')
        self.stdout.write('='*60)
        self.stdout.write(
            self.style.WARNING(
                '\n⚠️  SECURITY WARNING: Change these passwords in production!'
            )
        )
