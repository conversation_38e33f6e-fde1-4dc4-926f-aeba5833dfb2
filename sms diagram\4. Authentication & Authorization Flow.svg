<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-608" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 1219.28125 2394.61474609375" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-608{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-608 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-608 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-608 .error-icon{fill:#a44141;}#graph-608 .error-text{fill:#ddd;stroke:#ddd;}#graph-608 .edge-thickness-normal{stroke-width:1px;}#graph-608 .edge-thickness-thick{stroke-width:3.5px;}#graph-608 .edge-pattern-solid{stroke-dasharray:0;}#graph-608 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-608 .edge-pattern-dashed{stroke-dasharray:3;}#graph-608 .edge-pattern-dotted{stroke-dasharray:2;}#graph-608 .marker{fill:lightgrey;stroke:lightgrey;}#graph-608 .marker.cross{stroke:lightgrey;}#graph-608 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-608 p{margin:0;}#graph-608 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-608 .cluster-label text{fill:#F9FFFE;}#graph-608 .cluster-label span{color:#F9FFFE;}#graph-608 .cluster-label span p{background-color:transparent;}#graph-608 .label text,#graph-608 span{fill:#ccc;color:#ccc;}#graph-608 .node rect,#graph-608 .node circle,#graph-608 .node ellipse,#graph-608 .node polygon,#graph-608 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-608 .rough-node .label text,#graph-608 .node .label text,#graph-608 .image-shape .label,#graph-608 .icon-shape .label{text-anchor:middle;}#graph-608 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-608 .rough-node .label,#graph-608 .node .label,#graph-608 .image-shape .label,#graph-608 .icon-shape .label{text-align:center;}#graph-608 .node.clickable{cursor:pointer;}#graph-608 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-608 .arrowheadPath{fill:lightgrey;}#graph-608 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-608 .flowchart-link{stroke:lightgrey;fill:none;}#graph-608 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-608 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-608 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-608 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-608 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-608 .cluster text{fill:#F9FFFE;}#graph-608 .cluster span{color:#F9FFFE;}#graph-608 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-608 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-608 rect.text{fill:none;stroke-width:0;}#graph-608 .icon-shape,#graph-608 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-608 .icon-shape p,#graph-608 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-608 .icon-shape rect,#graph-608 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-608 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-608 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-608 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-608 .startEnd&gt;*{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-608 .startEnd span{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-608 .startEnd tspan{fill:#000!important;}#graph-608 .decision&gt;*{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-608 .decision span{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-608 .decision tspan{fill:#000!important;}#graph-608 .process&gt;*{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-608 .process span{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-608 .process tspan{fill:#000!important;}#graph-608 .error&gt;*{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-608 .error span{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-608 .error tspan{fill:#000!important;}</style><g><marker id="graph-608_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-608_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-608_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-608_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-608_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-608_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M254.08,47L247.743,51.167C241.405,55.333,228.731,63.667,222.465,71.417C216.198,79.167,216.338,86.334,216.409,89.917L216.479,93.501" id="L_START_VALIDATE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M174.587,236.374L163.103,249.452C151.619,262.53,128.651,288.687,117.241,307.349C105.831,326.011,105.98,337.177,106.055,342.761L106.129,348.344" id="L_VALIDATE_ATTEMPTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M106.182,547.708L106.099,553.792C106.016,559.875,105.849,572.042,105.766,592.023C105.682,612.005,105.682,639.802,105.682,653.701L105.682,667.599" id="L_ATTEMPTS_LOCKOUT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M177.941,475.95L228.033,493.993C278.126,512.036,378.31,548.122,428.403,585.23C478.495,622.339,478.495,660.469,478.495,698.599C478.495,736.729,478.495,774.859,478.495,804.591C478.495,834.323,478.495,855.656,478.495,876.99C478.495,898.323,478.495,919.656,478.495,946.994C478.495,974.332,478.495,1007.674,478.495,1041.016C478.495,1074.358,478.495,1107.7,487.601,1139.999C496.706,1172.298,514.918,1203.554,524.024,1219.182L533.129,1234.81" id="L_ATTEMPTS_LOGIN_FAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M601.118,1238.266L631.272,1222.062C661.425,1205.858,721.732,1173.45,751.886,1140.575C782.039,1107.7,782.039,1074.358,782.039,1041.016C782.039,1007.674,782.039,974.332,782.039,946.994C782.039,919.656,782.039,898.323,782.039,876.99C782.039,855.656,782.039,834.323,782.039,804.591C782.039,774.859,782.039,736.729,782.039,698.599C782.039,660.469,782.039,622.339,782.039,580.826C782.039,539.314,782.039,494.42,782.039,449.526C782.039,404.632,782.039,359.738,782.039,316.054C782.039,272.37,782.039,229.896,782.039,189.422C782.039,148.948,782.039,110.474,713.054,85.076C644.069,59.679,506.098,47.358,437.113,41.197L368.127,35.036" id="L_LOGIN_FAIL_START_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M105.682,725.599L105.682,740.164C105.682,754.729,105.682,783.859,105.682,805.174C105.682,826.49,105.682,839.99,105.682,846.74L105.682,853.49" id="L_LOCKOUT_END_FAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M261.915,232.986L275.647,246.629C289.379,260.272,316.843,287.558,330.652,309.203C344.461,330.847,344.615,346.851,344.692,354.853L344.769,362.854" id="L_VALIDATE_ACTIVE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M344.807,533.198L344.724,541.7C344.641,550.201,344.474,567.205,344.391,589.605C344.307,612.005,344.307,639.802,344.307,653.701L344.307,667.599" id="L_ACTIVE_SUSPENDED_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M344.307,725.599L344.307,740.164C344.307,754.729,344.307,783.859,317.298,805.668C290.289,827.478,236.27,841.965,209.261,849.209L182.252,856.453" id="L_SUSPENDED_END_FAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M400.898,477.108L437.957,494.958C475.017,512.808,549.136,548.508,586.27,571.942C623.404,595.375,623.553,606.542,623.627,612.125L623.702,617.709" id="L_ACTIVE_MFA_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M623.755,776.49L623.672,782.573C623.589,788.656,623.422,800.823,623.339,812.406C623.255,823.99,623.255,834.99,623.255,840.49L623.255,845.99" id="L_MFA_CHECK_MFA_CHALLENGE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M623.255,903.99L623.255,910.156C623.255,916.323,623.255,928.656,623.33,940.406C623.404,952.156,623.553,963.323,623.627,968.907L623.702,974.49" id="L_MFA_CHALLENGE_MFA_VALIDATE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M623.755,1104.542L623.672,1110.625C623.589,1116.708,623.422,1128.875,614.233,1150.586C605.044,1172.298,586.832,1203.554,577.726,1219.182L568.621,1234.81" id="L_MFA_VALIDATE_LOGIN_FAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M665.053,1063.244L689.772,1076.21C714.491,1089.177,763.929,1115.109,788.723,1133.659C813.516,1152.208,813.665,1163.375,813.739,1168.959L813.814,1174.542" id="L_MFA_VALIDATE_FIRST_LOGIN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M674.785,725.46L703.103,740.048C731.422,754.636,788.059,783.813,816.377,809.068C844.695,834.323,844.695,855.656,844.695,876.99C844.695,898.323,844.695,919.656,844.695,946.994C844.695,974.332,844.695,1007.674,844.695,1041.016C844.695,1074.358,844.695,1107.7,842.641,1132.9C840.587,1158.101,836.479,1175.16,834.425,1183.69L832.371,1192.22" id="L_MFA_CHECK_FIRST_LOGIN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M794.323,1333.446L791.518,1342.786C788.714,1352.127,783.104,1370.808,780.3,1385.649C777.495,1400.49,777.495,1411.49,777.495,1416.99L777.495,1422.49" id="L_FIRST_LOGIN_PASSWORD_CHANGE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M777.495,1480.49L777.495,1484.656C777.495,1488.823,777.495,1497.156,779.991,1504.941C782.486,1512.725,787.478,1519.961,789.974,1523.579L792.47,1527.197" id="L_PASSWORD_CHANGE_GENERATE_TOKEN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M857.079,1309.778L870.122,1323.063C883.164,1336.348,909.249,1362.919,922.291,1386.871C935.333,1410.823,935.333,1432.156,935.333,1451.49C935.333,1470.823,935.333,1488.156,926.174,1500.728C917.014,1513.3,898.695,1521.11,889.535,1525.016L880.375,1528.921" id="L_FIRST_LOGIN_GENERATE_TOKEN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M813.367,1584.49L813.367,1588.656C813.367,1592.823,813.367,1601.156,813.367,1608.823C813.367,1616.49,813.367,1623.49,813.367,1626.99L813.367,1630.49" id="L_GENERATE_TOKEN_LOAD_ROLES_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M813.367,1712.49L813.367,1716.656C813.367,1720.823,813.367,1729.156,813.367,1736.823C813.367,1744.49,813.367,1751.49,813.367,1754.99L813.367,1758.49" id="L_LOAD_ROLES_CREATE_SESSION_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M813.367,1816.49L813.367,1820.656C813.367,1824.823,813.367,1833.156,813.437,1840.906C813.508,1848.657,813.648,1855.823,813.719,1859.407L813.789,1862.99" id="L_CREATE_SESSION_ROUTE_DASHBOARD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M735.804,1988.051L666.087,2007.145C596.371,2026.239,456.938,2064.427,387.222,2089.021C317.505,2113.615,317.505,2124.615,317.505,2130.115L317.505,2135.615" id="L_ROUTE_DASHBOARD_SUPER_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M753.044,2005.291L727.362,2021.512C701.68,2037.732,650.317,2070.173,624.635,2091.894C598.953,2113.615,598.953,2124.615,598.953,2130.115L598.953,2135.615" id="L_ROUTE_DASHBOARD_SCHOOL_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M841.685,2038.297L845.791,2049.016C849.896,2059.736,858.107,2081.175,862.212,2097.395C866.318,2113.615,866.318,2124.615,866.318,2130.115L866.318,2135.615" id="L_ROUTE_DASHBOARD_LECTURER_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M882.321,1997.66L920.914,2015.153C959.506,2032.645,1036.69,2067.63,1075.283,2090.622C1113.875,2113.615,1113.875,2124.615,1113.875,2130.115L1113.875,2135.615" id="L_ROUTE_DASHBOARD_STUDENT_DASH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M317.505,2193.615L317.505,2197.781C317.505,2201.948,317.505,2210.281,379.966,2221.192C442.427,2232.103,567.349,2245.591,629.81,2252.335L692.271,2259.079" id="L_SUPER_DASH_AUDIT_LOG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M598.953,2193.615L598.953,2197.781C598.953,2201.948,598.953,2210.281,614.524,2218.493C630.094,2226.705,661.235,2234.795,676.805,2238.841L692.376,2242.886" id="L_SCHOOL_DASH_AUDIT_LOG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M866.318,2193.615L866.318,2197.781C866.318,2201.948,866.318,2210.281,861.46,2218.207C856.601,2226.132,846.885,2233.649,842.027,2237.408L837.168,2241.167" id="L_LECTURER_DASH_AUDIT_LOG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M1113.875,2193.615L1113.875,2197.781C1113.875,2201.948,1113.875,2210.281,1079.215,2220.174C1044.554,2230.066,975.233,2241.518,940.573,2247.244L905.913,2252.97" id="L_STUDENT_DASH_AUDIT_LOG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path><path d="M799.107,2297.615L799.107,2301.781C799.107,2305.948,799.107,2314.281,799.107,2321.948C799.107,2329.615,799.107,2336.615,799.107,2340.115L799.107,2343.615" id="L_AUDIT_LOG_END_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-608_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(105.68229675292969, 314.84375)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(105.68229675292969, 584.2083435058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(478.4947967529297, 876.9895935058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(344.3072967529297, 314.84375)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(344.3072967529297, 584.2083435058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(623.2552137374878, 584.2083435058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(623.2552137374878, 812.9895935058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(623.2552137374878, 1141.0416793823242)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(813.3671941757202, 1141.0416793823242)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(844.6953191757202, 940.9895935058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(777.494800567627, 1389.489601135254)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(935.3333425521851, 1453.489601135254)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(317.5052237510681, 2102.614601135254)"><g class="label" transform="translate(-44.58854293823242, -12)"><foreignObject width="89.17708587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Super Admin</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(598.9531378746033, 2102.614601135254)"><g class="label" transform="translate(-47.5625, -12)"><foreignObject width="95.125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>School Admin</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(866.3177161216736, 2102.614601135254)"><g class="label" transform="translate(-30.5, -12)"><foreignObject width="61" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Lecturer</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1113.8750052452087, 2102.614601135254)"><g class="label" transform="translate(-27.75520896911621, -12)"><foreignObject width="55.51041793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Student</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default startEnd" id="flowchart-START-0" transform="translate(283.73698806762695, 27.5)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-80.40625" y="-19.5" width="160.8125" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-68.03125, -12)"><rect></rect><foreignObject width="136.0625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>User Login Request</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-VALIDATE-1" transform="translate(216.05730056762695, 187.421875)"><polygon points="90.421875,0 180.84375,-90.421875 90.421875,-180.84375 0,-90.421875" class="label-container" transform="translate(-90.421875,90.421875)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-63.421875, -12)"><rect></rect><foreignObject width="126.84375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Valid Credentials?</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-ATTEMPTS-3" transform="translate(105.68229675292969, 449.5260467529297)"><polygon points="97.68229675292969,0 195.36459350585938,-97.68229675292969 97.68229675292969,-195.36459350585938 0,-97.68229675292969" class="label-container" transform="translate(-97.68229675292969,97.68229675292969)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-70.68229675292969, -12)"><rect></rect><foreignObject width="141.36459350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Too Many Attempts?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-LOCKOUT-5" transform="translate(105.68229675292969, 698.5989685058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-89.4375" y="-27" width="178.875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-59.4375, -12)"><rect></rect><foreignObject width="118.875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Account Lockout</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-LOGIN_FAIL-7" transform="translate(550.8750052452087, 1265.265640258789)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-76.3125" y="-27" width="152.625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-46.3125, -12)"><rect></rect><foreignObject width="92.625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Login Failure</p></span></div></foreignObject></g></g><g class="node default startEnd" id="flowchart-END_FAIL-11" transform="translate(105.68229675292969, 876.9895935058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-83.4375" y="-19.5" width="166.875" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-71.0625, -12)"><rect></rect><foreignObject width="142.125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>End - Access Denied</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-ACTIVE-13" transform="translate(344.3072967529297, 449.5260467529297)"><polygon points="83.171875,0 166.34375,-83.171875 83.171875,-166.34375 0,-83.171875" class="label-container" transform="translate(-83.171875,83.171875)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-56.171875, -12)"><rect></rect><foreignObject width="112.34375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Account Active?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-SUSPENDED-15" transform="translate(344.3072967529297, 698.5989685058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-99.1875" y="-27" width="198.375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-69.1875, -12)"><rect></rect><foreignObject width="138.375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Account Suspended</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-MFA_CHECK-19" transform="translate(623.2552137374878, 698.5989685058594)"><polygon points="77.390625,0 154.78125,-77.390625 77.390625,-154.78125 0,-77.390625" class="label-container" transform="translate(-77.390625,77.390625)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-50.390625, -12)"><rect></rect><foreignObject width="100.78125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>MFA Required?</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-MFA_CHALLENGE-21" transform="translate(623.2552137374878, 876.9895935058594)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-100.359375" y="-27" width="200.71875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-70.359375, -12)"><rect></rect><foreignObject width="140.71875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Send MFA Challenge</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-MFA_VALIDATE-23" transform="translate(623.2552137374878, 1041.0156364440918)"><polygon points="63.02604293823242,0 126.05208587646484,-63.02604293823242 63.02604293823242,-126.05208587646484 0,-63.02604293823242" class="label-container" transform="translate(-63.02604293823242,63.02604293823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-36.02604293823242, -12)"><rect></rect><foreignObject width="72.05208587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>MFA Valid?</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-FIRST_LOGIN-27" transform="translate(813.3671941757202, 1265.265640258789)"><polygon points="87.22396087646484,0 174.4479217529297,-87.22396087646484 87.22396087646484,-174.4479217529297 0,-87.22396087646484" class="label-container" transform="translate(-87.22396087646484,87.22396087646484)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-60.223960876464844, -12)"><rect></rect><foreignObject width="120.44792175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>First Time Login?</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-PASSWORD_CHANGE-31" transform="translate(777.494800567627, 1453.489601135254)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-113.4375" y="-27" width="226.875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-83.4375, -12)"><rect></rect><foreignObject width="166.875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Force Password Change</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-GENERATE_TOKEN-33" transform="translate(813.3671941757202, 1557.489601135254)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-103.875" y="-27" width="207.75" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-73.875, -12)"><rect></rect><foreignObject width="147.75" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Generate JWT Token</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-LOAD_ROLES-37" transform="translate(813.3671941757202, 1673.489601135254)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-130" y="-39" width="260" height="78"></rect><g class="label" style="color:#000 !important" transform="translate(-100, -24)"><rect></rect><foreignObject width="200" height="48"><div style="color: rgb(0, 0, 0) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Load User Roles &amp; Permissions</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-CREATE_SESSION-39" transform="translate(813.3671941757202, 1789.489601135254)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-100.34896087646484" y="-27" width="200.6979217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-70.34896087646484, -12)"><rect></rect><foreignObject width="140.6979217529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Create User Session</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-ROUTE_DASHBOARD-41" transform="translate(813.3671941757202, 1966.052101135254)"><polygon points="99.5625,0 199.125,-99.5625 99.5625,-199.125 0,-99.5625" class="label-container" transform="translate(-99.5625,99.5625)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-72.5625, -12)"><rect></rect><foreignObject width="145.125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Route Based on Role</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-SUPER_DASH-43" transform="translate(317.5052237510681, 2166.614601135254)"><rect class="basic label-container" style="" x="-114.234375" y="-27" width="228.46875" height="54"></rect><g class="label" style="" transform="translate(-84.234375, -12)"><rect></rect><foreignObject width="168.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Super Admin Dashboard</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-SCHOOL_DASH-45" transform="translate(598.9531378746033, 2166.614601135254)"><rect class="basic label-container" style="" x="-117.21354675292969" y="-27" width="234.42709350585938" height="54"></rect><g class="label" style="" transform="translate(-87.21354675292969, -12)"><rect></rect><foreignObject width="174.42709350585938" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>School Admin Dashboard</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-LECTURER_DASH-47" transform="translate(866.3177161216736, 2166.614601135254)"><rect class="basic label-container" style="" x="-100.15104675292969" y="-27" width="200.30209350585938" height="54"></rect><g class="label" style="" transform="translate(-70.15104675292969, -12)"><rect></rect><foreignObject width="140.30209350585938" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Lecturer Dashboard</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-STUDENT_DASH-49" transform="translate(1113.8750052452087, 2166.614601135254)"><rect class="basic label-container" style="" x="-97.40625" y="-27" width="194.8125" height="54"></rect><g class="label" style="" transform="translate(-67.40625, -12)"><rect></rect><foreignObject width="134.8125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Student Dashboard</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-AUDIT_LOG-51" transform="translate(799.1067848205566, 2270.614601135254)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-102.859375" y="-27" width="205.71875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-72.859375, -12)"><rect></rect><foreignObject width="145.71875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Log Successful Login</p></span></div></foreignObject></g></g><g class="node default startEnd" id="flowchart-END_SUCCESS-59" transform="translate(799.1067848205566, 2367.114601135254)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-81.65104675292969" y="-19.5" width="163.30209350585938" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-69.27604675292969, -12)"><rect></rect><foreignObject width="138.55209350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>End - Login Success</p></span></div></foreignObject></g></g></g></g></g></svg>