"""
Multi-tenant managers and querysets for RBAC models with school isolation.
"""

import logging
from django.db import models
from django.core.exceptions import ValidationError, PermissionDenied
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)


class SchoolIsolatedQuerySet(models.QuerySet):
    """
    Base queryset that provides school-based filtering for multi-tenant isolation.
    """
    
    def for_school(self, school):
        """
        Filter queryset to only include items for a specific school.
        
        Args:
            school: School instance or None for system-wide items
        """
        if school is None:
            # Return system-wide items (school is null)
            return self.filter(school__isnull=True)
        return self.filter(school=school)
    
    def for_user_school(self, user):
        """
        Filter queryset based on user's school context.
        
        Args:
            user: User instance
        """
        if user.is_superuser:
            # Super users can see all items
            return self
        
        if hasattr(user, 'school') and user.school:
            # Regular users can only see items from their school + system items
            return self.filter(
                models.Q(school=user.school) | models.Q(school__isnull=True)
            )
        
        # Users without school can only see system items
        return self.filter(school__isnull=True)
    
    def system_wide(self):
        """Return only system-wide items (school is null)."""
        return self.filter(school__isnull=True)
    
    def school_specific(self):
        """Return only school-specific items (school is not null)."""
        return self.filter(school__isnull=False)


class RoleQuerySet(SchoolIsolatedQuerySet):
    """
    Custom queryset for Role model with multi-tenant isolation.
    """
    
    def active(self):
        """Return only active roles."""
        return self.filter(is_active=True)
    
    def by_type(self, role_type):
        """Filter roles by type (system, school, custom)."""
        return self.filter(role_type=role_type)
    
    def system_roles(self):
        """Return only system roles."""
        return self.filter(role_type='system', school__isnull=True)
    
    def school_roles(self, school=None):
        """Return school-specific roles."""
        queryset = self.filter(role_type__in=['school', 'custom'], school__isnull=False)
        if school:
            queryset = queryset.filter(school=school)
        return queryset
    
    def assignable_to_user(self, user, target_user=None):
        """
        Return roles that a user can assign to others.
        
        Args:
            user: User who wants to assign roles
            target_user: User who will receive the role (optional)
        """
        if user.is_superuser:
            # Super users can assign any role
            return self.active()
        
        # Regular users can only assign roles within their school
        user_school = getattr(user, 'school', None)
        if not user_school:
            # Users without school can't assign roles
            return self.none()
        
        # Check if user has permission to assign roles
        from .rbac_utils import RBACManager
        if not RBACManager.user_has_permission(user, 'can_assign_roles', user_school):
            return self.none()
        
        # Filter to school-specific roles and system roles
        queryset = self.active().filter(
            models.Q(school=user_school) | models.Q(school__isnull=True)
        )
        
        # If target user is specified, ensure they're in the same school
        if target_user and hasattr(target_user, 'school'):
            if target_user.school != user_school and not user.is_superuser:
                return self.none()
        
        return queryset
    
    def comparable_across_schools(self, user):
        """
        Return roles that can be compared across schools (for super admins).
        
        Args:
            user: User requesting the comparison
        """
        if not user.is_superuser:
            # Only super users can compare across schools
            from .rbac_utils import RBACManager
            if not RBACManager.user_has_permission(user, 'can_view_all_schools'):
                return self.none()
        
        return self.active()


class RoleManager(models.Manager):
    """
    Custom manager for Role model with multi-tenant support.
    """
    
    def get_queryset(self):
        return RoleQuerySet(self.model, using=self._db)
    
    def for_school(self, school):
        return self.get_queryset().for_school(school)
    
    def for_user_school(self, user):
        return self.get_queryset().for_user_school(user)
    
    def system_roles(self):
        return self.get_queryset().system_roles()
    
    def school_roles(self, school=None):
        return self.get_queryset().school_roles(school)
    
    def assignable_to_user(self, user, target_user=None):
        return self.get_queryset().assignable_to_user(user, target_user)
    
    def create_school_role(self, name, school, created_by, **kwargs):
        """
        Create a new school-specific role with validation.
        
        Args:
            name: Role name
            school: School instance
            created_by: User creating the role
            **kwargs: Additional role fields
        """
        # Validate that user can create roles in this school
        if not created_by.is_superuser:
            user_school = getattr(created_by, 'school', None)
            if user_school != school:
                raise PermissionDenied("Cannot create role for different school")
            
            from .rbac_utils import RBACManager
            if not RBACManager.user_has_permission(created_by, 'can_assign_roles', school):
                raise PermissionDenied("No permission to create roles")
        
        # Set role type and school
        kwargs.update({
            'name': name,
            'school': school,
            'role_type': kwargs.get('role_type', 'school'),
            'created_by': created_by,
        })
        
        return self.create(**kwargs)
    
    def create_system_role(self, name, created_by, **kwargs):
        """
        Create a new system-wide role with validation.
        
        Args:
            name: Role name
            created_by: User creating the role
            **kwargs: Additional role fields
        """
        # Only super users can create system roles
        if not created_by.is_superuser:
            from .rbac_utils import RBACManager
            if not RBACManager.user_has_permission(created_by, 'can_manage_schools'):
                raise PermissionDenied("No permission to create system roles")
        
        kwargs.update({
            'name': name,
            'school': None,
            'role_type': 'system',
            'created_by': created_by,
        })
        
        return self.create(**kwargs)


class UserRoleQuerySet(SchoolIsolatedQuerySet):
    """
    Custom queryset for UserRole model with multi-tenant isolation.
    """
    
    def active(self):
        """Return only active user role assignments."""
        return self.filter(is_active=True)
    
    def for_user(self, user):
        """Return role assignments for a specific user."""
        return self.filter(user=user)
    
    def for_role(self, role):
        """Return user assignments for a specific role."""
        return self.filter(role=role)
    
    def pending_approval(self):
        """Return role assignments pending approval."""
        return self.filter(requires_approval=True, approved_at__isnull=True)
    
    def approved(self):
        """Return approved role assignments."""
        return self.filter(
            models.Q(requires_approval=False) | 
            models.Q(requires_approval=True, approved_at__isnull=False)
        )
    
    def for_school_context(self, user, school):
        """
        Return user role assignments within school context.
        
        Args:
            user: User requesting the data
            school: School context
        """
        if user.is_superuser:
            # Super users can see all assignments
            if school:
                return self.filter(role__school=school)
            return self
        
        # Regular users can only see assignments in their school
        user_school = getattr(user, 'school', None)
        if school and school != user_school:
            return self.none()
        
        return self.filter(
            models.Q(role__school=user_school) | 
            models.Q(role__school__isnull=True)
        )


class UserRoleManager(models.Manager):
    """
    Custom manager for UserRole model with multi-tenant support.
    """
    
    def get_queryset(self):
        return UserRoleQuerySet(self.model, using=self._db)
    
    def active(self):
        return self.get_queryset().active()
    
    def for_user(self, user):
        return self.get_queryset().for_user(user)
    
    def for_role(self, role):
        return self.get_queryset().for_role(role)
    
    def for_school_context(self, user, school):
        return self.get_queryset().for_school_context(user, school)
    
    def assign_role(self, user, role, assigned_by, **kwargs):
        """
        Assign a role to a user with multi-tenant validation.
        
        Args:
            user: User to assign role to
            role: Role to assign
            assigned_by: User performing the assignment
            **kwargs: Additional assignment fields
        """
        # Validate school context
        self._validate_role_assignment(user, role, assigned_by)
        
        # Check if assignment already exists
        existing = self.filter(user=user, role=role, is_active=True).first()
        if existing:
            raise ValidationError(f"User already has role '{role.name}'")
        
        # Create the assignment
        kwargs.update({
            'user': user,
            'role': role,
            'assigned_by': assigned_by,
        })
        
        return self.create(**kwargs)
    
    def _validate_role_assignment(self, user, role, assigned_by):
        """
        Validate that a role assignment is allowed in multi-tenant context.
        """
        # Super users can assign any role to anyone
        if assigned_by.is_superuser:
            return
        
        # Get school contexts
        user_school = getattr(user, 'school', None)
        role_school = role.school
        assigner_school = getattr(assigned_by, 'school', None)
        
        # Validate assigner has permission
        # Check if user has Django permission or is a school admin
        has_permission = (
            assigned_by.has_perm('users.can_assign_roles') or
            assigned_by.user_type in ['school_admin', 'super_admin'] or
            assigned_by.is_staff
        )
        if not has_permission:
            raise PermissionDenied("No permission to assign roles")
        
        # Validate school context alignment
        if role_school:  # School-specific role
            if user_school != role_school:
                raise ValidationError("Cannot assign school role to user from different school")
            if assigner_school != role_school:
                raise PermissionDenied("Cannot assign role from different school")
        
        # System roles can be assigned to anyone within the same school context
        if not role_school and user_school != assigner_school:
            raise PermissionDenied("Cannot assign system role to user from different school")


class SchoolContextValidator:
    """
    Utility class for validating multi-tenant school context operations.
    """
    
    @staticmethod
    def validate_user_school_access(requesting_user, target_user):
        """
        Validate that requesting user can access target user's data.
        
        Args:
            requesting_user: User making the request
            target_user: User whose data is being accessed
            
        Returns:
            bool: Whether access is allowed
        """
        if requesting_user.is_superuser:
            return True
        
        requesting_school = getattr(requesting_user, 'school', None)
        target_school = getattr(target_user, 'school', None)
        
        return requesting_school == target_school
    
    @staticmethod
    def validate_role_school_access(user, role):
        """
        Validate that user can access role data.
        
        Args:
            user: User making the request
            role: Role being accessed
            
        Returns:
            bool: Whether access is allowed
        """
        if user.is_superuser:
            return True
        
        user_school = getattr(user, 'school', None)
        role_school = role.school
        
        # Users can access system roles and roles from their school
        return role_school is None or role_school == user_school
    
    @staticmethod
    def get_accessible_schools(user):
        """
        Get list of schools that user can access.
        
        Args:
            user: User instance
            
        Returns:
            QuerySet: Schools the user can access
        """
        if user.is_superuser:
            from schools.models import School
            return School.objects.all()
        
        user_school = getattr(user, 'school', None)
        if user_school:
            from schools.models import School
            return School.objects.filter(id=user_school.id)
        
        from schools.models import School
        return School.objects.none()
