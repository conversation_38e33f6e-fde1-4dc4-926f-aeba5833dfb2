"""
Management command to test and troubleshoot Supabase database connectivity.
"""

from django.core.management.base import BaseCommand
from django.conf import settings
import os
import socket
import requests
import psycopg2
from urllib.parse import urlparse


class Command(BaseCommand):
    help = 'Test Supabase database connectivity and troubleshoot connection issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed connection information'
        )

    def handle(self, *args, **options):
        verbose = options['verbose']
        
        self.stdout.write(self.style.SUCCESS('🔍 Testing Supabase Connectivity...'))
        self.stdout.write('='*60)

        # Test 1: Environment Variables
        self.test_environment_variables(verbose)
        
        # Test 2: DNS Resolution
        self.test_dns_resolution(verbose)
        
        # Test 3: Network Connectivity
        self.test_network_connectivity(verbose)
        
        # Test 4: Supabase Management API
        self.test_supabase_api(verbose)
        
        # Test 5: Direct PostgreSQL Connection
        self.test_postgresql_connection(verbose)
        
        # Test 6: Django Database Connection
        self.test_django_connection(verbose)

        self.stdout.write('='*60)
        self.stdout.write(self.style.SUCCESS('🏁 Connectivity Test Complete'))

    def test_environment_variables(self, verbose):
        """Test if all required environment variables are set."""
        self.stdout.write('\n1️⃣ Testing Environment Variables...')
        
        required_vars = [
            'SUPABASE_DB_PASSWORD',
            'USE_POSTGRESQL'
        ]
        
        optional_vars = [
            'SUPABASE_URL',
            'SUPABASE_ANON_KEY',
            'SUPABASE_PROJECT_ID'
        ]
        
        all_good = True
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                self.stdout.write(f'  ✅ {var}: {"*" * len(value) if "PASSWORD" in var else value}')
            else:
                self.stdout.write(f'  ❌ {var}: Not set')
                all_good = False
        
        for var in optional_vars:
            value = os.getenv(var)
            if value:
                self.stdout.write(f'  ℹ️  {var}: {value}')
            else:
                self.stdout.write(f'  ⚠️  {var}: Not set (optional)')
        
        if verbose:
            self.stdout.write(f'  📋 USE_POSTGRESQL: {os.getenv("USE_POSTGRESQL")}')
            
        return all_good

    def test_dns_resolution(self, verbose):
        """Test DNS resolution for Supabase hostname."""
        self.stdout.write('\n2️⃣ Testing DNS Resolution...')
        
        hostname = 'db.fsznynpmqxljaijcuweb.supabase.co'
        
        try:
            ip_address = socket.gethostbyname(hostname)
            self.stdout.write(f'  ✅ DNS Resolution: {hostname} -> {ip_address}')
            return True
        except socket.gaierror as e:
            self.stdout.write(f'  ❌ DNS Resolution Failed: {str(e)}')
            
            # Try alternative DNS servers
            self.stdout.write('  🔄 Trying alternative DNS resolution...')
            try:
                import dns.resolver
                result = dns.resolver.resolve(hostname, 'A')
                for ip in result:
                    self.stdout.write(f'  ✅ Alternative DNS: {hostname} -> {ip}')
                return True
            except Exception as dns_e:
                self.stdout.write(f'  ❌ Alternative DNS also failed: {str(dns_e)}')
                
            return False

    def test_network_connectivity(self, verbose):
        """Test network connectivity to Supabase."""
        self.stdout.write('\n3️⃣ Testing Network Connectivity...')
        
        hostname = 'db.fsznynpmqxljaijcuweb.supabase.co'
        port = 5432
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((hostname, port))
            sock.close()
            
            if result == 0:
                self.stdout.write(f'  ✅ Network Connection: {hostname}:{port} is reachable')
                return True
            else:
                self.stdout.write(f'  ❌ Network Connection: {hostname}:{port} is not reachable (error {result})')
                return False
                
        except Exception as e:
            self.stdout.write(f'  ❌ Network Connection Error: {str(e)}')
            return False

    def test_supabase_api(self, verbose):
        """Test Supabase Management API connectivity."""
        self.stdout.write('\n4️⃣ Testing Supabase Management API...')
        
        project_id = 'fsznynpmqxljaijcuweb'
        
        try:
            # Test basic project info
            url = f'https://api.supabase.com/v1/projects/{project_id}'
            
            # Note: This would need proper authentication
            # For now, we'll test the endpoint availability
            response = requests.get(url, timeout=10)
            
            if response.status_code in [200, 401, 403]:  # 401/403 means endpoint exists but needs auth
                self.stdout.write(f'  ✅ Supabase API: Endpoint reachable (status: {response.status_code})')
                return True
            else:
                self.stdout.write(f'  ❌ Supabase API: Unexpected status {response.status_code}')
                return False
                
        except Exception as e:
            self.stdout.write(f'  ❌ Supabase API Error: {str(e)}')
            return False

    def test_postgresql_connection(self, verbose):
        """Test direct PostgreSQL connection."""
        self.stdout.write('\n5️⃣ Testing Direct PostgreSQL Connection...')
        
        connection_params = {
            'host': 'db.fsznynpmqxljaijcuweb.supabase.co',
            'port': 5432,
            'database': 'postgres',
            'user': 'postgres',
            'password': os.getenv('SUPABASE_DB_PASSWORD', 'Nike1414##!!'),
            'sslmode': 'require'
        }
        
        try:
            conn = psycopg2.connect(**connection_params)
            cursor = conn.cursor()
            cursor.execute('SELECT version();')
            version = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            
            self.stdout.write(f'  ✅ PostgreSQL Connection: Success')
            if verbose:
                self.stdout.write(f'  📋 Database Version: {version}')
            return True
            
        except Exception as e:
            self.stdout.write(f'  ❌ PostgreSQL Connection Failed: {str(e)}')
            
            # Provide troubleshooting suggestions
            self.stdout.write('  💡 Troubleshooting suggestions:')
            self.stdout.write('     - Check if your IP is whitelisted in Supabase')
            self.stdout.write('     - Verify password is correct')
            self.stdout.write('     - Check firewall settings')
            self.stdout.write('     - Try connecting from a different network')
            
            return False

    def test_django_connection(self, verbose):
        """Test Django database connection."""
        self.stdout.write('\n6️⃣ Testing Django Database Connection...')
        
        try:
            from django.db import connection
            
            # Force Django to use PostgreSQL
            os.environ['USE_POSTGRESQL'] = 'true'
            
            # Test the connection
            with connection.cursor() as cursor:
                cursor.execute('SELECT 1')
                result = cursor.fetchone()
                
            if result and result[0] == 1:
                self.stdout.write('  ✅ Django Database Connection: Success')
                
                # Show current database info
                if verbose:
                    db_settings = settings.DATABASES['default']
                    self.stdout.write(f'  📋 Database Engine: {db_settings["ENGINE"]}')
                    self.stdout.write(f'  📋 Database Host: {db_settings["HOST"]}')
                    self.stdout.write(f'  📋 Database Name: {db_settings["NAME"]}')
                
                return True
            else:
                self.stdout.write('  ❌ Django Database Connection: Unexpected result')
                return False
                
        except Exception as e:
            self.stdout.write(f'  ❌ Django Database Connection Failed: {str(e)}')
            
            # Show current database configuration
            if verbose:
                try:
                    db_settings = settings.DATABASES['default']
                    self.stdout.write('  📋 Current Django Database Configuration:')
                    for key, value in db_settings.items():
                        if key == 'PASSWORD':
                            value = '*' * len(str(value))
                        self.stdout.write(f'       {key}: {value}')
                except:
                    self.stdout.write('  📋 Could not retrieve database configuration')
            
            return False

    def provide_recommendations(self):
        """Provide recommendations based on test results."""
        self.stdout.write('\n💡 Recommendations:')
        self.stdout.write('1. If DNS resolution fails:')
        self.stdout.write('   - Check your internet connection')
        self.stdout.write('   - Try using a different DNS server (*******, *******)')
        self.stdout.write('   - Check if your network blocks certain domains')
        
        self.stdout.write('\n2. If network connectivity fails:')
        self.stdout.write('   - Check firewall settings')
        self.stdout.write('   - Verify Supabase project is active')
        self.stdout.write('   - Check if your IP is whitelisted in Supabase')
        
        self.stdout.write('\n3. If PostgreSQL connection fails:')
        self.stdout.write('   - Verify database password is correct')
        self.stdout.write('   - Check Supabase project settings')
        self.stdout.write('   - Try connecting from Supabase dashboard first')
        
        self.stdout.write('\n4. Alternative approaches:')
        self.stdout.write('   - Use Supabase REST API instead of direct PostgreSQL')
        self.stdout.write('   - Use Supabase Management API for data operations')
        self.stdout.write('   - Consider using connection pooling')
        
        self.stdout.write('\n🔗 Useful Links:')
        self.stdout.write('   - Supabase Dashboard: https://supabase.com/dashboard/project/fsznynpmqxljaijcuweb')
        self.stdout.write('   - Supabase Docs: https://supabase.com/docs/guides/database/connecting-to-postgres')
        self.stdout.write('   - Connection Troubleshooting: https://supabase.com/docs/guides/platform/troubleshooting')
