<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-1054" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 1610.5885009765625 2993.05224609375" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-1054{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-1054 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-1054 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-1054 .error-icon{fill:#a44141;}#graph-1054 .error-text{fill:#ddd;stroke:#ddd;}#graph-1054 .edge-thickness-normal{stroke-width:1px;}#graph-1054 .edge-thickness-thick{stroke-width:3.5px;}#graph-1054 .edge-pattern-solid{stroke-dasharray:0;}#graph-1054 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-1054 .edge-pattern-dashed{stroke-dasharray:3;}#graph-1054 .edge-pattern-dotted{stroke-dasharray:2;}#graph-1054 .marker{fill:lightgrey;stroke:lightgrey;}#graph-1054 .marker.cross{stroke:lightgrey;}#graph-1054 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-1054 p{margin:0;}#graph-1054 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-1054 .cluster-label text{fill:#F9FFFE;}#graph-1054 .cluster-label span{color:#F9FFFE;}#graph-1054 .cluster-label span p{background-color:transparent;}#graph-1054 .label text,#graph-1054 span{fill:#ccc;color:#ccc;}#graph-1054 .node rect,#graph-1054 .node circle,#graph-1054 .node ellipse,#graph-1054 .node polygon,#graph-1054 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-1054 .rough-node .label text,#graph-1054 .node .label text,#graph-1054 .image-shape .label,#graph-1054 .icon-shape .label{text-anchor:middle;}#graph-1054 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-1054 .rough-node .label,#graph-1054 .node .label,#graph-1054 .image-shape .label,#graph-1054 .icon-shape .label{text-align:center;}#graph-1054 .node.clickable{cursor:pointer;}#graph-1054 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-1054 .arrowheadPath{fill:lightgrey;}#graph-1054 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-1054 .flowchart-link{stroke:lightgrey;fill:none;}#graph-1054 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1054 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-1054 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1054 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-1054 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-1054 .cluster text{fill:#F9FFFE;}#graph-1054 .cluster span{color:#F9FFFE;}#graph-1054 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-1054 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-1054 rect.text{fill:none;stroke-width:0;}#graph-1054 .icon-shape,#graph-1054 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1054 .icon-shape p,#graph-1054 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-1054 .icon-shape rect,#graph-1054 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1054 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-1054 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-1054 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-1054 .schoolAdmin&gt;*{fill:#3B82F6!important;stroke:#2563EB!important;color:#fff!important;}#graph-1054 .schoolAdmin span{fill:#3B82F6!important;stroke:#2563EB!important;color:#fff!important;}#graph-1054 .schoolAdmin tspan{fill:#fff!important;}#graph-1054 .superAdmin&gt;*{fill:#1E3A8A!important;stroke:#1E40AF!important;color:#fff!important;}#graph-1054 .superAdmin span{fill:#1E3A8A!important;stroke:#1E40AF!important;color:#fff!important;}#graph-1054 .superAdmin tspan{fill:#fff!important;}#graph-1054 .system&gt;*{fill:#6B7280!important;stroke:#4B5563!important;color:#fff!important;}#graph-1054 .system span{fill:#6B7280!important;stroke:#4B5563!important;color:#fff!important;}#graph-1054 .system tspan{fill:#fff!important;}#graph-1054 .decision&gt;*{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1054 .decision span{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1054 .decision tspan{fill:#000!important;}#graph-1054 .error&gt;*{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1054 .error span{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1054 .error tspan{fill:#000!important;}</style><g><marker id="graph-1054_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1054_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1054_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1054_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1054_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1054_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"><g class="cluster" id="System" data-look="classic"><rect style="" x="339.90625" y="330" width="531.5572929382324" height="2204.0520935058594"></rect><g class="cluster-label" transform="translate(580.4765625, 330)"><foreignObject width="50.41666793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>System</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph1" data-look="classic"><rect style="" x="1120.5885429382324" y="821.2291717529297" width="481.99999237060547" height="2074.8229217529297"></rect><g class="cluster-label" transform="translate(1316.9999961853027, 821.2291717529297)"><foreignObject width="89.17708587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Super Admin</p></span></div></foreignObject></g></g><g class="cluster" id="subGraph0" data-look="classic"><rect style="" x="8" y="8" width="311.90625" height="2759.0520935058594"></rect><g class="cluster-label" transform="translate(98.55728912353516, 8)"><foreignObject width="130.7916717529297" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>New School Admin</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path d="M163.953,72L163.953,76.167C163.953,80.333,163.953,88.667,163.953,96.333C163.953,104,163.953,111,163.953,114.5L163.953,118" id="L_START_FORM_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,176L163.953,180.167C163.953,184.333,163.953,192.667,163.953,200.333C163.953,208,163.953,215,163.953,218.5L163.953,222" id="L_FORM_SUBMIT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,1559.813L163.953,1563.979C163.953,1568.146,163.953,1576.479,163.953,1584.146C163.953,1591.813,163.953,1598.813,163.953,1602.313L163.953,1605.813" id="L_VERIFY_EMAIL_WAIT_APPROVAL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,1767.813L163.953,1771.979C163.953,1776.146,163.953,1784.479,163.953,1792.146C163.953,1799.813,163.953,1806.813,163.953,1810.313L163.953,1813.813" id="L_CONFIG_DEPT_SETUP_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,1871.813L163.953,1875.979C163.953,1880.146,163.953,1888.479,163.953,1896.146C163.953,1903.813,163.953,1910.813,163.953,1914.313L163.953,1917.813" id="L_DEPT_SETUP_ROLE_CREATE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,1975.813L163.953,1979.979C163.953,1984.146,163.953,1992.479,163.953,2000.146C163.953,2007.813,163.953,2014.813,163.953,2018.313L163.953,2021.813" id="L_ROLE_CREATE_BULK_IMPORT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,2638.052L163.953,2642.219C163.953,2646.385,163.953,2654.719,163.953,2662.385C163.953,2670.052,163.953,2677.052,163.953,2680.552L163.953,2684.052" id="L_TEST_GOLIVE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M1357.807,900.229L1357.807,904.396C1357.807,908.563,1357.807,916.896,1357.878,924.646C1357.948,932.396,1358.088,939.563,1358.159,943.146L1358.229,946.73" id="L_REVIEW_APPROVE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M1317.482,1079.488L1305.355,1092.375C1293.228,1105.263,1268.973,1131.038,1256.846,1149.425C1244.719,1167.813,1244.719,1178.813,1244.719,1184.313L1244.719,1189.813" id="L_APPROVE_SEND_APPROVAL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M1399.132,1079.488L1411.093,1092.375C1423.053,1105.263,1446.975,1131.038,1458.935,1149.425C1470.896,1167.813,1470.896,1178.813,1470.896,1184.313L1470.896,1189.813" id="L_APPROVE_REJECT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M632.842,409L639.013,415.167C645.183,421.333,657.525,433.667,658.141,450.216C658.756,466.765,647.644,487.53,642.088,497.913L636.533,508.296" id="L_EMAIL_VERIFY_VALIDATE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M579.38,510.443L573.897,499.702C568.413,488.962,557.446,467.481,557.227,451.063C557.009,434.644,567.538,423.289,572.803,417.611L578.068,411.933" id="L_VALIDATE_EMAIL_VERIFY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M606.323,643.729L606.24,649.813C606.156,655.896,605.99,668.063,605.906,679.646C605.823,691.229,605.823,702.229,605.823,707.729L605.823,713.229" id="L_VALIDATE_NOTIFY_SUPER_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M605.823,1351.813L605.823,1355.979C605.823,1360.146,605.823,1368.479,605.823,1376.146C605.823,1383.813,605.823,1390.813,605.823,1394.313L605.823,1397.813" id="L_SETUP_DB_SEND_WELCOME_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M603.357,2183.813L603.357,2187.979C603.357,2192.146,603.357,2200.479,603.427,2208.229C603.497,2215.979,603.638,2223.146,603.708,2226.73L603.778,2230.313" id="L_IMPORT_PROCESS_VALIDATE_IMPORT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M564.391,2342.087L549.582,2354.748C534.773,2367.409,505.155,2392.73,490.346,2410.891C475.536,2429.052,475.536,2440.052,475.536,2445.552L475.536,2451.052" id="L_VALIDATE_IMPORT_IMPORT_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M643.322,2342.087L657.965,2354.748C672.607,2367.409,701.892,2392.73,716.535,2410.891C731.177,2429.052,731.177,2440.052,731.177,2445.552L731.177,2451.052" id="L_VALIDATE_IMPORT_SEND_INVITES_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,280L163.953,284.167C163.953,288.333,163.953,296.667,163.953,305C163.953,313.333,163.953,321.667,218.088,332.204C272.223,342.741,380.492,355.483,434.627,361.853L488.762,368.224" id="L_SUBMIT_EMAIL_VERIFY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M605.823,771.229L605.823,775.396C605.823,779.563,605.823,787.896,605.823,796.229C605.823,804.563,605.823,812.896,714.2,824.557C822.576,836.218,1039.329,851.206,1147.706,858.701L1256.082,866.195" id="L_NOTIFY_SUPER_REVIEW_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M1244.719,1247.813L1244.719,1251.979C1244.719,1256.146,1244.719,1264.479,1157.385,1275.754C1070.051,1287.029,895.384,1301.245,808.05,1308.353L720.716,1315.461" id="L_SEND_APPROVAL_SETUP_DB_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M1470.896,1247.813L1470.896,1251.979C1470.896,1256.146,1470.896,1264.479,1407.341,1275.605C1343.785,1286.732,1216.675,1300.651,1153.12,1307.61L1089.565,1314.57" id="L_REJECT_END_REJECT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M605.823,1455.813L605.823,1459.979C605.823,1464.146,605.823,1472.479,545.01,1483.802C484.197,1495.126,362.572,1509.439,301.759,1516.595L240.947,1523.752" id="L_SEND_WELCOME_VERIFY_EMAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,1663.813L163.953,1667.979C163.953,1672.146,163.953,1680.479,163.953,1688.146C163.953,1695.813,163.953,1702.813,163.953,1706.313L163.953,1709.813" id="L_WAIT_APPROVAL_CONFIG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M199.249,2079.813L204.695,2083.979C210.142,2088.146,221.036,2096.479,270.909,2106.866C320.783,2117.252,409.636,2129.691,454.063,2135.911L498.489,2142.131" id="L_BULK_IMPORT_IMPORT_PROCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M375.182,2465.131L328.648,2457.284C282.114,2449.438,189.045,2433.745,142.511,2407.462C95.977,2381.179,95.977,2344.306,95.977,2309.432C95.977,2274.559,95.977,2241.686,95.977,2216.582C95.977,2191.479,95.977,2174.146,95.977,2156.813C95.977,2139.479,95.977,2122.146,100.894,2109.718C105.811,2097.289,115.646,2089.766,120.563,2086.004L125.481,2082.243" id="L_IMPORT_ERROR_BULK_IMPORT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M731.177,2509.052L731.177,2513.219C731.177,2517.385,731.177,2525.719,636.64,2534.052C542.102,2542.385,353.028,2550.719,258.49,2558.385C163.953,2566.052,163.953,2573.052,163.953,2576.552L163.953,2580.052" id="L_SEND_INVITES_TEST_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M163.953,2742.052L163.953,2746.219C163.953,2750.385,163.953,2758.719,364.189,2767.052C564.425,2775.385,964.898,2783.719,1165.134,2791.385C1365.37,2799.052,1365.37,2806.052,1365.37,2809.552L1365.37,2813.052" id="L_GOLIVE_MONITOR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path><path d="M1365.37,2871.052L1365.37,2875.219C1365.37,2879.385,1365.37,2887.719,1365.37,2896.052C1365.37,2904.385,1365.37,2912.719,1365.37,2920.385C1365.37,2928.052,1365.37,2935.052,1365.37,2938.552L1365.37,2942.052" id="L_MONITOR_END_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1054_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1244.71875, 1156.8125)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1470.8958282470703, 1156.8125)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(546.4791693687439, 446)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(605.8229179382324, 680.2291717529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(475.5364570617676, 2418.0520935058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(731.1770820617676, 2418.0520935058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default" id="flowchart-START-0" transform="translate(163.953125, 52.5)"><rect class="basic label-container" style="" rx="19.5" ry="19.5" x="-80.74479675292969" y="-19.5" width="161.48959350585938" height="39"></rect><g class="label" style="" transform="translate(-68.36979675292969, -12)"><rect></rect><foreignObject width="136.73959350585938" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>School Registration</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-FORM-1" transform="translate(163.953125, 149)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-107.09375" y="-27" width="214.1875" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-77.09375, -12)"><rect></rect><foreignObject width="154.1875" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Fill Registration Form</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-SUBMIT-3" transform="translate(163.953125, 253)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-97.296875" y="-27" width="194.59375" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-67.296875, -12)"><rect></rect><foreignObject width="134.59375" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Submit Application</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-VERIFY_EMAIL-4" transform="translate(163.953125, 1532.8125)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-73.02083587646484" y="-27" width="146.0416717529297" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-43.020835876464844, -12)"><rect></rect><foreignObject width="86.04167175292969" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Verify Email</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-WAIT_APPROVAL-5" transform="translate(163.953125, 1636.8125)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-92.28125" y="-27" width="184.5625" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-62.28125, -12)"><rect></rect><foreignObject width="124.5625" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Wait for Approval</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-CONFIG-6" transform="translate(163.953125, 1740.8125)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-120.953125" y="-27" width="241.90625" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-90.953125, -12)"><rect></rect><foreignObject width="181.90625" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Configure School Settings</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-DEPT_SETUP-7" transform="translate(163.953125, 1844.8125)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-98.61979675292969" y="-27" width="197.23959350585938" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-68.61979675292969, -12)"><rect></rect><foreignObject width="137.23959350585938" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Setup Departments</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-ROLE_CREATE-9" transform="translate(163.953125, 1948.8125)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-103.90625" y="-27" width="207.8125" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-73.90625, -12)"><rect></rect><foreignObject width="147.8125" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Create Custom Roles</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-BULK_IMPORT-11" transform="translate(163.953125, 2052.8125)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-75.44791793823242" y="-27" width="150.89583587646484" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-45.44791793823242, -12)"><rect></rect><foreignObject width="90.89583587646484" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Import Users</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-TEST-12" transform="translate(163.953125, 2611.0520935058594)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-72.04166793823242" y="-27" width="144.08333587646484" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-42.04166793823242, -12)"><rect></rect><foreignObject width="84.08333587646484" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Test System</p></span></div></foreignObject></g></g><g class="node default schoolAdmin" id="flowchart-GOLIVE-13" transform="translate(163.953125, 2715.0520935058594)"><rect class="basic label-container" style="fill:#3B82F6 !important;stroke:#2563EB !important" x="-56.72916793823242" y="-27" width="113.45833587646484" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-26.729167938232422, -12)"><rect></rect><foreignObject width="53.458335876464844" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Go Live</p></span></div></foreignObject></g></g><g class="node default superAdmin" id="flowchart-REVIEW-14" transform="translate(1357.8072891235352, 873.2291717529297)"><rect class="basic label-container" style="fill:#1E3A8A !important;stroke:#1E40AF !important" x="-97.734375" y="-27" width="195.46875" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-67.734375, -12)"><rect></rect><foreignObject width="135.46875" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Review Application</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-APPROVE-15" transform="translate(1357.8072891235352, 1035.0208358764648)"><polygon points="84.79166793823242,0 169.58333587646484,-84.79166793823242 84.79166793823242,-169.58333587646484 0,-84.79166793823242" class="label-container" transform="translate(-84.79166793823242,84.79166793823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-57.79166793823242, -12)"><rect></rect><foreignObject width="115.58333587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Approve School?</p></span></div></foreignObject></g></g><g class="node default superAdmin" id="flowchart-SEND_APPROVAL-17" transform="translate(1244.71875, 1220.8125)"><rect class="basic label-container" style="fill:#1E3A8A !important;stroke:#1E40AF !important" x="-80.52604293823242" y="-27" width="161.05208587646484" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-50.52604293823242, -12)"><rect></rect><foreignObject width="101.05208587646484" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Send Approval</p></span></div></foreignObject></g></g><g class="node default superAdmin" id="flowchart-REJECT-19" transform="translate(1470.8958282470703, 1220.8125)"><rect class="basic label-container" style="fill:#1E3A8A !important;stroke:#1E40AF !important" x="-95.65104675292969" y="-27" width="191.30209350585938" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-65.65104675292969, -12)"><rect></rect><foreignObject width="131.30209350585938" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Reject Application</p></span></div></foreignObject></g></g><g class="node default superAdmin" id="flowchart-MONITOR-20" transform="translate(1365.3697891235352, 2844.0520935058594)"><rect class="basic label-container" style="fill:#1E3A8A !important;stroke:#1E40AF !important" x="-111.68229675292969" y="-27" width="223.36459350585938" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-81.68229675292969, -12)"><rect></rect><foreignObject width="163.36459350585938" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Monitor Setup Progress</p></span></div></foreignObject></g></g><g class="node default system" id="flowchart-EMAIL_VERIFY-21" transform="translate(605.8229179382324, 382)"><rect class="basic label-container" style="fill:#6B7280 !important;stroke:#4B5563 !important" x="-113.08854675292969" y="-27" width="226.17709350585938" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-83.08854675292969, -12)"><rect></rect><foreignObject width="166.17709350585938" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Send Verification Email</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-VALIDATE-22" transform="translate(605.8229179382324, 563.1145858764648)"><polygon points="80.11458587646484,0 160.2291717529297,-80.11458587646484 80.11458587646484,-160.2291717529297 0,-80.11458587646484" class="label-container" transform="translate(-80.11458587646484,80.11458587646484)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-53.114585876464844, -12)"><rect></rect><foreignObject width="106.22917175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Email Verified?</p></span></div></foreignObject></g></g><g class="node default system" id="flowchart-NOTIFY_SUPER-26" transform="translate(605.8229179382324, 744.2291717529297)"><rect class="basic label-container" style="fill:#6B7280 !important;stroke:#4B5563 !important" x="-98.75" y="-27" width="197.5" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-68.75, -12)"><rect></rect><foreignObject width="137.5" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Notify Super Admin</p></span></div></foreignObject></g></g><g class="node default system" id="flowchart-SETUP_DB-27" transform="translate(605.8229179382324, 1324.8125)"><rect class="basic label-container" style="fill:#6B7280 !important;stroke:#4B5563 !important" x="-110.90625" y="-27" width="221.8125" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-80.90625, -12)"><rect></rect><foreignObject width="161.8125" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Setup School Database</p></span></div></foreignObject></g></g><g class="node default system" id="flowchart-SEND_WELCOME-28" transform="translate(605.8229179382324, 1428.8125)"><rect class="basic label-container" style="fill:#6B7280 !important;stroke:#4B5563 !important" x="-104.0625" y="-27" width="208.125" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-74.0625, -12)"><rect></rect><foreignObject width="148.125" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Send Welcome Email</p></span></div></foreignObject></g></g><g class="node default system" id="flowchart-IMPORT_PROCESS-29" transform="translate(603.3567695617676, 2156.8125)"><rect class="basic label-container" style="fill:#6B7280 !important;stroke:#4B5563 !important" x="-100.90625" y="-27" width="201.8125" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-70.90625, -12)"><rect></rect><foreignObject width="141.8125" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Process User Import</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-VALIDATE_IMPORT-30" transform="translate(603.3567695617676, 2307.4322967529297)"><polygon points="73.61979293823242,0 147.23958587646484,-73.61979293823242 73.61979293823242,-147.23958587646484 0,-73.61979293823242" class="label-container" transform="translate(-73.61979293823242,73.61979293823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-46.61979293823242, -12)"><rect></rect><foreignObject width="93.23958587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Import Valid?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-IMPORT_ERROR-32" transform="translate(475.5364570617676, 2482.0520935058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-100.35417175292969" y="-27" width="200.70834350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-70.35417175292969, -12)"><rect></rect><foreignObject width="140.70834350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Import Error Report</p></span></div></foreignObject></g></g><g class="node default system" id="flowchart-SEND_INVITES-34" transform="translate(731.1770820617676, 2482.0520935058594)"><rect class="basic label-container" style="fill:#6B7280 !important;stroke:#4B5563 !important" x="-105.28646087646484" y="-27" width="210.5729217529297" height="54"></rect><g class="label" style="color:#fff !important" transform="translate(-75.28646087646484, -12)"><rect></rect><foreignObject width="150.5729217529297" height="24"><div style="color: rgb(255, 255, 255) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#fff !important" class="nodeLabel"><p>Send User Invitations</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-END_REJECT-42" transform="translate(996.0260429382324, 1324.8125)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" rx="19.5" ry="19.5" x="-89.5625" y="-19.5" width="179.125" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-77.1875, -12)"><rect></rect><foreignObject width="154.375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Registration Rejected</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-END_SUCCESS-56" transform="translate(1365.3697891235352, 2965.5520935058594)"><rect class="basic label-container" style="" rx="19.5" ry="19.5" x="-90.171875" y="-19.5" width="180.34375" height="39"></rect><g class="label" style="" transform="translate(-77.796875, -12)"><rect></rect><foreignObject width="155.59375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Onboarding Complete</p></span></div></foreignObject></g></g></g></g></g></svg>