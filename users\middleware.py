"""
RBAC middleware for API security and permission enforcement.
"""

import json
import logging
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth import get_user_model
from django.contrib.auth.models import AnonymousUser
from django.urls import resolve
from django.conf import settings
from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON><PERSON>ication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from .models import RoleAuditLog
from .rbac_utils import RBACManager

User = get_user_model()
logger = logging.getLogger(__name__)


class RBACMiddleware(MiddlewareMixin):
    """
    Middleware for RBAC permission checking and audit logging.
    """
    
    # Paths that don't require RBAC checking
    EXEMPT_PATHS = [
        '/admin/',
        '/api/v1/token/',
        '/api/v1/auth/',
        '/static/',
        '/media/',
        '/favicon.ico',
    ]
    
    # API paths that require RBAC checking
    RBAC_PROTECTED_PATHS = [
        '/api/v1/users/',
        '/api/v1/schools/',
        '/api/v1/courses/',
        '/rbac-admin/',
    ]
    
    def __init__(self, get_response):
        self.get_response = get_response
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Process incoming request for RBAC validation.
        """
        # Skip RBAC for exempt paths
        if self.is_exempt_path(request.path):
            return None
        
        # Only apply RBAC to protected API paths
        if not self.is_rbac_protected_path(request.path):
            return None
        
        # Add user context and permissions to request
        self.add_user_context(request)
        
        # Log API access for audit trail
        self.log_api_access(request)
        
        return None
    
    def process_response(self, request, response):
        """
        Process response for additional security headers.
        """
        # Add security headers for API responses
        if request.path.startswith('/api/'):
            response['X-Content-Type-Options'] = 'nosniff'
            response['X-Frame-Options'] = 'DENY'
            response['X-XSS-Protection'] = '1; mode=block'
            response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        return response
    
    def is_exempt_path(self, path):
        """Check if path is exempt from RBAC checking."""
        return any(path.startswith(exempt) for exempt in self.EXEMPT_PATHS)
    
    def is_rbac_protected_path(self, path):
        """Check if path requires RBAC protection."""
        return any(path.startswith(protected) for protected in self.RBAC_PROTECTED_PATHS)
    
    def add_user_context(self, request):
        """Add user permissions and context to request."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            # Add user permissions to request for easy access
            request.user_permissions = RBACManager.get_user_permissions(
                request.user, 
                request.user.school
            )
            
            # Add school context
            request.user_school = request.user.school
            
            # Add role information
            request.user_roles = list(
                request.user.user_roles.filter(is_active=True)
                .select_related('role')
                .values_list('role__name', flat=True)
            )
    
    def log_api_access(self, request):
        """Log API access for audit trail."""
        if hasattr(request, 'user') and request.user.is_authenticated:
            try:
                # Get client IP
                x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
                if x_forwarded_for:
                    ip_address = x_forwarded_for.split(',')[0]
                else:
                    ip_address = request.META.get('REMOTE_ADDR')
                
                # Log the access
                RoleAuditLog.objects.create(
                    action_type='api_access',
                    description=f'API access: {request.method} {request.path}',
                    performed_by=request.user,
                    school=request.user.school,
                    ip_address=ip_address,
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    metadata={
                        'method': request.method,
                        'path': request.path,
                        'query_params': dict(request.GET),
                    }
                )
            except Exception as e:
                logger.error(f"Failed to log API access: {e}")


class JWTRBACMiddleware(MiddlewareMixin):
    """
    Middleware to enhance JWT tokens with RBAC claims.
    """
    
    def process_request(self, request):
        """
        Add RBAC claims to JWT authenticated requests.
        """
        # Only process API requests
        if not request.path.startswith('/api/'):
            return None
        
        # Skip token endpoints
        if request.path.startswith('/api/v1/token/'):
            return None
        
        # Try to authenticate with JWT
        jwt_auth = JWTAuthentication()
        try:
            auth_result = jwt_auth.authenticate(request)
            if auth_result:
                user, token = auth_result
                
                # Add RBAC claims to the token payload
                self.add_rbac_claims(request, user, token)
                
        except (InvalidToken, TokenError):
            # Token is invalid or missing, let DRF handle it
            pass
        except Exception as e:
            logger.error(f"JWT RBAC middleware error: {e}")
        
        return None
    
    def add_rbac_claims(self, request, user, token):
        """Add RBAC-specific claims to JWT token payload."""
        try:
            # Get user permissions
            permissions = RBACManager.get_user_permissions(user, user.school)
            
            # Get user roles
            roles = list(
                user.user_roles.filter(is_active=True)
                .select_related('role')
                .values('role__id', 'role__name', 'role__role_type')
            )
            
            # Add claims to request for easy access
            request.jwt_claims = {
                'user_id': str(user.id),
                'school_id': str(user.school.id) if user.school else None,
                'permissions': list(permissions),
                'roles': roles,
                'is_superuser': user.is_superuser,
                'user_type': user.user_type,
            }
            
            # Store in token payload for future reference
            if hasattr(token, 'payload'):
                token.payload.update({
                    'rbac_permissions': list(permissions)[:50],  # Limit to prevent token bloat
                    'rbac_roles': [role['role__name'] for role in roles][:10],
                    'school_id': str(user.school.id) if user.school else None,
                })
                
        except Exception as e:
            logger.error(f"Failed to add RBAC claims: {e}")


class APIRateLimitMiddleware(MiddlewareMixin):
    """
    Simple rate limiting middleware for API endpoints.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limits = {}
        super().__init__(get_response)
    
    def process_request(self, request):
        """
        Apply rate limiting to API requests.
        """
        # Only apply to API endpoints
        if not request.path.startswith('/api/'):
            return None
        
        # Get client identifier
        client_id = self.get_client_identifier(request)
        
        # Check rate limit
        if self.is_rate_limited(client_id, request):
            return JsonResponse(
                {
                    'error': 'Rate limit exceeded',
                    'detail': 'Too many requests. Please try again later.'
                },
                status=429
            )
        
        return None
    
    def get_client_identifier(self, request):
        """Get unique identifier for the client."""
        # Use user ID if authenticated, otherwise IP address
        if hasattr(request, 'user') and request.user.is_authenticated:
            return f"user_{request.user.id}"
        
        # Get IP address
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        
        return f"ip_{ip}"
    
    def is_rate_limited(self, client_id, request):
        """Check if client has exceeded rate limit."""
        import time
        
        current_time = time.time()
        window_size = 60  # 1 minute window
        max_requests = 100  # Max requests per minute
        
        # Clean old entries
        self.rate_limits = {
            k: v for k, v in self.rate_limits.items()
            if current_time - v['first_request'] < window_size
        }
        
        # Check current client
        if client_id not in self.rate_limits:
            self.rate_limits[client_id] = {
                'count': 1,
                'first_request': current_time
            }
            return False
        
        client_data = self.rate_limits[client_id]
        
        # Reset if window expired
        if current_time - client_data['first_request'] >= window_size:
            self.rate_limits[client_id] = {
                'count': 1,
                'first_request': current_time
            }
            return False
        
        # Increment count
        client_data['count'] += 1
        
        # Check if limit exceeded
        return client_data['count'] > max_requests


class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Middleware to add security headers to all responses.
    """
    
    def process_response(self, request, response):
        """Add security headers to response."""
        # CORS headers for API endpoints
        if request.path.startswith('/api/'):
            # Only allow specific origins in production
            allowed_origins = getattr(settings, 'CORS_ALLOWED_ORIGINS', ['http://localhost:3000'])
            origin = request.META.get('HTTP_ORIGIN')
            
            if origin in allowed_origins:
                response['Access-Control-Allow-Origin'] = origin
            
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Authorization, Content-Type, X-Requested-With'
            response['Access-Control-Max-Age'] = '86400'
        
        # General security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # HSTS header for HTTPS
        if request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        return response


class SchoolContextMiddleware(MiddlewareMixin):
    """
    Enhanced middleware to enforce school context and multi-tenant data isolation.
    """

    # Endpoints that require strict school isolation
    STRICT_ISOLATION_PATHS = [
        '/api/v1/users/rbac/roles/',
        '/api/v1/users/rbac/user-roles/',
        '/api/v1/users/',
        '/api/v1/courses/',
        '/api/v1/grades/',
    ]

    # Endpoints that allow cross-school access for super admins
    CROSS_SCHOOL_ALLOWED_PATHS = [
        '/api/v1/users/rbac/roles/cross_school_comparison/',
        '/api/v1/users/rbac/roles/school_distribution/',
        '/api/v1/users/rbac/roles/permission_matrix/',
        '/api/v1/users/rbac/roles/standardization_suggestions/',
    ]

    def process_request(self, request):
        """
        Add school context to request and validate multi-tenant access.
        """
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return None

        # Add school context to request
        user_school = getattr(request.user, 'school', None)
        request.school_context = user_school
        request.is_cross_school_allowed = self.is_cross_school_endpoint(request.path)

        # Validate school context for API endpoints
        if request.path.startswith('/api/'):
            response = self.validate_school_context(request)
            if response:
                return response

    def is_cross_school_endpoint(self, path):
        """Check if the endpoint allows cross-school access."""
        return any(allowed_path in path for allowed_path in self.CROSS_SCHOOL_ALLOWED_PATHS)

    def requires_strict_isolation(self, path):
        """Check if the endpoint requires strict school isolation."""
        return any(strict_path in path for strict_path in self.STRICT_ISOLATION_PATHS)

    def validate_school_context(self, request):
        """Enhanced validation for school context and multi-tenant access."""
        # Skip validation for super users on cross-school endpoints
        if request.user.is_superuser and request.is_cross_school_allowed:
            return None

        # Skip validation for non-strict endpoints
        if not self.requires_strict_isolation(request.path):
            return None

        # Validate school parameter in request
        response = self.validate_school_parameter(request)
        if response:
            return response

        return None

    def validate_school_parameter(self, request):
        """Validate school parameter in request data."""
        school_param = None

        # Extract school parameter from request
        if request.method == 'GET':
            school_param = request.GET.get('school')
        elif request.method in ['POST', 'PUT', 'PATCH']:
            if hasattr(request, 'data'):
                school_param = request.data.get('school')

        # If school parameter is provided, validate it
        if school_param:
            return self.check_school_access(request, school_param)

        return None

    def check_school_access(self, request, school_param):
        """Check if user can access the specified school."""
        user_school = getattr(request.user, 'school', None)

        # Super users can access any school
        if request.user.is_superuser:
            return None

        # Users without school can't access school-specific data
        if not user_school:
            return self.create_access_denied_response(
                request,
                'User not associated with any school',
                {'requested_school': str(school_param)}
            )

        # Validate school parameter matches user's school
        if str(user_school.id) != str(school_param):
            return self.create_access_denied_response(
                request,
                'School context violation: Cannot access different school data',
                {
                    'user_school': str(user_school.id),
                    'requested_school': str(school_param)
                }
            )

        return None

    def create_access_denied_response(self, request, message, context=None):
        """Create a standardized access denied response and log the violation."""
        # Log security violation
        APISecurityManager.log_security_event(
            'school_context_violation',
            request,
            {
                'message': message,
                'endpoint': request.path,
                'method': request.method,
                'context': context or {}
            }
        )

        # Return 403 Forbidden
        from django.http import JsonResponse
        return JsonResponse(
            {
                'error': 'Access denied',
                'detail': message,
                'code': 'SCHOOL_CONTEXT_VIOLATION'
            },
            status=403
        )
