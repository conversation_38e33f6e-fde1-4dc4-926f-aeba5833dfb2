"""
API views for role template management and workflow operations.
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.core.exceptions import ValidationError, PermissionDenied
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta

from .models import RoleTemplate, Role, UserRole, Permission, RolePermission
from .serializers import RoleTemplateSerializer, RoleSerializer, UserRoleSerializer
from .rbac_utils import RBACManager
from .decorators import RBACPermission
from .decorators import audit_action
from .multi_tenant_managers import SchoolContextValidator

User = get_user_model()


class RoleTemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing role templates.
    """
    serializer_class = RoleTemplateSerializer
    permission_classes = [IsAuthenticated, RBACPermission('can_manage_role_templates')]
    
    def get_queryset(self):
        """Filter templates based on user permissions."""
        user = self.request.user
        
        if user.is_superuser:
            return RoleTemplate.objects.all().prefetch_related('permissions')
        
        # Regular users can only see active templates
        return RoleTemplate.objects.filter(is_active=True).prefetch_related('permissions')
    
    @audit_action('template_created')
    def create(self, request, *args, **kwargs):
        """Create a new role template."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Set created_by
        serializer.validated_data['created_by'] = request.user
        
        template = serializer.save()
        
        # Add permissions if provided
        permission_ids = request.data.get('permission_ids', [])
        if permission_ids:
            permissions = Permission.objects.filter(id__in=permission_ids)
            template.permissions.set(permissions)
        
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['post'])
    @audit_action('role_created_from_template')
    def create_role(self, request, pk=None):
        """Create a new role from this template."""
        template = self.get_object()
        
        role_name = request.data.get('role_name')
        school_id = request.data.get('school_id')
        description = request.data.get('description', '')
        
        if not role_name:
            return Response(
                {'error': 'role_name is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get school if provided
            school = None
            if school_id:
                from schools.models import School
                school = get_object_or_404(School, id=school_id)
                
                # Validate user can create roles for this school
                if not SchoolContextValidator.validate_user_school_access(request.user, school):
                    raise PermissionDenied("Cannot create role for this school")
            
            # Create role from template
            role = RBACManager.create_role_from_template(
                template_name=template.name,
                role_name=role_name,
                school=school,
                created_by=request.user,
                description=description
            )
            
            serializer = RoleSerializer(role)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except ValueError as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    @audit_action('bulk_roles_created')
    def bulk_create_roles(self, request, pk=None):
        """Create multiple roles from this template."""
        template = self.get_object()
        
        roles_data = request.data.get('roles', [])
        if not roles_data:
            return Response(
                {'error': 'roles data is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        created_roles = []
        errors = []
        
        with transaction.atomic():
            for role_data in roles_data:
                try:
                    role_name = role_data.get('role_name')
                    school_id = role_data.get('school_id')
                    description = role_data.get('description', '')
                    
                    if not role_name:
                        errors.append({'role_data': role_data, 'error': 'role_name is required'})
                        continue
                    
                    # Get school if provided
                    school = None
                    if school_id:
                        from schools.models import School
                        school = School.objects.get(id=school_id)
                        
                        # Validate user can create roles for this school
                        if not SchoolContextValidator.validate_user_school_access(request.user, school):
                            errors.append({'role_data': role_data, 'error': 'Cannot create role for this school'})
                            continue
                    
                    # Create role from template
                    role = RBACManager.create_role_from_template(
                        template_name=template.name,
                        role_name=role_name,
                        school=school,
                        created_by=request.user,
                        description=description
                    )
                    
                    created_roles.append(RoleSerializer(role).data)
                    
                except Exception as e:
                    errors.append({'role_data': role_data, 'error': str(e)})
        
        return Response({
            'created_roles': created_roles,
            'errors': errors,
            'success_count': len(created_roles),
            'error_count': len(errors)
        }, status=status.HTTP_201_CREATED if created_roles else status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    @audit_action('template_validated')
    def validate_template(self, request, pk=None):
        """Validate template configuration and permissions."""
        template = self.get_object()
        
        validation_results = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'suggestions': []
        }
        
        # Check if template has permissions
        if not template.permissions.exists():
            validation_results['warnings'].append('Template has no permissions assigned')
        
        # Check for conflicting permissions
        permissions = template.permissions.all()
        permission_categories = set(p.category for p in permissions)
        
        if len(permission_categories) > 3:
            validation_results['suggestions'].append(
                f'Template has permissions from {len(permission_categories)} categories. '
                'Consider splitting into multiple templates for better organization.'
            )
        
        # Check for system permissions in non-system template
        if not template.is_system_template:
            system_perms = permissions.filter(is_system_permission=True)
            if system_perms.exists():
                validation_results['warnings'].append(
                    f'Non-system template contains {system_perms.count()} system permissions'
                )
        
        # Check template usage
        if template.usage_count == 0:
            validation_results['suggestions'].append('Template has never been used')
        elif template.usage_count > 100:
            validation_results['suggestions'].append(
                'High usage template - consider creating variants for specific use cases'
            )
        
        return Response(validation_results)
    
    @action(detail=False, methods=['get'])
    def popular_templates(self, request):
        """Get most popular templates by usage count."""
        templates = self.get_queryset().filter(
            is_active=True
        ).order_by('-usage_count')[:10]
        
        serializer = self.get_serializer(templates, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def template_analytics(self, request):
        """Get template usage analytics."""
        from django.db.models import Count, Avg
        
        analytics = {
            'total_templates': self.get_queryset().count(),
            'active_templates': self.get_queryset().filter(is_active=True).count(),
            'system_templates': self.get_queryset().filter(is_system_template=True).count(),
            'custom_templates': self.get_queryset().filter(is_system_template=False).count(),
            'avg_usage': self.get_queryset().aggregate(avg_usage=Avg('usage_count'))['avg_usage'] or 0,
            'most_used': self.get_queryset().order_by('-usage_count').first(),
            'least_used': self.get_queryset().filter(usage_count__gt=0).order_by('usage_count').first(),
            'unused_templates': self.get_queryset().filter(usage_count=0).count(),
        }
        
        # Serialize the most/least used templates
        if analytics['most_used']:
            analytics['most_used'] = self.get_serializer(analytics['most_used']).data
        if analytics['least_used']:
            analytics['least_used'] = self.get_serializer(analytics['least_used']).data
        
        return Response(analytics)


class WorkflowViewSet(viewsets.ViewSet):
    """
    ViewSet for managing role assignment workflows.
    """
    permission_classes = [IsAuthenticated, RBACPermission('can_manage_workflows')]
    
    @action(detail=False, methods=['get'])
    def pending_approvals(self, request):
        """Get pending role assignment approvals."""
        user = request.user
        
        # Filter based on user's school context
        queryset = UserRole.objects.filter(
            requires_approval=True,
            approved_at__isnull=True,
            is_active=False
        ).select_related('user', 'role', 'assigned_by', 'role__school')
        
        if not user.is_superuser:
            # Filter by school context
            if hasattr(user, 'school') and user.school:
                queryset = queryset.filter(
                    role__school=user.school
                )
            else:
                queryset = queryset.filter(role__school__isnull=True)
        
        serializer = UserRoleSerializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    @audit_action('bulk_approval')
    def bulk_approve(self, request):
        """Approve multiple role assignments."""
        assignment_ids = request.data.get('assignment_ids', [])
        
        if not assignment_ids:
            return Response(
                {'error': 'assignment_ids is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        approved_count = 0
        errors = []
        
        with transaction.atomic():
            for assignment_id in assignment_ids:
                try:
                    user_role = UserRole.objects.get(id=assignment_id)
                    
                    # Validate user can approve this assignment
                    if not SchoolContextValidator.validate_role_school_access(request.user, user_role.role):
                        errors.append({'assignment_id': assignment_id, 'error': 'Cannot approve assignment from different school'})
                        continue
                    
                    if RBACManager.approve_role_assignment(user_role, request.user):
                        approved_count += 1
                    else:
                        errors.append({'assignment_id': assignment_id, 'error': 'Assignment does not require approval or already approved'})
                        
                except UserRole.DoesNotExist:
                    errors.append({'assignment_id': assignment_id, 'error': 'Assignment not found'})
                except Exception as e:
                    errors.append({'assignment_id': assignment_id, 'error': str(e)})
        
        return Response({
            'approved_count': approved_count,
            'errors': errors,
            'total_processed': len(assignment_ids)
        })
    
    @action(detail=False, methods=['get'])
    def workflow_statistics(self, request):
        """Get workflow statistics."""
        from django.db.models import Count, Q
        from django.utils import timezone
        from datetime import timedelta
        
        now = timezone.now()
        last_30_days = now - timedelta(days=30)
        
        stats = {
            'pending_approvals': UserRole.objects.filter(
                requires_approval=True,
                approved_at__isnull=True,
                is_active=False
            ).count(),
            'approved_last_30_days': UserRole.objects.filter(
                approved_at__gte=last_30_days
            ).count(),
            'expired_assignments': UserRole.objects.filter(
                expires_at__lt=now,
                is_active=True
            ).count(),
            'temporary_assignments': UserRole.objects.filter(
                expires_at__isnull=False,
                is_active=True
            ).count(),
            'avg_approval_time': self._calculate_avg_approval_time(),
        }
        
        return Response(stats)
    
    def _calculate_avg_approval_time(self):
        """Calculate average approval time in hours."""
        from django.db.models import Avg, F
        
        approved_assignments = UserRole.objects.filter(
            approved_at__isnull=False,
            requires_approval=True
        ).annotate(
            approval_time=F('approved_at') - F('created_at')
        )
        
        if approved_assignments.exists():
            avg_time = approved_assignments.aggregate(
                avg_time=Avg('approval_time')
            )['avg_time']
            
            if avg_time:
                return avg_time.total_seconds() / 3600  # Convert to hours
        
        return 0
    
    @action(detail=False, methods=['post'])
    @audit_action('temporary_role_cleanup')
    def cleanup_expired_roles(self, request):
        """Clean up expired temporary role assignments."""
        from django.utils import timezone
        
        now = timezone.now()
        expired_assignments = UserRole.objects.filter(
            expires_at__lt=now,
            is_active=True
        )
        
        count = expired_assignments.count()
        expired_assignments.update(is_active=False)
        
        return Response({
            'cleaned_up_count': count,
            'message': f'Cleaned up {count} expired role assignments'
        })
