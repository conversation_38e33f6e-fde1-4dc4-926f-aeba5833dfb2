# SMS Project Environment Variables

# Django Settings
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Supabase Configuration
SUPABASE_URL=https://fsznynpmqxljaijcuweb.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZzem55bnBtcXhsamFpamN1d2ViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMzOTY1ODksImV4cCI6MjA2ODk3MjU4OX0.G8X5wXBXSXk3Eu5PLEGMlAOCY5pJq383xG_Eg0wK0E4
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZzem55bnBtcXhsamFpamN1d2ViIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzM5NjU4OSwiZXhwIjoyMDY4OTcyNTg5fQ.IWuqEpxA6SkcR_G1ljqJn_yt0itgsuVPZkklX7csmRI

# Database Configuration (Supabase PostgreSQL)
DATABASE_URL=DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres


# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_LIFETIME=15
JWT_REFRESH_TOKEN_LIFETIME=7

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Email Configuration (for notifications)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# File Storage (Supabase Storage)
SUPABASE_STORAGE_BUCKET=sms-files

# Security Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
