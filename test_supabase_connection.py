#!/usr/bin/env python
"""
Test Supabase connection and query the SMS data.
"""
import os
from dotenv import load_dotenv
from supabase import create_client, Client

# Load environment variables
load_dotenv()

def test_supabase_connection():
    """Test connection to Supabase and query SMS data"""
    print("=== Testing Supabase Connection ===\n")
    
    # Initialize Supabase client
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        print("❌ Missing Supabase credentials in .env file")
        return
    
    print(f"Supabase URL: {url}")
    print(f"Supabase Key: {key[:20]}...")
    
    try:
        supabase: Client = create_client(url, key)
        print("✅ Supabase client created successfully")
        
        # Test 1: Query schools
        print("\n--- Testing Schools Query ---")
        schools_response = supabase.table('schools').select('*').execute()
        schools = schools_response.data
        print(f"✅ Found {len(schools)} schools:")
        for school in schools:
            print(f"  - {school['name']} ({school['slug']}) - {school['subscription_plan']}")
        
        # Test 2: Query users
        print("\n--- Testing Users Query ---")
        users_response = supabase.table('users').select('*, schools(name)').execute()
        users = users_response.data
        print(f"✅ Found {len(users)} users:")
        for user in users:
            school_name = user.get('schools', {}).get('name', 'No School') if user.get('schools') else 'No School'
            print(f"  - {user['email']} ({user['user_type']}) - {school_name}")
        
        # Test 3: Query courses
        print("\n--- Testing Courses Query ---")
        courses_response = supabase.table('courses').select('*, schools(name)').execute()
        courses = courses_response.data
        print(f"✅ Found {len(courses)} courses:")
        for course in courses:
            school_name = course.get('schools', {}).get('name', 'No School') if course.get('schools') else 'No School'
            print(f"  - {course['code']}: {course['name']} - {school_name}")
        
        # Test 4: Test multi-tenant filtering
        print("\n--- Testing Multi-Tenant Filtering ---")
        greenwood_users = supabase.table('users').select('*').eq('school_id', schools[0]['id']).execute()
        riverside_users = supabase.table('users').select('*').eq('school_id', schools[1]['id']).execute()
        
        print(f"✅ Greenwood High School has {len(greenwood_users.data)} users")
        print(f"✅ Riverside Academy has {len(riverside_users.data)} users")
        
        print("\n🎉 All Supabase tests passed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {str(e)}")
        return False

if __name__ == '__main__':
    test_supabase_connection()
