<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-1453" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0.00000762939453125 0 1527.47412109375 2304.02099609375" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-1453{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-1453 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-1453 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-1453 .error-icon{fill:#a44141;}#graph-1453 .error-text{fill:#ddd;stroke:#ddd;}#graph-1453 .edge-thickness-normal{stroke-width:1px;}#graph-1453 .edge-thickness-thick{stroke-width:3.5px;}#graph-1453 .edge-pattern-solid{stroke-dasharray:0;}#graph-1453 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-1453 .edge-pattern-dashed{stroke-dasharray:3;}#graph-1453 .edge-pattern-dotted{stroke-dasharray:2;}#graph-1453 .marker{fill:lightgrey;stroke:lightgrey;}#graph-1453 .marker.cross{stroke:lightgrey;}#graph-1453 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-1453 p{margin:0;}#graph-1453 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-1453 .cluster-label text{fill:#F9FFFE;}#graph-1453 .cluster-label span{color:#F9FFFE;}#graph-1453 .cluster-label span p{background-color:transparent;}#graph-1453 .label text,#graph-1453 span{fill:#ccc;color:#ccc;}#graph-1453 .node rect,#graph-1453 .node circle,#graph-1453 .node ellipse,#graph-1453 .node polygon,#graph-1453 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-1453 .rough-node .label text,#graph-1453 .node .label text,#graph-1453 .image-shape .label,#graph-1453 .icon-shape .label{text-anchor:middle;}#graph-1453 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-1453 .rough-node .label,#graph-1453 .node .label,#graph-1453 .image-shape .label,#graph-1453 .icon-shape .label{text-align:center;}#graph-1453 .node.clickable{cursor:pointer;}#graph-1453 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-1453 .arrowheadPath{fill:lightgrey;}#graph-1453 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-1453 .flowchart-link{stroke:lightgrey;fill:none;}#graph-1453 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1453 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-1453 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1453 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-1453 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-1453 .cluster text{fill:#F9FFFE;}#graph-1453 .cluster span{color:#F9FFFE;}#graph-1453 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-1453 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-1453 rect.text{fill:none;stroke-width:0;}#graph-1453 .icon-shape,#graph-1453 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1453 .icon-shape p,#graph-1453 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-1453 .icon-shape rect,#graph-1453 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1453 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-1453 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-1453 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-1453 .process&gt;*{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1453 .process span{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1453 .process tspan{fill:#000!important;}#graph-1453 .decision&gt;*{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1453 .decision span{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1453 .decision tspan{fill:#000!important;}#graph-1453 .parallel&gt;*{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1453 .parallel span{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1453 .parallel tspan{fill:#000!important;}#graph-1453 .error&gt;*{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1453 .error span{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1453 .error tspan{fill:#000!important;}</style><g><marker id="graph-1453_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1453_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1453_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1453_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1453_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1453_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M651.784,47L651.784,51.167C651.784,55.333,651.784,63.667,651.784,71.333C651.784,79,651.784,86,651.784,89.5L651.784,93" id="L_START_SELECT_COURSE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M651.784,151L651.784,155.167C651.784,159.333,651.784,167.667,651.784,175.333C651.784,183,651.784,190,651.784,193.5L651.784,197" id="L_SELECT_COURSE_LOAD_STUDENTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M651.784,255L651.784,259.167C651.784,263.333,651.784,271.667,651.784,279.333C651.784,287,651.784,294,651.784,297.5L651.784,301" id="L_LOAD_STUDENTS_SELECT_ASSESSMENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M651.784,359L651.784,363.167C651.784,367.333,651.784,375.667,651.784,383.333C651.784,391,651.784,398,651.784,401.5L651.784,405" id="L_SELECT_ASSESSMENT_GRADE_ENTRY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M726.245,463L737.736,467.167C749.227,471.333,772.208,479.667,783.77,487.417C795.331,495.167,795.471,502.334,795.541,505.917L795.612,509.501" id="L_GRADE_ENTRY_VALIDATE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M745.183,611.993L714.399,626.494C683.614,640.995,622.045,669.998,585.76,690.186C549.474,710.375,538.471,721.75,532.969,727.437L527.468,733.125" id="L_VALIDATE_ERROR_MSG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M492.369,736L490.952,729.833C489.536,723.667,486.703,711.333,485.286,686.583C483.87,661.833,483.87,624.667,483.87,589.5C483.87,554.333,483.87,521.167,498.47,500.062C513.069,478.957,542.269,469.915,556.868,465.394L571.468,460.872" id="L_ERROR_MSG_GRADE_ENTRY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M795.69,662.5L795.607,668.583C795.523,674.667,795.357,686.833,795.273,698.417C795.19,710,795.19,721,795.19,726.5L795.19,732" id="L_VALIDATE_AUTO_SAVE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M795.19,790L795.19,794.167C795.19,798.333,795.19,806.667,795.19,814.333C795.19,822,795.19,829,795.19,832.5L795.19,836" id="L_AUTO_SAVE_REVIEW_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M795.19,894L795.19,898.167C795.19,902.333,795.19,910.667,788.661,923.488C782.133,936.31,769.075,953.619,762.547,962.274L756.018,970.929" id="L_REVIEW_EDIT_MORE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M694.365,974.122L687.268,964.935C680.171,955.748,665.977,937.374,658.881,919.52C651.784,901.667,651.784,884.333,651.784,867C651.784,849.667,651.784,832.333,651.784,815C651.784,797.667,651.784,780.333,651.784,761C651.784,741.667,651.784,720.333,651.784,691.083C651.784,661.833,651.784,624.667,651.784,589.5C651.784,554.333,651.784,521.167,651.784,501.083C651.784,481,651.784,474,651.784,470.5L651.784,467" id="L_EDIT_MORE_GRADE_ENTRY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M723.987,1080.646L723.904,1086.729C723.82,1092.813,723.654,1104.979,723.57,1116.563C723.487,1128.146,723.487,1139.146,723.487,1144.646L723.487,1150.146" id="L_EDIT_MORE_FINAL_SUBMIT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M723.487,1208.146L723.487,1212.313C723.487,1216.479,723.487,1224.813,723.487,1232.479C723.487,1240.146,723.487,1247.146,723.487,1250.646L723.487,1254.146" id="L_FINAL_SUBMIT_LOCK_GRADES_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M821.013,1293.42L906.908,1300.708C992.803,1307.995,1164.593,1322.571,1250.488,1346.835C1336.383,1371.099,1336.383,1405.052,1336.383,1441.005C1336.383,1476.958,1336.383,1514.911,1296.41,1541.666C1256.436,1568.42,1176.49,1583.976,1136.517,1591.754L1096.544,1599.531" id="L_LOCK_GRADES_NOTIFY_STUDENTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M821.013,1292.419L920.968,1299.874C1020.924,1307.328,1220.834,1322.237,1320.79,1346.668C1420.745,1371.099,1420.745,1405.052,1420.745,1441.005C1420.745,1476.958,1420.745,1514.911,1420.745,1544.555C1420.745,1574.198,1420.745,1595.531,1420.745,1614.865C1420.745,1634.198,1420.745,1651.531,1420.745,1668.865C1420.745,1686.198,1420.745,1703.531,1420.745,1720.865C1420.745,1738.198,1420.745,1755.531,1420.745,1783.128C1420.745,1810.724,1420.745,1848.583,1420.745,1888.443C1420.745,1928.302,1420.745,1970.161,1420.745,1996.591C1420.745,2023.021,1420.745,2034.021,1420.745,2039.521L1420.745,2045.021" id="L_LOCK_GRADES_UPDATE_DASHBOARD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M686.236,1312.146L680.488,1316.313C674.739,1320.479,663.242,1328.813,657.493,1349.956C651.745,1371.099,651.745,1405.052,651.745,1441.005C651.745,1476.958,651.745,1514.911,651.745,1544.555C651.745,1574.198,651.745,1595.531,651.745,1614.865C651.745,1634.198,651.745,1651.531,651.745,1668.865C651.745,1686.198,651.745,1703.531,651.745,1720.865C651.745,1738.198,651.745,1755.531,651.745,1783.128C651.745,1810.724,651.745,1848.583,651.745,1888.443C651.745,1928.302,651.745,1970.161,651.745,1996.591C651.745,2023.021,651.745,2034.021,651.745,2039.521L651.745,2045.021" id="L_LOCK_GRADES_AUDIT_LOG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M625.961,1301.161L589.439,1307.159C552.917,1313.156,479.872,1325.151,443.35,1348.125C406.828,1371.099,406.828,1405.052,406.828,1441.005C406.828,1476.958,406.828,1514.911,406.828,1544.555C406.828,1574.198,406.828,1595.531,406.828,1614.865C406.828,1634.198,406.828,1651.531,406.828,1668.865C406.828,1686.198,406.828,1703.531,406.828,1720.865C406.828,1738.198,406.828,1755.531,406.828,1783.128C406.828,1810.724,406.828,1848.583,406.828,1888.443C406.828,1928.302,406.828,1970.161,406.828,1996.591C406.828,2023.021,406.828,2034.021,406.828,2039.521L406.828,2045.021" id="L_LOCK_GRADES_CALC_STATS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M1092.617,1633.602L1122.518,1639.479C1152.418,1645.356,1212.22,1657.11,1242.12,1666.488C1272.021,1675.865,1272.021,1682.865,1272.021,1686.365L1272.021,1689.865" id="L_NOTIFY_STUDENTS_WEBSOCKET_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M1012.452,1643.865L1013.222,1648.031C1013.992,1652.198,1015.533,1660.531,1016.303,1668.198C1017.073,1675.865,1017.073,1682.865,1017.073,1686.365L1017.073,1689.865" id="L_NOTIFY_STUDENTS_PUSH_NOTIF_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M922.305,1636.444L898.803,1641.847C875.302,1647.251,828.299,1658.058,804.798,1666.961C781.297,1675.865,781.297,1682.865,781.297,1686.365L781.297,1689.865" id="L_NOTIFY_STUDENTS_EMAIL_NOTIF_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M1272.021,1747.865L1272.021,1752.031C1272.021,1756.198,1272.021,1764.531,1240.434,1782.888C1208.847,1801.244,1145.674,1829.624,1114.087,1843.814L1082.5,1858.004" id="L_WEBSOCKET_DELIVERY_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M1017.073,1747.865L1017.073,1752.031C1017.073,1756.198,1017.073,1764.531,1017.143,1772.281C1017.213,1780.032,1017.354,1787.198,1017.424,1790.782L1017.495,1794.365" id="L_PUSH_NOTIF_DELIVERY_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M781.297,1747.865L781.297,1752.031C781.297,1756.198,781.297,1764.531,810.113,1782.621C838.928,1800.711,896.559,1828.558,925.375,1842.482L954.191,1856.405" id="L_EMAIL_NOTIF_DELIVERY_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M1078.335,1914.759L1113.844,1930.969C1149.354,1947.179,1220.372,1979.6,1224.241,2003.192C1228.109,2026.785,1164.828,2041.549,1133.187,2048.931L1101.546,2056.313" id="L_DELIVERY_CHECK_RETRY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M986.484,2049.021L979.497,2042.854C972.511,2036.688,958.538,2024.354,957.977,2007.275C957.417,1990.195,970.268,1968.37,976.694,1957.457L983.12,1946.544" id="L_RETRY_DELIVERY_CHECK_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M940.012,1897.96L805.516,1916.97C671.02,1935.98,402.028,1974.001,267.532,1998.511C133.036,2023.021,133.036,2034.021,133.036,2039.521L133.036,2045.021" id="L_DELIVERY_CHECK_UPDATE_STATUS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M1420.745,2103.021L1420.745,2107.188C1420.745,2111.354,1420.745,2119.688,1294.47,2131.22C1168.196,2142.752,915.647,2157.484,789.372,2164.85L663.097,2172.215" id="L_UPDATE_DASHBOARD_COMPLETE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M651.745,2103.021L651.745,2107.188C651.745,2111.354,651.745,2119.688,642.546,2127.76C633.347,2135.833,614.95,2143.645,605.751,2147.551L596.552,2151.457" id="L_AUDIT_LOG_COMPLETE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M406.828,2103.021L406.828,2107.188C406.828,2111.354,406.828,2119.688,416.027,2127.76C425.226,2135.833,443.623,2143.645,452.822,2147.551L462.021,2151.457" id="L_CALC_STATS_COMPLETE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M133.036,2103.021L133.036,2107.188C133.036,2111.354,133.036,2119.688,176.781,2129.595C220.525,2139.502,308.014,2150.983,351.758,2156.724L395.503,2162.464" id="L_UPDATE_STATUS_COMPLETE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M529.286,2207.021L529.286,2211.188C529.286,2215.354,529.286,2223.688,529.286,2231.354C529.286,2239.021,529.286,2246.021,529.286,2249.521L529.286,2253.021" id="L_COMPLETE_END_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M757.121,1312.146L762.311,1316.313C767.501,1320.479,777.882,1328.813,783.143,1336.563C788.404,1344.313,788.544,1351.48,788.614,1355.063L788.685,1358.647" id="L_LOCK_GRADES_CONFLICT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M738.643,1466.245L711.345,1480.682C684.046,1495.118,629.449,1523.991,598.146,1544.052C566.844,1564.112,558.837,1575.359,554.833,1580.982L550.829,1586.606" id="L_CONFLICT_RESOLVE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M507.674,1589.865L502.738,1583.698C497.802,1577.531,487.93,1565.198,482.993,1540.055C478.057,1514.911,478.057,1476.958,478.057,1441.005C478.057,1405.052,478.057,1371.099,502.056,1349.038C526.054,1326.977,574.051,1316.807,598.049,1311.723L622.048,1306.638" id="L_RESOLVE_LOCK_GRADES_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path><path d="M788.763,1516.365L788.68,1522.448C788.596,1528.531,788.43,1540.698,810.047,1553.117C831.664,1565.536,875.064,1578.208,896.765,1584.544L918.465,1590.88" id="L_CONFLICT_NOTIFY_STUDENTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1453_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(560.4765825271606, 699)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(795.1901230812073, 699)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(651.7838730812073, 763)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(723.4869980812073, 1117.1458435058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1291.3906540870667, 2012.0208435058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(133.03646087646484, 2012.0208435058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(574.8515739440918, 1552.8645935058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(788.2630410194397, 1552.8645935058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default" id="flowchart-START-0" transform="translate(651.7838730812073, 27.5)"><rect class="basic label-container" style="" rx="19.5" ry="19.5" x="-64.296875" y="-19.5" width="128.59375" height="39"></rect><g class="label" style="" transform="translate(-51.921875, -12)"><rect></rect><foreignObject width="103.84375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Lecturer Login</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-SELECT_COURSE-1" transform="translate(651.7838730812073, 124)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-78.640625" y="-27" width="157.28125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-48.640625, -12)"><rect></rect><foreignObject width="97.28125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Select Course</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-LOAD_STUDENTS-3" transform="translate(651.7838730812073, 228)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-92.32291793823242" y="-27" width="184.64583587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-62.32291793823242, -12)"><rect></rect><foreignObject width="124.64583587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Load Student List</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-SELECT_ASSESSMENT-5" transform="translate(651.7838730812073, 332)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-113.375" y="-27" width="226.75" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-83.375, -12)"><rect></rect><foreignObject width="166.75" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Select Assessment Type</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-GRADE_ENTRY-7" transform="translate(651.7838730812073, 436)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-76.49479293823242" y="-27" width="152.98958587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-46.49479293823242, -12)"><rect></rect><foreignObject width="92.98958587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Enter Grades</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-VALIDATE-9" transform="translate(795.1901230812073, 587.5)"><polygon points="74.5,0 149,-74.5 74.5,-149 0,-74.5" class="label-container" transform="translate(-74.5,74.5)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-47.5, -12)"><rect></rect><foreignObject width="95" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Grades Valid?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-ERROR_MSG-11" transform="translate(498.5703339576721, 763)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-106.88542175292969" y="-27" width="213.77084350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-76.88542175292969, -12)"><rect></rect><foreignObject width="153.77084350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Show Validation Error</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-AUTO_SAVE-15" transform="translate(795.1901230812073, 763)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-97.078125" y="-27" width="194.15625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-67.078125, -12)"><rect></rect><foreignObject width="134.15625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Auto-save Progress</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-REVIEW-17" transform="translate(795.1901230812073, 867)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-82.44271087646484" y="-27" width="164.8854217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-52.442710876464844, -12)"><rect></rect><foreignObject width="104.88542175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Review Entries</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-EDIT_MORE-19" transform="translate(723.4869980812073, 1012.0729217529297)"><polygon points="68.07291793823242,0 136.14583587646484,-68.07291793823242 68.07291793823242,-136.14583587646484 0,-68.07291793823242" class="label-container" transform="translate(-68.07291793823242,68.07291793823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-41.07291793823242, -12)"><rect></rect><foreignObject width="82.14583587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Need Edits?</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-FINAL_SUBMIT-23" transform="translate(723.4869980812073, 1181.1458435058594)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-88.84375" y="-27" width="177.6875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-58.84375, -12)"><rect></rect><foreignObject width="117.6875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Final Submission</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-LOCK_GRADES-25" transform="translate(723.4869980812073, 1285.1458435058594)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-97.52604675292969" y="-27" width="195.05209350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-67.52604675292969, -12)"><rect></rect><foreignObject width="135.05209350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Lock Grade Entries</p></span></div></foreignObject></g></g><g class="node default parallel" id="flowchart-NOTIFY_STUDENTS-27" transform="translate(1007.4609589576721, 1616.8645935058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-85.15625" y="-27" width="170.3125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-55.15625, -12)"><rect></rect><foreignObject width="110.3125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Notify Students</p></span></div></foreignObject></g></g><g class="node default parallel" id="flowchart-UPDATE_DASHBOARD-29" transform="translate(1420.7448196411133, 2076.0208435058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-98.72917175292969" y="-27" width="197.45834350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-68.72917175292969, -12)"><rect></rect><foreignObject width="137.45834350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update Dashboards</p></span></div></foreignObject></g></g><g class="node default parallel" id="flowchart-AUDIT_LOG-31" transform="translate(651.7448043823242, 2076.0208435058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-89.734375" y="-27" width="179.46875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-59.734375, -12)"><rect></rect><foreignObject width="119.46875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Create Audit Log</p></span></div></foreignObject></g></g><g class="node default parallel" id="flowchart-CALC_STATS-33" transform="translate(406.82813262939453, 2076.0208435058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-98.75521087646484" y="-27" width="197.5104217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-68.75521087646484, -12)"><rect></rect><foreignObject width="137.5104217529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Calculate Statistics</p></span></div></foreignObject></g></g><g class="node default parallel" id="flowchart-WEBSOCKET-35" transform="translate(1272.0208587646484, 1720.8645935058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-113.72396087646484" y="-27" width="227.4479217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-83.72396087646484, -12)"><rect></rect><foreignObject width="167.4479217529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>WebSocket Notification</p></span></div></foreignObject></g></g><g class="node default parallel" id="flowchart-PUSH_NOTIF-37" transform="translate(1017.0729370117188, 1720.8645935058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-91.22396087646484" y="-27" width="182.4479217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-61.223960876464844, -12)"><rect></rect><foreignObject width="122.44792175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Push Notification</p></span></div></foreignObject></g></g><g class="node default parallel" id="flowchart-EMAIL_NOTIF-39" transform="translate(781.2968902587891, 1720.8645935058594)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-94.55208587646484" y="-27" width="189.1041717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-64.55208587646484, -12)"><rect></rect><foreignObject width="129.1041717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Email Notification</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-DELIVERY_CHECK-41" transform="translate(1017.0729370117188, 1886.4427185058594)"><polygon points="88.578125,0 177.15625,-88.578125 88.578125,-177.15625 0,-88.578125" class="label-container" transform="translate(-88.578125,88.578125)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-61.578125, -12)"><rect></rect><foreignObject width="123.15625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Delivery Success?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-RETRY-47" transform="translate(1017.0729370117188, 2076.0208435058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-80.578125" y="-27" width="161.15625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-50.578125, -12)"><rect></rect><foreignObject width="101.15625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Retry Delivery</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-UPDATE_STATUS-51" transform="translate(133.03646087646484, 2076.0208435058594)"><rect class="basic label-container" style="" x="-125.03646087646484" y="-27" width="250.0729217529297" height="54"></rect><g class="label" style="" transform="translate(-95.03646087646484, -12)"><rect></rect><foreignObject width="190.0729217529297" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Update Notification Status</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-COMPLETE-53" transform="translate(529.2864685058594, 2180.0208435058594)"><rect class="basic label-container" style="" x="-129.81771087646484" y="-27" width="259.6354217529297" height="54"></rect><g class="label" style="" transform="translate(-99.81771087646484, -12)"><rect></rect><foreignObject width="199.6354217529297" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Grade Submission Complete</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-END-61" transform="translate(529.2864685058594, 2276.5208435058594)"><rect class="basic label-container" style="" rx="19.5" ry="19.5" x="-54.1875" y="-19.5" width="108.375" height="39"></rect><g class="label" style="" transform="translate(-41.8125, -12)"><rect></rect><foreignObject width="83.625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>End Process</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-CONFLICT-63" transform="translate(788.2630410194397, 1439.0052185058594)"><polygon points="76.859375,0 153.71875,-76.859375 76.859375,-153.71875 0,-76.859375" class="label-container" transform="translate(-76.859375,76.859375)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-49.859375, -12)"><rect></rect><foreignObject width="99.71875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Save Conflict?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-RESOLVE-65" transform="translate(529.2864685058594, 1616.8645935058594)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-87.45833587646484" y="-27" width="174.9166717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-57.458335876464844, -12)"><rect></rect><foreignObject width="114.91667175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Resolve Conflict</p></span></div></foreignObject></g></g></g></g></g></svg>