"""
Comprehensive test suite for the complete RBAC system.
"""

import json
from datetime import <PERSON><PERSON><PERSON>
from django.test import TestCase, TransactionTestCase
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission
from django.utils import timezone
from django.urls import reverse
from django.db.models import Count
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from schools.models import School
from users.models import Role, UserRole, RoleTemplate, RoleAuditLog, RolePermission
from users.rbac_utils import RBACManager
from users.audit_management import AuditManager, AuditRetentionPolicy
from users.multi_tenant_managers import SchoolContextValidator

User = get_user_model()


class RBACSystemIntegrationTest(TransactionTestCase):
    """
    Integration tests for the complete RBAC system.
    """

    def setUp(self):
        """Set up test data."""
        # Create schools
        self.school1 = School.objects.create(
            name="Test School 1",
            address="123 Test St",
            phone="************",
            email="<EMAIL>"
        )
        self.school2 = School.objects.create(
            name="Test School 2", 
            address="456 Test Ave",
            phone="************",
            email="<EMAIL>"
        )

        # Create users
        self.superuser = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Super',
            last_name='User',
            user_type='system',
            is_superuser=True,
            is_staff=True
        )

        self.admin1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='One',
            user_type='admin',
            school=self.school1
        )

        self.admin2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='Two',
            user_type='admin',
            school=self.school2
        )

        self.teacher1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Teacher',
            last_name='One',
            user_type='teacher',
            school=self.school1
        )

        # Create permissions
        self.permissions = []
        permission_data = [
            ('can_manage_roles', 'Can manage roles'),
            ('can_assign_roles', 'Can assign roles'),
            ('can_view_audit_logs', 'Can view audit logs'),
            ('can_manage_students', 'Can manage students'),
            ('can_view_analytics', 'Can view analytics'),
        ]

        for codename, name in permission_data:
            perm, created = Permission.objects.get_or_create(
                codename=codename,
                defaults={'name': name, 'content_type_id': 1}
            )
            self.permissions.append(perm)

    def test_complete_rbac_workflow(self):
        """Test complete RBAC workflow from template to assignment."""
        # 1. Create role template
        template = RoleTemplate.objects.create(
            name='School Administrator',
            description='Full school administration access',
            is_system_template=True,
            created_by=self.superuser
        )
        template.permissions.set(self.permissions[:3])

        # 2. Create role from template
        role = RBACManager.create_role_from_template(
            template_name='School Administrator',
            role_name='School 1 Admin',
            school=self.school1,
            created_by=self.superuser,
            description='Administrator for School 1'
        )

        self.assertEqual(role.name, 'School 1 Admin')
        self.assertEqual(role.school, self.school1)
        self.assertEqual(role.role_permissions.count(), 3)

        # 3. Assign role to user with approval workflow
        user_role = RBACManager.assign_role_to_user(
            user=self.admin1,
            role=role,
            assigned_by=self.superuser,
            requires_approval=True
        )

        self.assertTrue(user_role.requires_approval)
        self.assertFalse(user_role.is_active)
        self.assertIsNone(user_role.approved_at)

        # 4. Approve role assignment
        approval_result = RBACManager.approve_role_assignment(user_role, self.superuser)
        self.assertTrue(approval_result)

        user_role.refresh_from_db()
        self.assertTrue(user_role.is_active)
        self.assertIsNotNone(user_role.approved_at)

        # 5. Verify user has permissions
        self.assertTrue(RBACManager.user_has_permission(self.admin1, 'can_manage_roles'))
        self.assertTrue(RBACManager.user_has_permission(self.admin1, 'can_assign_roles'))

        # 6. Verify audit logs were created
        audit_logs = RoleAuditLog.objects.filter(target_user=self.admin1)
        self.assertGreater(audit_logs.count(), 0)

        # Check specific audit log types
        assignment_log = audit_logs.filter(action_type='user_role_assigned').first()
        self.assertIsNotNone(assignment_log)
        self.assertEqual(assignment_log.performed_by, self.superuser)

        approval_log = audit_logs.filter(action_type='role_approved').first()
        self.assertIsNotNone(approval_log)

    def test_multi_tenant_isolation(self):
        """Test multi-tenant isolation works correctly."""
        # Create roles for different schools
        role1 = Role.objects.create(
            name='School 1 Teacher',
            description='Teacher role for school 1',
            school=self.school1,
            role_type='school',
            created_by=self.admin1
        )

        role2 = Role.objects.create(
            name='School 2 Teacher',
            description='Teacher role for school 2',
            school=self.school2,
            role_type='school',
            created_by=self.admin2
        )

        # Test school context validation
        self.assertTrue(SchoolContextValidator.validate_user_school_access(self.admin1, self.school1))
        self.assertFalse(SchoolContextValidator.validate_user_school_access(self.admin1, self.school2))

        self.assertTrue(SchoolContextValidator.validate_role_school_access(self.admin1, role1))
        self.assertFalse(SchoolContextValidator.validate_role_school_access(self.admin1, role2))

        # Test cross-school role assignment prevention
        with self.assertRaises(ValueError):
            RBACManager.assign_role_to_user(
                user=self.teacher1,  # School 1 user
                role=role2,          # School 2 role
                assigned_by=self.admin1
            )

    def test_audit_management_system(self):
        """Test audit management and retention policies."""
        # Create some audit logs
        for i in range(10):
            AuditManager.create_audit_log(
                action_type='role_created',
                description=f'Test role {i} created',
                performed_by=self.admin1,
                school=self.school1
            )

        # Test audit statistics
        stats = AuditManager.get_audit_statistics(days=30)
        self.assertGreater(stats['total_logs'], 0)
        self.assertIn('action_breakdown', stats)
        self.assertIn('user_activity', stats)

        # Test audit search
        search_results = AuditManager.search_audit_logs(
            action_types=['role_created'],
            user_email='<EMAIL>',
            limit=5
        )
        self.assertLessEqual(len(search_results), 5)

        # Test compliance report
        start_date = timezone.now() - timedelta(days=1)
        end_date = timezone.now()
        
        report = AuditManager.generate_compliance_report(
            start_date=start_date,
            end_date=end_date,
            school=self.school1
        )
        
        self.assertIn('summary', report)
        self.assertIn('action_breakdown', report)
        self.assertGreater(report['summary']['total_audit_entries'], 0)

    def test_role_template_validation(self):
        """Test role template validation and management."""
        # Create template with no permissions
        template = RoleTemplate.objects.create(
            name='Empty Template',
            description='Template with no permissions',
            created_by=self.superuser
        )

        # Test template usage tracking
        initial_usage = template.usage_count
        
        # Create role from template
        RBACManager.create_role_from_template(
            template_name='Empty Template',
            role_name='Test Role',
            school=self.school1,
            created_by=self.admin1
        )

        template.refresh_from_db()
        self.assertEqual(template.usage_count, initial_usage + 1)

    def test_temporary_role_assignments(self):
        """Test temporary role assignments with expiration."""
        # Create role
        role = Role.objects.create(
            name='Temporary Role',
            description='Role with expiration',
            school=self.school1,
            role_type='school',
            created_by=self.admin1
        )

        # Assign temporary role
        expires_at = timezone.now() + timedelta(hours=1)
        user_role = RBACManager.assign_role_to_user(
            user=self.teacher1,
            role=role,
            assigned_by=self.admin1,
            expires_at=expires_at
        )

        self.assertEqual(user_role.expires_at, expires_at)
        self.assertFalse(user_role.is_expired)

        # Test expiration check
        user_role.expires_at = timezone.now() - timedelta(hours=1)
        user_role.save()
        self.assertTrue(user_role.is_expired)

    def test_security_alerts(self):
        """Test security alert generation."""
        # Create multiple failed role assignments to trigger alert
        for i in range(6):
            AuditManager.create_audit_log(
                action_type='role_denied',
                description=f'Failed assignment {i}',
                performed_by=self.admin1,
                school=self.school1
            )

        # Check that security alert was created
        security_alerts = RoleAuditLog.objects.filter(action_type='security_alert')
        self.assertGreater(security_alerts.count(), 0)

    def test_retention_policy(self):
        """Test audit log retention policy."""
        # Create old audit logs
        old_date = timezone.now() - timedelta(days=400)
        
        old_log = RoleAuditLog.objects.create(
            action_type='role_created',
            description='Old log entry',
            performed_by=self.admin1,
            created_at=old_date
        )

        # Apply retention policy
        results = AuditRetentionPolicy.apply_retention_policy()
        
        # Check that old log was deleted
        self.assertFalse(RoleAuditLog.objects.filter(id=old_log.id).exists())
        self.assertGreater(results['non_critical_deleted'], 0)


class RBACAPITestCase(APITestCase):
    """
    API tests for RBAC endpoints.
    """

    def setUp(self):
        """Set up test data for API tests."""
        self.school = School.objects.create(
            name="API Test School",
            address="123 API St",
            phone="************",
            email="<EMAIL>"
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='User',
            user_type='admin',
            school=self.school,
            is_staff=True
        )

        self.client = APIClient()
        self.client.force_authenticate(user=self.admin_user)

        # Create permissions
        self.permission = Permission.objects.get_or_create(
            codename='can_manage_roles',
            defaults={'name': 'Can manage roles', 'content_type_id': 1}
        )[0]

    def test_role_template_api(self):
        """Test role template API endpoints."""
        # Create template via API
        template_data = {
            'name': 'API Test Template',
            'description': 'Template created via API',
            'is_active': True,
            'permission_ids': [self.permission.id]
        }

        response = self.client.post('/api/rbac/role-templates/', template_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        template_id = response.data['id']

        # Test template validation
        response = self.client.post(f'/api/rbac/role-templates/{template_id}/validate_template/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('is_valid', response.data)

        # Test role creation from template
        role_data = {
            'role_name': 'API Created Role',
            'school_id': str(self.school.id),
            'description': 'Role created from template via API'
        }

        response = self.client.post(f'/api/rbac/role-templates/{template_id}/create_role/', role_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'API Created Role')

    def test_workflow_api(self):
        """Test workflow management API endpoints."""
        # Test pending approvals endpoint
        response = self.client.get('/api/rbac/workflows/pending_approvals/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data, list)

        # Test workflow statistics
        response = self.client.get('/api/rbac/workflows/workflow_statistics/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('pending_approvals', response.data)

    def test_analytics_api(self):
        """Test analytics API endpoints."""
        # Test dashboard stats
        response = self.client.get('/api/rbac/analytics/dashboard_stats/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('stats', response.data)
        self.assertIn('activity_trends', response.data)

        # Test role usage analytics
        response = self.client.get('/api/rbac/analytics/role_usage_analytics/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('role_stats', response.data)

        # Test security analytics
        response = self.client.get('/api/rbac/analytics/security_analytics/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('security_score', response.data)

        # Test compliance report
        response = self.client.get('/api/rbac/analytics/compliance_report/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_audit_entries', response.data)


class RBACPerformanceTest(TestCase):
    """
    Performance tests for RBAC system.
    """

    def setUp(self):
        """Set up performance test data."""
        self.school = School.objects.create(
            name="Performance Test School",
            address="123 Perf St",
            phone="************",
            email="<EMAIL>"
        )

        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type='admin',
            school=self.school
        )

    def test_bulk_role_assignment_performance(self):
        """Test performance of bulk role assignments."""
        # Create role
        role = Role.objects.create(
            name='Performance Test Role',
            school=self.school,
            role_type='school',
            created_by=self.admin_user
        )

        # Create multiple users
        users = []
        for i in range(100):
            user = User.objects.create_user(
                email=f'user{i}@perf.com',
                password='testpass123',
                user_type='student',
                school=self.school
            )
            users.append(user)

        # Time bulk assignment
        import time
        start_time = time.time()

        for user in users:
            RBACManager.assign_role_to_user(
                user=user,
                role=role,
                assigned_by=self.admin_user
            )

        end_time = time.time()
        duration = end_time - start_time

        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(duration, 10.0, "Bulk role assignment took too long")

        # Verify all assignments were created
        self.assertEqual(UserRole.objects.filter(role=role).count(), 100)

    def test_permission_check_performance(self):
        """Test performance of permission checks."""
        # Create user with multiple roles and permissions
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type='teacher',
            school=self.school
        )

        # Create multiple roles with permissions
        for i in range(10):
            role = Role.objects.create(
                name=f'Role {i}',
                school=self.school,
                role_type='school',
                created_by=self.admin_user
            )
            
            RBACManager.assign_role_to_user(
                user=user,
                role=role,
                assigned_by=self.admin_user
            )

        # Time permission checks
        import time
        start_time = time.time()

        for i in range(1000):
            RBACManager.user_has_permission(user, 'can_manage_roles')

        end_time = time.time()
        duration = end_time - start_time

        # Should complete within reasonable time
        self.assertLess(duration, 1.0, "Permission checks took too long")
