from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views
from .api_views import (
    RoleViewSet as RBACRoleViewSet, PermissionViewSet, UserRoleViewSet,
    RoleAuditLogViewSet, UserPermissionViewSet
)
from .template_api_views import RoleTemplateViewSet as EnhancedRoleTemplateViewSet, WorkflowViewSet
from .analytics_api_views import AnalyticsViewSet
from .admin_views import (
    RoleHierarchyView, PermissionMatrixView, RoleAnalyticsView,
    role_hierarchy_json, permission_matrix_json, bulk_role_assignment,
    role_comparison
)
from .secure_auth_views import (
    SecureTokenObtainPairView, SecureTokenRefreshView,
    logout_view, logout_all_devices_view, user_permissions_view
)

app_name = 'users'

# Main router for user management
router = DefaultRouter()
router.register(r'', views.UserViewSet, basename='user')

# RBAC router for role and permission management
rbac_router = DefaultRouter()
rbac_router.register(r'roles', RBACRoleViewSet, basename='rbac-role')
rbac_router.register(r'permissions', PermissionViewSet, basename='permission')
rbac_router.register(r'user-roles', UserRoleViewSet, basename='userrole')
rbac_router.register(r'role-templates', EnhancedRoleTemplateViewSet, basename='roletemplate')
rbac_router.register(r'workflows', WorkflowViewSet, basename='workflow')
rbac_router.register(r'analytics', AnalyticsViewSet, basename='analytics')
rbac_router.register(r'audit-logs', RoleAuditLogViewSet, basename='auditlog')
rbac_router.register(r'user-permissions', UserPermissionViewSet, basename='userpermission')

# Multi-tenant router for enhanced RBAC with school isolation
mt_router = DefaultRouter()
from .multi_tenant_api_views import MultiTenantRoleViewSet, MultiTenantUserRoleViewSet
mt_router.register(r'mt-roles', MultiTenantRoleViewSet, basename='mt-role')
mt_router.register(r'mt-user-roles', MultiTenantUserRoleViewSet, basename='mt-userrole')

urlpatterns = [
    # User management endpoints
    path('', include(router.urls)),

    # RBAC management endpoints
    path('rbac/', include(rbac_router.urls)),

    # Multi-tenant RBAC endpoints
    path('rbac/', include(mt_router.urls)),

    # Enhanced admin views
    path('admin/role-hierarchy/', RoleHierarchyView.as_view(), name='role-hierarchy'),
    path('admin/permission-matrix/', PermissionMatrixView.as_view(), name='permission-matrix'),
    path('admin/role-analytics/', RoleAnalyticsView.as_view(), name='role-analytics'),
    path('admin/bulk-assignment/', bulk_role_assignment, name='bulk-role-assignment'),
    path('admin/role-comparison/', role_comparison, name='role-comparison'),

    # JSON endpoints for admin views
    path('admin/api/role-hierarchy/', role_hierarchy_json, name='role-hierarchy-json'),
    path('admin/api/permission-matrix/', permission_matrix_json, name='permission-matrix-json'),

    # Secure authentication endpoints
    path('auth/login/', SecureTokenObtainPairView.as_view(), name='secure-login'),
    path('auth/refresh/', SecureTokenRefreshView.as_view(), name='secure-refresh'),
    path('auth/logout/', logout_view, name='logout'),
    path('auth/logout-all/', logout_all_devices_view, name='logout-all'),
    path('auth/permissions/', user_permissions_view, name='user-permissions'),
]
