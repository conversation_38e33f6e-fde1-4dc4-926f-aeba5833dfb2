{% load static %}

<div class="role-node level-{{ node.level }}">
    <div class="role-header">
        <div>
            <span class="role-name">{{ node.role.name }}</span>
            <span class="role-type {{ node.role.role_type }}">{{ node.role.role_type }}</span>
            {% if node.children %}
                <span class="expand-toggle">[-]</span>
            {% endif %}
        </div>
        {% if node.role.school %}
            <div class="school-info">{{ node.role.school.name }}</div>
        {% else %}
            <div class="school-info">System Role</div>
        {% endif %}
    </div>
    
    {% if node.role.description %}
        <div class="role-description">{{ node.role.description }}</div>
    {% endif %}
    
    <div class="role-stats">
        <div class="stat-item">
            <span>👥</span>
            <span class="stat-number">{{ node.user_count }}</span>
            <span>Users</span>
        </div>
        <div class="stat-item">
            <span>🔐</span>
            <span class="stat-number">{{ node.permission_count }}</span>
            <span>Permissions</span>
        </div>
        {% if node.role.parent_role %}
            <div class="stat-item">
                <span>⬆️</span>
                <span>Parent: {{ node.role.parent_role.name }}</span>
            </div>
        {% endif %}
    </div>
    
    {% if node.children %}
        <div class="children-container">
            {% for child in node.children %}
                {% include "admin/users/role_node.html" with node=child %}
            {% endfor %}
        </div>
    {% endif %}
</div>
