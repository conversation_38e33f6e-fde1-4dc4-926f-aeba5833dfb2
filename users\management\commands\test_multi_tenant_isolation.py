"""
Management command to test multi-tenant isolation functionality.
"""

from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from django.core.exceptions import PermissionDenied, ValidationError
from django.db import transaction
from datetime import date
from schools.models import School
from users.models import Role, UserRole, Permission
from users.multi_tenant_managers import SchoolContextValidator
from users.cross_school_utils import CrossSchoolRoleComparator

User = get_user_model()


class Command(BaseCommand):
    help = 'Test multi-tenant isolation functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Clean up test data after running tests',
        )

    def handle(self, *args, **options):
        """Run multi-tenant isolation tests."""
        self.stdout.write(self.style.SUCCESS('Starting multi-tenant isolation tests...'))
        
        try:
            with transaction.atomic():
                # Create test data
                self.create_test_data()
                
                # Run isolation tests
                self.test_school_isolation()
                self.test_role_filtering()
                self.test_cross_school_access()
                self.test_role_assignment_validation()
                self.test_cross_school_comparison()
                
                self.stdout.write(self.style.SUCCESS('All multi-tenant isolation tests passed!'))
                
                if options['cleanup']:
                    self.stdout.write('Cleaning up test data...')
                    # Transaction will rollback automatically
                    raise CommandError("Test completed - rolling back changes")
                
        except CommandError:
            # Expected for cleanup
            pass
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Multi-tenant isolation test failed: {e}')
            )
            raise

    def create_test_data(self):
        """Create test schools, users, and roles."""
        self.stdout.write('Creating test data...')
        
        # Create test schools
        self.school_alpha = School.objects.create(
            name="Alpha Academy",
            slug="alpha-academy",
            email="<EMAIL>",
            address_line_1="123 Alpha Street",
            city="Alpha City",
            state_province="Alpha State",
            postal_code="11111",
            country="Alpha Country",
            academic_year_start=date(2024, 9, 1),
            academic_year_end=date(2025, 6, 30),
            status='active'
        )
        
        self.school_beta = School.objects.create(
            name="Beta Institute",
            slug="beta-institute",
            email="<EMAIL>",
            address_line_1="456 Beta Avenue",
            city="Beta City",
            state_province="Beta State",
            postal_code="22222",
            country="Beta Country",
            academic_year_start=date(2024, 9, 1),
            academic_year_end=date(2025, 6, 30),
            status='active'
        )
        
        # Create super admin
        self.super_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Super",
            last_name="Admin",
            user_type="super_admin",
            is_superuser=True,
            is_staff=True
        )
        
        # Create school admins
        self.alpha_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Alpha",
            last_name="Admin",
            user_type="school_admin",
            school=self.school_alpha,
            is_staff=True
        )
        
        self.beta_admin = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Beta",
            last_name="Admin",
            user_type="school_admin",
            school=self.school_beta,
            is_staff=True
        )
        
        # Create teachers
        self.alpha_teacher = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Alpha",
            last_name="Teacher",
            user_type="lecturer",
            school=self.school_alpha
        )
        
        self.beta_teacher = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            first_name="Beta",
            last_name="Teacher",
            user_type="lecturer",
            school=self.school_beta
        )
        
        # Create permissions
        self.permission_teach = Permission.objects.create(
            codename="can_teach_courses",
            name="Can Teach Courses",
            category="academic"
        )
        
        self.permission_admin = Permission.objects.create(
            codename="can_manage_school",
            name="Can Manage School",
            category="admin"
        )
        
        # Create roles
        self.alpha_teacher_role = Role.objects.create(
            name="Teacher",
            description="Teacher role for Alpha Academy",
            school=self.school_alpha,
            role_type="school",
            created_by=self.alpha_admin
        )
        
        self.beta_teacher_role = Role.objects.create(
            name="Teacher",
            description="Teacher role for Beta Institute",
            school=self.school_beta,
            role_type="school",
            created_by=self.beta_admin
        )
        
        self.system_admin_role = Role.objects.create(
            name="System Administrator",
            description="System-wide administrator role",
            school=None,
            role_type="system",
            is_system_role=True,
            created_by=self.super_admin
        )
        
        # Add permissions to roles
        self.alpha_teacher_role.permissions.add(self.permission_teach)
        self.beta_teacher_role.permissions.add(self.permission_teach)
        self.system_admin_role.permissions.add(self.permission_admin)
        
        self.stdout.write(self.style.SUCCESS('Test data created successfully'))

    def test_school_isolation(self):
        """Test that users can only access their own school's data."""
        self.stdout.write('Testing school isolation...')
        
        # Test user access validation
        assert SchoolContextValidator.validate_user_school_access(
            self.alpha_admin, self.alpha_teacher
        ), "Alpha admin should access Alpha teacher"
        
        assert not SchoolContextValidator.validate_user_school_access(
            self.alpha_admin, self.beta_teacher
        ), "Alpha admin should NOT access Beta teacher"
        
        assert SchoolContextValidator.validate_user_school_access(
            self.super_admin, self.alpha_teacher
        ), "Super admin should access any user"
        
        # Test role access validation
        assert SchoolContextValidator.validate_role_school_access(
            self.alpha_admin, self.alpha_teacher_role
        ), "Alpha admin should access Alpha roles"
        
        assert not SchoolContextValidator.validate_role_school_access(
            self.alpha_admin, self.beta_teacher_role
        ), "Alpha admin should NOT access Beta roles"
        
        assert SchoolContextValidator.validate_role_school_access(
            self.alpha_admin, self.system_admin_role
        ), "Any admin should access system roles"
        
        self.stdout.write(self.style.SUCCESS('School isolation tests passed'))

    def test_role_filtering(self):
        """Test role manager filtering by school context."""
        self.stdout.write('Testing role filtering...')
        
        # Alpha admin should see Alpha roles + system roles
        alpha_roles = Role.mt_objects.for_user_school(self.alpha_admin)
        alpha_role_schools = set(role.school for role in alpha_roles)
        
        assert self.school_alpha in alpha_role_schools, "Should see Alpha school roles"
        assert None in alpha_role_schools, "Should see system roles"
        assert self.school_beta not in alpha_role_schools, "Should NOT see Beta school roles"
        
        # Super admin should see all roles
        all_roles = Role.mt_objects.for_user_school(self.super_admin)
        assert all_roles.count() >= 3, "Super admin should see all roles"
        
        self.stdout.write(self.style.SUCCESS('Role filtering tests passed'))

    def test_cross_school_access(self):
        """Test cross-school access restrictions."""
        self.stdout.write('Testing cross-school access restrictions...')
        
        # Test accessible schools
        alpha_schools = SchoolContextValidator.get_accessible_schools(self.alpha_admin)
        assert alpha_schools.count() == 1, "Alpha admin should access only Alpha school"
        assert self.school_alpha in alpha_schools, "Should include Alpha school"
        
        super_schools = SchoolContextValidator.get_accessible_schools(self.super_admin)
        assert super_schools.count() >= 2, "Super admin should access all schools"
        
        self.stdout.write(self.style.SUCCESS('Cross-school access tests passed'))

    def test_role_assignment_validation(self):
        """Test role assignment validation with multi-tenant constraints."""
        self.stdout.write('Testing role assignment validation...')
        
        # Valid assignment within same school
        user_role = UserRole.mt_objects.assign_role(
            user=self.alpha_teacher,
            role=self.alpha_teacher_role,
            assigned_by=self.alpha_admin
        )
        assert user_role is not None, "Valid assignment should succeed"
        assert user_role.is_active, "Assignment should be active"
        
        # Invalid cross-school assignment should fail
        try:
            UserRole.mt_objects.assign_role(
                user=self.alpha_teacher,
                role=self.beta_teacher_role,  # Different school role
                assigned_by=self.alpha_admin
            )
            assert False, "Cross-school assignment should fail"
        except ValidationError:
            pass  # Expected
        
        # System role assignment should work
        system_assignment = UserRole.mt_objects.assign_role(
            user=self.alpha_teacher,
            role=self.system_admin_role,
            assigned_by=self.alpha_admin
        )
        assert system_assignment is not None, "System role assignment should succeed"
        
        self.stdout.write(self.style.SUCCESS('Role assignment validation tests passed'))

    def test_cross_school_comparison(self):
        """Test cross-school comparison functionality for super admins."""
        self.stdout.write('Testing cross-school comparison...')
        
        # Test role distribution
        distribution = CrossSchoolRoleComparator.get_role_distribution_by_school(
            self.super_admin
        )
        assert 'schools' in distribution, "Should return schools data"
        assert len(distribution['schools']) >= 2, "Should include both test schools"
        
        # Test role comparison
        comparison = CrossSchoolRoleComparator.compare_roles_across_schools(
            user=self.super_admin,
            role_names=['Teacher']
        )
        assert 'comparison' in comparison, "Should return comparison data"
        
        # Find Teacher role comparison
        teacher_comparison = next(
            (item for item in comparison['comparison'] if item['role_name'] == 'Teacher'),
            None
        )
        assert teacher_comparison is not None, "Should find Teacher role comparison"
        assert teacher_comparison['instance_count'] == 2, "Should find 2 Teacher role instances"
        
        # Test permission matrix
        matrix = CrossSchoolRoleComparator.get_role_permission_matrix(
            user=self.super_admin,
            role_name='Teacher'
        )
        assert 'permission_matrix' in matrix, "Should return permission matrix"
        assert matrix['role_name'] == 'Teacher', "Should be for Teacher role"
        
        # Test standardization suggestions
        suggestions = CrossSchoolRoleComparator.suggest_role_standardization(
            user=self.super_admin,
            role_name='Teacher'
        )
        assert 'suggestions' in suggestions, "Should return suggestions"
        
        # Regular admin should not have access
        try:
            CrossSchoolRoleComparator.get_role_distribution_by_school(self.alpha_admin)
            assert False, "Regular admin should not have cross-school access"
        except PermissionDenied:
            pass  # Expected
        
        self.stdout.write(self.style.SUCCESS('Cross-school comparison tests passed'))
