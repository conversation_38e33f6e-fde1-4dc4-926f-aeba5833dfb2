# Generated by Django 5.2.4 on 2025-07-25 02:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("auth", "0012_alter_user_first_name_max_length"),
        ("schools", "0001_initial"),
        ("users", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="user",
            name="gender",
            field=models.CharField(
                blank=True,
                choices=[
                    ("male", "Male"),
                    ("female", "Female"),
                    ("other", "Other"),
                    ("prefer_not_to_say", "Prefer not to say"),
                ],
                help_text="Gender identity",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="user",
            name="nid",
            field=models.CharField(
                blank=True,
                help_text="National Identification Number (NID) - must be unique across all users",
                max_length=50,
                null=True,
            ),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["nid"], name="users_nid_21ddb9_idx"),
        ),
        migrations.AddIndex(
            model_name="user",
            index=models.Index(fields=["gender"], name="users_gender_c12881_idx"),
        ),
        migrations.AddConstraint(
            model_name="user",
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ("nid__isnull", False), models.Q(("nid", ""), _negated=True)
                ),
                fields=("nid",),
                name="unique_nid_global",
            ),
        ),
    ]
