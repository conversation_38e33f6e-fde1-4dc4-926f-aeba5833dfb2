<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-1606" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 1997.8125 1830.260498046875" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-1606{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-1606 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-1606 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-1606 .error-icon{fill:#a44141;}#graph-1606 .error-text{fill:#ddd;stroke:#ddd;}#graph-1606 .edge-thickness-normal{stroke-width:1px;}#graph-1606 .edge-thickness-thick{stroke-width:3.5px;}#graph-1606 .edge-pattern-solid{stroke-dasharray:0;}#graph-1606 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-1606 .edge-pattern-dashed{stroke-dasharray:3;}#graph-1606 .edge-pattern-dotted{stroke-dasharray:2;}#graph-1606 .marker{fill:lightgrey;stroke:lightgrey;}#graph-1606 .marker.cross{stroke:lightgrey;}#graph-1606 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-1606 p{margin:0;}#graph-1606 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-1606 .cluster-label text{fill:#F9FFFE;}#graph-1606 .cluster-label span{color:#F9FFFE;}#graph-1606 .cluster-label span p{background-color:transparent;}#graph-1606 .label text,#graph-1606 span{fill:#ccc;color:#ccc;}#graph-1606 .node rect,#graph-1606 .node circle,#graph-1606 .node ellipse,#graph-1606 .node polygon,#graph-1606 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-1606 .rough-node .label text,#graph-1606 .node .label text,#graph-1606 .image-shape .label,#graph-1606 .icon-shape .label{text-anchor:middle;}#graph-1606 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-1606 .rough-node .label,#graph-1606 .node .label,#graph-1606 .image-shape .label,#graph-1606 .icon-shape .label{text-align:center;}#graph-1606 .node.clickable{cursor:pointer;}#graph-1606 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-1606 .arrowheadPath{fill:lightgrey;}#graph-1606 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-1606 .flowchart-link{stroke:lightgrey;fill:none;}#graph-1606 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1606 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-1606 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1606 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-1606 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-1606 .cluster text{fill:#F9FFFE;}#graph-1606 .cluster span{color:#F9FFFE;}#graph-1606 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-1606 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-1606 rect.text{fill:none;stroke-width:0;}#graph-1606 .icon-shape,#graph-1606 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1606 .icon-shape p,#graph-1606 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-1606 .icon-shape rect,#graph-1606 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1606 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-1606 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-1606 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-1606 .event&gt;*{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1606 .event span{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1606 .event tspan{fill:#000!important;}#graph-1606 .process&gt;*{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1606 .process span{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1606 .process tspan{fill:#000!important;}#graph-1606 .decision&gt;*{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1606 .decision span{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1606 .decision tspan{fill:#000!important;}#graph-1606 .delivery&gt;*{fill:#F3E5F5!important;stroke:#9C27B0!important;color:#000!important;}#graph-1606 .delivery span{fill:#F3E5F5!important;stroke:#9C27B0!important;color:#000!important;}#graph-1606 .delivery tspan{fill:#000!important;}#graph-1606 .retry&gt;*{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1606 .retry span{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1606 .retry tspan{fill:#000!important;}</style><g><marker id="graph-1606_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1606_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1606_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1606_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1606_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1606_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M893.732,47L893.732,51.167C893.732,55.333,893.732,63.667,924.358,72.949C954.983,82.232,1016.235,92.464,1046.861,97.579L1077.487,102.695" id="L_GRADE_EVENT_PROCESS_EVENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1099.435,47L1099.435,51.167C1099.435,55.333,1099.435,63.667,1107.298,71.705C1115.16,79.744,1130.886,87.489,1138.749,91.361L1146.611,95.233" id="L_MATERIAL_EVENT_PROCESS_EVENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1318.206,47L1318.206,51.167C1318.206,55.333,1318.206,63.667,1309.743,71.722C1301.28,79.777,1284.353,87.553,1275.89,91.442L1267.427,95.33" id="L_ANNOUNCE_EVENT_PROCESS_EVENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1516.32,47L1516.32,51.167C1516.32,55.333,1516.32,63.667,1485.694,72.949C1455.069,82.232,1393.817,92.464,1363.191,97.579L1332.565,102.695" id="L_SYSTEM_EVENT_PROCESS_EVENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1205.026,151L1205.026,155.167C1205.026,159.333,1205.026,167.667,1205.026,175.333C1205.026,183,1205.026,190,1205.026,193.5L1205.026,197" id="L_PROCESS_EVENT_GET_RECIPIENTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1205.026,255L1205.026,259.167C1205.026,263.333,1205.026,271.667,1205.026,279.333C1205.026,287,1205.026,294,1205.026,297.5L1205.026,301" id="L_GET_RECIPIENTS_FILTER_PREFS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1205.026,359L1205.026,363.167C1205.026,367.333,1205.026,375.667,1205.026,383.333C1205.026,391,1205.026,398,1205.026,401.5L1205.026,405" id="L_FILTER_PREFS_CREATE_CONTENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1205.026,463L1205.026,467.167C1205.026,471.333,1205.026,479.667,1205.096,487.417C1205.167,495.167,1205.307,502.334,1205.377,505.917L1205.448,509.501" id="L_CREATE_CONTENT_MULTI_CHANNEL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1113.531,636.411L997.398,655.827C881.265,675.243,649,714.074,532.941,738.612C416.882,763.149,417.029,773.393,417.103,778.514L417.177,783.636" id="L_MULTI_CHANNEL_CHECK_ONLINE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1160.313,683.194L1151.73,694.812C1143.146,706.431,1125.978,729.669,1117.466,745.426C1108.953,761.182,1109.097,769.459,1109.169,773.597L1109.241,777.735" id="L_MULTI_CHANNEL_CHECK_PUSH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1291.33,642.602L1364.442,660.986C1437.554,679.37,1583.777,716.138,1656.959,738.106C1730.141,760.073,1730.281,767.24,1730.351,770.824L1730.422,774.407" id="L_MULTI_CHANNEL_CHECK_EMAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M376.945,890.117L358.178,904.453C339.412,918.79,301.879,947.463,283.113,967.299C264.346,987.135,264.346,998.135,264.346,1003.635L264.346,1009.135" id="L_CHECK_ONLINE_WEBSOCKET_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M463.996,883.645L493.345,899.06C522.693,914.475,581.391,945.305,610.74,971.387C640.089,997.469,640.089,1018.802,640.089,1040.135C640.089,1061.469,640.089,1082.802,640.089,1110.313C640.089,1137.823,640.089,1171.51,640.089,1205.198C640.089,1238.885,640.089,1272.573,640.089,1294.917C640.089,1317.26,640.089,1328.26,640.089,1333.76L640.089,1339.26" id="L_CHECK_ONLINE_QUEUE_WS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M264.346,1067.135L264.346,1073.302C264.346,1079.469,264.346,1091.802,264.421,1103.552C264.495,1115.302,264.644,1126.469,264.719,1132.052L264.793,1137.636" id="L_WEBSOCKET_WS_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M228.241,1233.155L211.802,1245.339C195.364,1257.523,162.486,1281.892,146.048,1299.576C129.609,1317.26,129.609,1328.26,129.609,1333.76L129.609,1339.26" id="L_WS_SUCCESS_UPDATE_WS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M273.871,1260.736L275.045,1268.324C276.219,1275.911,278.568,1291.086,289.82,1304.506C301.072,1317.926,321.227,1329.591,331.305,1335.424L341.383,1341.257" id="L_WS_SUCCESS_RETRY_WS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M439.714,1343.26L450.727,1337.094C461.74,1330.927,483.766,1318.594,462.763,1299.074C441.76,1279.554,377.729,1252.847,345.714,1239.494L313.698,1226.14" id="L_RETRY_WS_WS_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1076.216,903.213L1066.968,915.367C1057.721,927.521,1039.226,951.828,1029.979,969.482C1020.732,987.135,1020.732,998.135,1020.732,1003.635L1020.732,1009.135" id="L_CHECK_PUSH_PUSH_NOTIF_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1156.74,888.877L1179.892,903.42C1203.044,917.963,1249.347,947.049,1272.499,972.259C1295.651,997.469,1295.651,1018.802,1295.651,1040.135C1295.651,1061.469,1295.651,1082.802,1295.651,1110.313C1295.651,1137.823,1295.651,1171.51,1295.651,1205.198C1295.651,1238.885,1295.651,1272.573,1295.651,1294.917C1295.651,1317.26,1295.651,1328.26,1295.651,1333.76L1295.651,1339.26" id="L_CHECK_PUSH_SKIP_PUSH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1020.732,1067.135L1020.732,1073.302C1020.732,1079.469,1020.732,1091.802,1020.806,1103.552C1020.881,1115.302,1021.03,1126.469,1021.104,1132.052L1021.178,1137.636" id="L_PUSH_NOTIF_PUSH_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M986.037,1234.565L971.283,1246.514C956.53,1258.464,927.023,1282.362,912.269,1299.811C897.516,1317.26,897.516,1328.26,897.516,1333.76L897.516,1339.26" id="L_PUSH_SUCCESS_UPDATE_PUSH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1024.59,1266.402L1024.879,1273.045C1025.168,1279.688,1025.745,1292.974,1033.981,1305.392C1042.218,1317.81,1058.112,1329.36,1066.06,1335.134L1074.007,1340.909" id="L_PUSH_SUCCESS_RETRY_PUSH_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1148.415,1343.26L1156.183,1337.094C1163.952,1330.927,1179.489,1318.594,1165.627,1299.919C1151.766,1281.245,1108.505,1256.229,1086.875,1243.721L1065.245,1231.213" id="L_RETRY_PUSH_PUSH_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1694.434,903.569L1684.491,915.663C1674.549,927.758,1654.664,951.947,1644.721,969.541C1634.779,987.135,1634.779,998.135,1634.779,1003.635L1634.779,1009.135" id="L_CHECK_EMAIL_EMAIL_NOTIF_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1780.591,889.545L1804.327,903.976C1828.064,918.408,1875.537,947.272,1899.274,972.37C1923.01,997.469,1923.01,1018.802,1923.01,1040.135C1923.01,1061.469,1923.01,1082.802,1923.01,1110.313C1923.01,1137.823,1923.01,1171.51,1923.01,1205.198C1923.01,1238.885,1923.01,1272.573,1923.01,1294.917C1923.01,1317.26,1923.01,1328.26,1923.01,1333.76L1923.01,1339.26" id="L_CHECK_EMAIL_SKIP_EMAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1634.779,1067.135L1634.779,1073.302C1634.779,1079.469,1634.779,1091.802,1634.853,1103.552C1634.928,1115.302,1635.076,1126.469,1635.151,1132.052L1635.225,1137.636" id="L_EMAIL_NOTIF_EMAIL_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1600.083,1234.565L1585.33,1246.514C1570.576,1258.464,1541.069,1282.362,1526.316,1299.811C1511.562,1317.26,1511.562,1328.26,1511.562,1333.76L1511.562,1339.26" id="L_EMAIL_SUCCESS_UPDATE_EMAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1640.474,1264.565L1641.011,1271.514C1641.549,1278.464,1642.623,1292.362,1651.422,1305.095C1660.22,1317.829,1676.743,1329.398,1685.004,1335.182L1693.265,1340.966" id="L_EMAIL_SUCCESS_RETRY_EMAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1770.522,1343.26L1778.611,1337.094C1786.7,1330.927,1802.879,1318.594,1787.819,1299.768C1772.759,1280.943,1726.46,1255.625,1703.31,1242.966L1680.161,1230.307" id="L_RETRY_EMAIL_EMAIL_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M129.609,1397.26L129.609,1401.427C129.609,1405.594,129.609,1413.927,273.835,1425.819C418.06,1437.712,706.511,1453.163,850.736,1460.888L994.961,1468.614" id="L_UPDATE_WS_TRACK_READ_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M897.516,1397.26L897.516,1401.427C897.516,1405.594,897.516,1413.927,913.777,1422.262C930.037,1430.597,962.559,1438.933,978.82,1443.101L995.081,1447.27" id="L_UPDATE_PUSH_TRACK_READ_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1511.562,1397.26L1511.562,1401.427C1511.562,1405.594,1511.562,1413.927,1460.597,1424.539C1409.631,1435.151,1307.699,1448.042,1256.734,1454.487L1205.768,1460.932" id="L_UPDATE_EMAIL_TRACK_READ_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1295.651,1397.26L1295.651,1401.427C1295.651,1405.594,1295.651,1413.927,1280.648,1422.089C1265.646,1430.251,1235.64,1438.241,1220.638,1442.236L1205.635,1446.231" id="L_SKIP_PUSH_TRACK_READ_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1923.01,1397.26L1923.01,1401.427C1923.01,1405.594,1923.01,1413.927,1803.474,1425.65C1683.937,1437.373,1444.864,1452.485,1325.328,1460.041L1205.792,1467.597" id="L_SKIP_EMAIL_TRACK_READ_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M640.089,1397.26L640.089,1401.427C640.089,1405.594,640.089,1413.927,699.237,1424.776C758.386,1435.625,876.684,1448.989,935.832,1455.671L994.981,1462.354" id="L_QUEUE_WS_TRACK_READ_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1100.378,1501.26L1100.378,1505.427C1100.378,1509.594,1100.378,1517.927,1100.378,1525.594C1100.378,1533.26,1100.378,1540.26,1100.378,1543.76L1100.378,1547.26" id="L_TRACK_READ_ANALYTICS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1100.378,1605.26L1100.378,1609.427C1100.378,1613.594,1100.378,1621.927,1100.378,1629.594C1100.378,1637.26,1100.378,1644.26,1100.378,1647.76L1100.378,1651.26" id="L_ANALYTICS_CLEANUP_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path><path d="M1100.378,1709.26L1100.378,1713.427C1100.378,1717.594,1100.378,1725.927,1100.378,1733.594C1100.378,1741.26,1100.378,1748.26,1100.378,1751.76L1100.378,1755.26" id="L_CLEANUP_END_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1606_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(264.34635734558105, 976.1354217529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(640.0885391235352, 1104.1354217529297)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(129.609375, 1306.2604217529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(280.91666555404663, 1306.2604217529297)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1020.7317748069763, 976.1354217529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1295.651035308838, 1104.1354217529297)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(897.515625, 1306.2604217529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1026.3229155540466, 1306.2604217529297)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1634.7786421775818, 976.1354217529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1923.0104064941406, 1104.1354217529297)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1511.5624923706055, 1306.2604217529297)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1643.697907924652, 1306.2604217529297)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default event" id="flowchart-GRADE_EVENT-0" transform="translate(893.731761932373, 27.5)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-77.77083587646484" y="-19.5" width="155.5416717529297" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-65.39583587646484, -12)"><rect></rect><foreignObject width="130.7916717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>New Grade Posted</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-PROCESS_EVENT-1" transform="translate(1205.0260372161865, 124)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-123.59375" y="-27" width="247.1875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-93.59375, -12)"><rect></rect><foreignObject width="187.1875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Process Notification Event</p></span></div></foreignObject></g></g><g class="node default event" id="flowchart-MATERIAL_EVENT-2" transform="translate(1099.4348945617676, 27.5)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-77.93229675292969" y="-19.5" width="155.86459350585938" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-65.55729675292969, -12)"><rect></rect><foreignObject width="131.11459350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Material Uploaded</p></span></div></foreignObject></g></g><g class="node default event" id="flowchart-ANNOUNCE_EVENT-4" transform="translate(1318.2057304382324, 27.5)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-90.83854675292969" y="-19.5" width="181.67709350585938" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-78.46354675292969, -12)"><rect></rect><foreignObject width="156.92709350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>School Announcement</p></span></div></foreignObject></g></g><g class="node default event" id="flowchart-SYSTEM_EVENT-6" transform="translate(1516.3203125, 27.5)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-57.27604293823242" y="-19.5" width="114.55208587646484" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-44.90104293823242, -12)"><rect></rect><foreignObject width="89.80208587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>System Alert</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-GET_RECIPIENTS-9" transform="translate(1205.0260372161865, 228)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-94.09375" y="-27" width="188.1875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-64.09375, -12)"><rect></rect><foreignObject width="128.1875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Get Recipient List</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-FILTER_PREFS-11" transform="translate(1205.0260372161865, 332)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-123.30208587646484" y="-27" width="246.6041717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-93.30208587646484, -12)"><rect></rect><foreignObject width="186.6041717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Filter by User Preferences</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-CREATE_CONTENT-13" transform="translate(1205.0260372161865, 436)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-129.71875" y="-27" width="259.4375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-99.71875, -12)"><rect></rect><foreignObject width="199.4375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Create Notification Content</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-MULTI_CHANNEL-15" transform="translate(1205.0260372161865, 620.453125)"><polygon points="107.453125,0 214.90625,-107.453125 107.453125,-214.90625 0,-107.453125" class="label-container" transform="translate(-107.453125,107.453125)"></polygon><g class="label" style="" transform="translate(-80.453125, -12)"><rect></rect><foreignObject width="160.90625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Multi-channel Delivery</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-CHECK_ONLINE-17" transform="translate(416.73437309265137, 858.5208358764648)"><polygon points="71.38541793823242,0 142.77083587646484,-71.38541793823242 71.38541793823242,-142.77083587646484 0,-71.38541793823242" class="label-container" transform="translate(-71.38541793823242,71.38541793823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-44.38541793823242, -12)"><rect></rect><foreignObject width="88.77083587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>User Online?</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-CHECK_PUSH-19" transform="translate(1108.8098983764648, 858.5208358764648)"><polygon points="77.28646087646484,0 154.5729217529297,-77.28646087646484 77.28646087646484,-154.5729217529297 0,-77.28646087646484" class="label-container" transform="translate(-77.28646087646484,77.28646087646484)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-50.286460876464844, -12)"><rect></rect><foreignObject width="100.57292175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Push Enabled?</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-CHECK_EMAIL-21" transform="translate(1729.9999904632568, 858.5208358764648)"><polygon points="80.61458587646484,0 161.2291717529297,-80.61458587646484 80.61458587646484,-161.2291717529297 0,-80.61458587646484" class="label-container" transform="translate(-80.61458587646484,80.61458587646484)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-53.614585876464844, -12)"><rect></rect><foreignObject width="107.22917175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Email Enabled?</p></span></div></foreignObject></g></g><g class="node default delivery" id="flowchart-WEBSOCKET-23" transform="translate(264.34635734558105, 1040.1354217529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-88.390625" y="-27" width="176.78125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-58.390625, -12)"><rect></rect><foreignObject width="116.78125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Send WebSocket</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-QUEUE_WS-25" transform="translate(640.0885391235352, 1370.2604217529297)"><rect class="basic label-container" style="" x="-108.31771087646484" y="-27" width="216.6354217529297" height="54"></rect><g class="label" style="" transform="translate(-78.31771087646484, -12)"><rect></rect><foreignObject width="156.6354217529297" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Queue for Connection</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-WS_SUCCESS-27" transform="translate(264.34635734558105, 1205.1979217529297)"><polygon points="64.0625,0 128.125,-64.0625 64.0625,-128.125 0,-64.0625" class="label-container" transform="translate(-64.0625,64.0625)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-37.0625, -12)"><rect></rect><foreignObject width="74.125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Delivered?</p></span></div></foreignObject></g></g><g class="node default delivery" id="flowchart-UPDATE_WS-29" transform="translate(129.609375, 1370.2604217529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-121.609375" y="-27" width="243.21875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-91.609375, -12)"><rect></rect><foreignObject width="183.21875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update WebSocket Status</p></span></div></foreignObject></g></g><g class="node default retry" id="flowchart-RETRY_WS-31" transform="translate(391.49478912353516, 1370.2604217529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-90.27604293823242" y="-27" width="180.55208587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-60.27604293823242, -12)"><rect></rect><foreignObject width="120.55208587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Retry WebSocket</p></span></div></foreignObject></g></g><g class="node default delivery" id="flowchart-PUSH_NOTIF-35" transform="translate(1020.7317748069763, 1040.1354217529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-110.671875" y="-27" width="221.34375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-80.671875, -12)"><rect></rect><foreignObject width="161.34375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Send Push Notification</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-SKIP_PUSH-37" transform="translate(1295.651035308838, 1370.2604217529297)"><rect class="basic label-container" style="" x="-63.473960876464844" y="-27" width="126.94792175292969" height="54"></rect><g class="label" style="" transform="translate(-33.473960876464844, -12)"><rect></rect><foreignObject width="66.94792175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Skip Push</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-PUSH_SUCCESS-39" transform="translate(1020.7317748069763, 1205.1979217529297)"><polygon points="64.0625,0 128.125,-64.0625 64.0625,-128.125 0,-64.0625" class="label-container" transform="translate(-64.0625,64.0625)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-37.0625, -12)"><rect></rect><foreignObject width="74.125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Delivered?</p></span></div></foreignObject></g></g><g class="node default delivery" id="flowchart-UPDATE_PUSH-41" transform="translate(897.515625, 1370.2604217529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-99.109375" y="-27" width="198.21875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-69.109375, -12)"><rect></rect><foreignObject width="138.21875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update Push Status</p></span></div></foreignObject></g></g><g class="node default retry" id="flowchart-RETRY_PUSH-43" transform="translate(1114.4010391235352, 1370.2604217529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-67.77604293823242" y="-27" width="135.55208587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-37.77604293823242, -12)"><rect></rect><foreignObject width="75.55208587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Retry Push</p></span></div></foreignObject></g></g><g class="node default delivery" id="flowchart-EMAIL_NOTIF-47" transform="translate(1634.7786421775818, 1040.1354217529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-69.21875" y="-27" width="138.4375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-39.21875, -12)"><rect></rect><foreignObject width="78.4375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Send Email</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-SKIP_EMAIL-49" transform="translate(1923.0104064941406, 1370.2604217529297)"><rect class="basic label-container" style="" x="-66.80208587646484" y="-27" width="133.6041717529297" height="54"></rect><g class="label" style="" transform="translate(-36.802085876464844, -12)"><rect></rect><foreignObject width="73.60417175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Skip Email</p></span></div></foreignObject></g></g><g class="node default decision" id="flowchart-EMAIL_SUCCESS-51" transform="translate(1634.7786421775818, 1205.1979217529297)"><polygon points="64.0625,0 128.125,-64.0625 64.0625,-128.125 0,-64.0625" class="label-container" transform="translate(-64.0625,64.0625)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-37.0625, -12)"><rect></rect><foreignObject width="74.125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Delivered?</p></span></div></foreignObject></g></g><g class="node default delivery" id="flowchart-UPDATE_EMAIL-53" transform="translate(1511.5624923706055, 1370.2604217529297)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-102.4375" y="-27" width="204.875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-72.4375, -12)"><rect></rect><foreignObject width="144.875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update Email Status</p></span></div></foreignObject></g></g><g class="node default retry" id="flowchart-RETRY_EMAIL-55" transform="translate(1735.1041564941406, 1370.2604217529297)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-71.10416793823242" y="-27" width="142.20833587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-41.10416793823242, -12)"><rect></rect><foreignObject width="82.20833587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Retry Email</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-TRACK_READ-59" transform="translate(1100.3776054382324, 1474.2604217529297)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-101.421875" y="-27" width="202.84375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-71.421875, -12)"><rect></rect><foreignObject width="142.84375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Track Read Receipts</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-ANALYTICS-71" transform="translate(1100.3776054382324, 1578.2604217529297)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-90.0625" y="-27" width="180.125" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-60.0625, -12)"><rect></rect><foreignObject width="120.125" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update Analytics</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-CLEANUP-73" transform="translate(1100.3776054382324, 1682.2604217529297)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-121.546875" y="-27" width="243.09375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-91.546875, -12)"><rect></rect><foreignObject width="183.09375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Cleanup Old Notifications</p></span></div></foreignObject></g></g><g class="node default" id="flowchart-END-75" transform="translate(1100.3776054382324, 1790.7604217529297)"><rect class="basic label-container" style="" rx="31.5" ry="31.5" x="-115.375" y="-31.5" width="230.75" height="63"></rect><g class="label" style="" transform="translate(-100, -24)"><rect></rect><foreignObject width="200" height="48"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;"><span class="nodeLabel"><p>Notification Process Complete</p></span></div></foreignObject></g></g></g></g></g></svg>