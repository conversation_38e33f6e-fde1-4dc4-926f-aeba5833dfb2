"""
Secure authentication views with enhanced RBAC features.
"""

import logging
from django.contrib.auth import authenticate
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from .api_security import J<PERSON><PERSON><PERSON><PERSON>okenGenerator, APISecurityManager, TokenBlacklist
from .models import RoleAuditLog
from .serializers import UserSerializer

User = get_user_model()
logger = logging.getLogger(__name__)


class SecureTokenObtainPairView(TokenObtainPairView):
    """
    Enhanced token obtain view with RBAC claims and security features.
    """
    
    def post(self, request, *args, **kwargs):
        """
        Authenticate user and return JWT token with RBAC claims.
        """
        try:
            # Get credentials
            email = request.data.get('email')
            password = request.data.get('password')
            
            if not email or not password:
                return Response({
                    'error': 'Email and password required',
                    'detail': 'Both email and password must be provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check rate limiting
            rate_limit_key = f"login_attempts:{email}"
            attempts = cache.get(rate_limit_key, 0)
            
            if attempts >= 5:  # Max 5 attempts
                APISecurityManager.log_security_event(
                    'rate_limit_exceeded', 
                    request, 
                    {'email': email, 'attempts': attempts}
                )
                return Response({
                    'error': 'Too many login attempts',
                    'detail': 'Account temporarily locked. Try again later.'
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)
            
            # Authenticate user
            user = authenticate(request, username=email, password=password)
            
            if not user:
                # Increment failed attempts
                cache.set(rate_limit_key, attempts + 1, 300)  # 5 minute timeout
                
                APISecurityManager.log_security_event(
                    'login_failed', 
                    request, 
                    {'email': email, 'attempts': attempts + 1}
                )
                
                return Response({
                    'error': 'Invalid credentials',
                    'detail': 'Email or password is incorrect'
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # Check if user is active
            if not user.is_active:
                APISecurityManager.log_security_event(
                    'inactive_user_login', 
                    request, 
                    {'user_id': str(user.id), 'email': email}
                )
                return Response({
                    'error': 'Account disabled',
                    'detail': 'Your account has been disabled'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Generate enhanced token with RBAC claims
            token_data = JWTRBACTokenGenerator.generate_token_for_user(user)
            
            # Clear failed attempts on successful login
            cache.delete(rate_limit_key)
            
            # Update last login
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])
            
            # Log successful login
            APISecurityManager.log_security_event(
                'login_success', 
                request, 
                {
                    'user_id': str(user.id), 
                    'email': email,
                    'user_type': user.user_type,
                    'school_id': str(user.school.id) if user.school else None
                }
            )
            
            # Return token with user info
            return Response({
                'access': token_data['access'],
                'refresh': token_data['refresh'],
                'expires_in': token_data['expires_in'],
                'token_type': token_data['token_type'],
                'user': {
                    'id': str(user.id),
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'user_type': user.user_type,
                    'school': {
                        'id': str(user.school.id),
                        'name': user.school.name
                    } if user.school else None,
                    'permissions': token_data['rbac_claims']['permissions'][:10],  # First 10 permissions
                    'roles': [role['role__name'] for role in token_data['rbac_claims']['roles']],
                    'last_login': user.last_login.isoformat() if user.last_login else None
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Login error: {e}")
            APISecurityManager.log_security_event(
                'login_error', 
                request, 
                {'error': str(e), 'email': request.data.get('email')}
            )
            return Response({
                'error': 'Authentication failed',
                'detail': 'An error occurred during authentication'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class SecureTokenRefreshView(TokenRefreshView):
    """
    Enhanced token refresh view with RBAC claims update.
    """
    
    def post(self, request, *args, **kwargs):
        """
        Refresh token and update RBAC claims.
        """
        try:
            # Get the refresh token
            refresh_token = request.data.get('refresh')
            
            if not refresh_token:
                return Response({
                    'error': 'Refresh token required',
                    'detail': 'Refresh token must be provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if token is blacklisted
            try:
                from rest_framework_simplejwt.tokens import RefreshToken
                token = RefreshToken(refresh_token)
                
                if TokenBlacklist.is_token_blacklisted(str(token['jti'])):
                    return Response({
                        'error': 'Token blacklisted',
                        'detail': 'This token has been revoked'
                    }, status=status.HTTP_401_UNAUTHORIZED)
                
                # Get user from token
                user_id = token['user_id']
                user = User.objects.get(id=user_id)
                
                # Check if user is still active
                if not user.is_active:
                    return Response({
                        'error': 'Account disabled',
                        'detail': 'Your account has been disabled'
                    }, status=status.HTTP_403_FORBIDDEN)
                
                # Generate new token with fresh RBAC claims
                token_data = JWTRBACTokenGenerator.generate_token_for_user(user)
                
                # Log token refresh
                APISecurityManager.log_security_event(
                    'token_refresh', 
                    request, 
                    {
                        'user_id': str(user.id),
                        'email': user.email
                    }
                )
                
                return Response({
                    'access': token_data['access'],
                    'expires_in': token_data['expires_in'],
                    'token_type': token_data['token_type'],
                    'rbac_updated': True
                }, status=status.HTTP_200_OK)
                
            except (TokenError, InvalidToken, User.DoesNotExist) as e:
                APISecurityManager.log_security_event(
                    'token_refresh_failed', 
                    request, 
                    {'error': str(e)}
                )
                return Response({
                    'error': 'Invalid refresh token',
                    'detail': 'The refresh token is invalid or expired'
                }, status=status.HTTP_401_UNAUTHORIZED)
                
        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            return Response({
                'error': 'Token refresh failed',
                'detail': 'An error occurred during token refresh'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def logout_view(request):
    """
    Logout user and blacklist tokens.
    """
    try:
        refresh_token = request.data.get('refresh')
        
        if refresh_token:
            try:
                from rest_framework_simplejwt.tokens import RefreshToken
                token = RefreshToken(refresh_token)
                
                # Blacklist the refresh token
                TokenBlacklist.blacklist_token(
                    str(token['jti']), 
                    timezone.datetime.fromtimestamp(token['exp'])
                )
                
                # Log logout
                if hasattr(request, 'user') and request.user.is_authenticated:
                    APISecurityManager.log_security_event(
                        'logout', 
                        request, 
                        {'user_id': str(request.user.id)}
                    )
                
                return Response({
                    'message': 'Successfully logged out',
                    'detail': 'Token has been blacklisted'
                }, status=status.HTTP_200_OK)
                
            except (TokenError, InvalidToken):
                return Response({
                    'error': 'Invalid token',
                    'detail': 'The provided token is invalid'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        return Response({
            'message': 'Logged out',
            'detail': 'No token provided to blacklist'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        return Response({
            'error': 'Logout failed',
            'detail': 'An error occurred during logout'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def logout_all_devices_view(request):
    """
    Logout user from all devices by clearing all tokens.
    """
    try:
        # This would require authentication to identify the user
        if not request.user or not request.user.is_authenticated:
            return Response({
                'error': 'Authentication required',
                'detail': 'Must be logged in to logout from all devices'
            }, status=status.HTTP_401_UNAUTHORIZED)
        
        # Clear all tokens for the user
        TokenBlacklist.clear_user_tokens(str(request.user.id))
        
        # Log the action
        APISecurityManager.log_security_event(
            'logout_all_devices', 
            request, 
            {'user_id': str(request.user.id)}
        )
        
        return Response({
            'message': 'Successfully logged out from all devices',
            'detail': 'All tokens have been invalidated'
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Logout all devices error: {e}")
        return Response({
            'error': 'Logout failed',
            'detail': 'An error occurred during logout'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def user_permissions_view(request):
    """
    Get current user's permissions and roles.
    """
    try:
        # Validate API access
        validation = APISecurityManager.validate_api_access(
            request, 
            required_permission=None,  # No specific permission required for own data
            school_context=True
        )
        
        if not validation['success']:
            return Response({
                'error': 'Access denied',
                'details': validation['errors']
            }, status=status.HTTP_403_FORBIDDEN)
        
        user = validation['user']
        
        # Get user's current permissions and roles
        permissions = list(validation['permissions'])
        roles = validation['roles']
        
        # Get role details
        role_details = list(
            user.user_roles.filter(is_active=True)
            .select_related('role')
            .values(
                'role__id', 'role__name', 'role__role_type', 
                'role__level', 'assigned_at', 'expires_at'
            )
        )
        
        return Response({
            'user_id': str(user.id),
            'email': user.email,
            'school': {
                'id': str(user.school.id),
                'name': user.school.name
            } if user.school else None,
            'permissions': permissions,
            'roles': roles,
            'role_details': role_details,
            'permission_count': len(permissions),
            'role_count': len(roles),
            'is_superuser': user.is_superuser,
            'last_login': user.last_login.isoformat() if user.last_login else None
        }, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"User permissions view error: {e}")
        return Response({
            'error': 'Failed to retrieve permissions',
            'detail': 'An error occurred while fetching user permissions'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
