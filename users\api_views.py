"""
API views for RBAC role and permission management.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from django.utils import timezone
from .models import (
    User, Role, Permission, UserRole, RolePermission, 
    RoleTemplate, RoleAuditLog
)
from .serializers import (
    RoleSerializer, PermissionSerializer, UserRoleSerializer,
    RolePermissionSerializer, RoleTemplateSerializer, RoleAuditLogSerializer
)
from .decorators import (
    RBACPermission, MultiPermissionMixin, audit_action,
    EnhancedRBACPermission, secure_api_endpoint, api_permission_required
)
from .rbac_utils import RBACManager
from .api_security import APISecurityManager, JWTRBACTokenGenerator


class RoleViewSet(MultiPermissionMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing roles.
    """
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]
    
    permission_map = {
        'list': 'can_view_users',
        'retrieve': 'can_view_users',
        'create': 'can_assign_roles',
        'update': 'can_assign_roles',
        'partial_update': 'can_assign_roles',
        'destroy': 'can_manage_permissions',
    }
    
    def get_queryset(self):
        """Filter roles based on user's school context."""
        user = self.request.user
        
        if user.is_superuser or RBACManager.user_has_permission(user, 'can_manage_schools'):
            # Super admin can see all roles
            return Role.objects.all().select_related('school', 'created_by', 'parent_role')
        
        # Regular users see only their school's roles and system roles
        return Role.objects.filter(
            Q(school=user.school) | Q(school__isnull=True)
        ).select_related('school', 'created_by', 'parent_role')
    
    @audit_action('role_created')
    def create(self, request, *args, **kwargs):
        """Create a new role."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # Set created_by and school context
        serializer.save(
            created_by=request.user,
            school=request.user.school if serializer.validated_data.get('role_type') == 'school' else None
        )
        
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['post'])
    @audit_action('permission_granted')
    def assign_permission(self, request, pk=None):
        """Assign a permission to a role."""
        role = self.get_object()
        permission_id = request.data.get('permission_id')
        granted = request.data.get('granted', True)
        expires_at = request.data.get('expires_at')
        
        if not permission_id:
            return Response(
                {'error': 'permission_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        permission = get_object_or_404(Permission, id=permission_id)
        
        # Create or update role permission
        role_permission, created = RolePermission.objects.update_or_create(
            role=role,
            permission=permission,
            defaults={
                'granted': granted,
                'granted_by': request.user,
                'expires_at': expires_at
            }
        )
        
        serializer = RolePermissionSerializer(role_permission)
        return Response(serializer.data)
    
    @action(detail=True, methods=['delete'])
    @audit_action('permission_revoked')
    def revoke_permission(self, request, pk=None):
        """Revoke a permission from a role."""
        role = self.get_object()
        permission_id = request.data.get('permission_id')
        
        if not permission_id:
            return Response(
                {'error': 'permission_id is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        role_permission = get_object_or_404(
            RolePermission, 
            role=role, 
            permission_id=permission_id
        )
        role_permission.delete()
        
        return Response(status=status.HTTP_204_NO_CONTENT)
    
    @action(detail=True, methods=['get'])
    def permissions(self, request, pk=None):
        """Get all permissions for a role."""
        role = self.get_object()
        role_permissions = role.role_permissions.select_related('permission')
        serializer = RolePermissionSerializer(role_permissions, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def users(self, request, pk=None):
        """Get all users assigned to this role."""
        role = self.get_object()
        user_roles = role.user_roles.filter(is_active=True).select_related('user')
        serializer = UserRoleSerializer(user_roles, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['post'])
    def create_from_template(self, request):
        """Create a role from a template."""
        template_name = request.data.get('template_name')
        role_name = request.data.get('role_name')
        description = request.data.get('description', '')
        
        if not template_name or not role_name:
            return Response(
                {'error': 'template_name and role_name are required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            role = RBACManager.create_role_from_template(
                template_name=template_name,
                role_name=role_name,
                school=request.user.school,
                created_by=request.user,
                description=description
            )
            serializer = self.get_serializer(role)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except ValueError as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )


class PermissionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing permissions.
    """
    serializer_class = PermissionSerializer
    permission_classes = [IsAuthenticated, RBACPermission('can_view_users')]
    queryset = Permission.objects.all().select_related('content_type', 'created_by')
    
    def get_queryset(self):
        """Filter permissions based on category if specified."""
        queryset = super().get_queryset()
        category = self.request.query_params.get('category')
        if category:
            queryset = queryset.filter(category=category)
        return queryset
    
    @action(detail=False, methods=['get'])
    def categories(self, request):
        """Get all permission categories."""
        categories = Permission.objects.values_list('category', flat=True).distinct()
        return Response(list(categories))


class UserRoleViewSet(MultiPermissionMixin, viewsets.ModelViewSet):
    """
    ViewSet for managing user role assignments.
    """
    serializer_class = UserRoleSerializer
    permission_classes = [IsAuthenticated]
    
    permission_map = {
        'list': 'can_view_users',
        'retrieve': 'can_view_users',
        'create': 'can_assign_roles',
        'update': 'can_assign_roles',
        'partial_update': 'can_assign_roles',
        'destroy': 'can_assign_roles',
    }
    
    def get_queryset(self):
        """Filter user roles based on school context."""
        user = self.request.user
        
        if user.is_superuser or RBACManager.user_has_permission(user, 'can_manage_schools'):
            return UserRole.objects.all().select_related('user', 'role', 'assigned_by')
        
        # Filter by school context
        return UserRole.objects.filter(
            Q(role__school=user.school) | Q(role__school__isnull=True)
        ).select_related('user', 'role', 'assigned_by')
    
    @audit_action('user_role_assigned')
    def create(self, request, *args, **kwargs):
        """Assign a role to a user."""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        user_id = serializer.validated_data['user'].id
        role_id = serializer.validated_data['role'].id
        
        try:
            user_role = RBACManager.assign_role_to_user(
                user=serializer.validated_data['user'],
                role=serializer.validated_data['role'],
                assigned_by=request.user,
                valid_from=serializer.validated_data.get('valid_from'),
                valid_until=serializer.validated_data.get('valid_until'),
                requires_approval=serializer.validated_data.get('requires_approval', False)
            )
            
            response_serializer = self.get_serializer(user_role)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        except ValueError as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    @audit_action('role_approved')
    def approve(self, request, pk=None):
        """Approve a pending role assignment."""
        user_role = self.get_object()
        
        if RBACManager.approve_role_assignment(user_role, request.user):
            serializer = self.get_serializer(user_role)
            return Response(serializer.data)
        else:
            return Response(
                {'error': 'Role assignment cannot be approved'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    @audit_action('user_role_removed')
    def deactivate(self, request, pk=None):
        """Deactivate a user role assignment."""
        user_role = self.get_object()
        
        if RBACManager.remove_role_from_user(
            user_role.user, 
            user_role.role, 
            request.user
        ):
            return Response(status=status.HTTP_204_NO_CONTENT)
        else:
            return Response(
                {'error': 'Role assignment could not be removed'}, 
                status=status.HTTP_400_BAD_REQUEST
            )


class RoleTemplateViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing role templates.
    """
    serializer_class = RoleTemplateSerializer
    permission_classes = [IsAuthenticated, RBACPermission('can_view_users')]
    queryset = RoleTemplate.objects.filter(is_active=True).prefetch_related('permissions')


class RoleAuditLogViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for viewing role audit logs.
    """
    serializer_class = RoleAuditLogSerializer
    permission_classes = [IsAuthenticated, RBACPermission('can_view_audit_logs')]
    
    def get_queryset(self):
        """Filter audit logs based on school context."""
        user = self.request.user
        
        if user.is_superuser or RBACManager.user_has_permission(user, 'can_manage_schools'):
            return RoleAuditLog.objects.all().select_related(
                'performed_by', 'target_user', 'target_role', 'school'
            )
        
        # Filter by school context
        return RoleAuditLog.objects.filter(
            Q(school=user.school) | Q(school__isnull=True)
        ).select_related('performed_by', 'target_user', 'target_role', 'school')


class UserPermissionViewSet(viewsets.ViewSet):
    """
    ViewSet for checking user permissions.
    """
    permission_classes = [IsAuthenticated]
    
    @action(detail=False, methods=['get'])
    def my_permissions(self, request):
        """Get current user's permissions."""
        permissions = RBACManager.get_user_permissions(request.user, request.user.school)
        return Response({'permissions': list(permissions)})
    
    @action(detail=False, methods=['post'])
    def check_permission(self, request):
        """Check if current user has a specific permission."""
        permission_codename = request.data.get('permission')
        if not permission_codename:
            return Response(
                {'error': 'permission is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        has_permission = RBACManager.user_has_permission(
            request.user, 
            permission_codename, 
            request.user.school
        )
        
        return Response({'has_permission': has_permission})
    
    @action(detail=True, methods=['get'])
    def user_permissions(self, request, pk=None):
        """Get permissions for a specific user (admin only)."""
        if not RBACManager.user_has_permission(request.user, 'can_view_users'):
            return Response(
                {'error': 'Permission denied'}, 
                status=status.HTTP_403_FORBIDDEN
            )
        
        user = get_object_or_404(User, pk=pk)
        permissions = RBACManager.get_user_permissions(user, user.school)
        return Response({'permissions': list(permissions)})
