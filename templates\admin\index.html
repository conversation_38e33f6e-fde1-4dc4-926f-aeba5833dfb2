{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrahead %}
{{ block.super }}
<style>
.rbac-dashboard {
    margin: 20px 0;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.stat-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #2196f3;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-card.users .stat-number { color: #4caf50; }
.stat-card.roles .stat-number { color: #2196f3; }
.stat-card.permissions .stat-number { color: #ff9800; }
.stat-card.assignments .stat-number { color: #9c27b0; }
.stat-card.pending .stat-number { color: #f44336; }

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.action-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
}

.action-card h3 {
    margin: 0 0 10px 0;
    color: #495057;
    font-size: 16px;
}

.action-link {
    display: inline-block;
    padding: 8px 16px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-size: 14px;
    margin: 5px 5px 5px 0;
    transition: background 0.2s;
}

.action-link:hover {
    background: #0056b3;
    color: white;
    text-decoration: none;
}

.recent-activity {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin: 20px 0;
}

.recent-activity h3 {
    background: #f8f9fa;
    margin: 0;
    padding: 15px;
    border-bottom: 1px solid #ddd;
    border-radius: 8px 8px 0 0;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-description {
    flex: 1;
}

.activity-time {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
    margin-left: 15px;
}

.activity-user {
    font-weight: bold;
    color: #2196f3;
}

.role-distribution {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.role-type-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.role-type-card.system {
    border-left: 4px solid #ff5722;
}

.role-type-card.school {
    border-left: 4px solid #2196f3;
}

.role-type-card.custom {
    border-left: 4px solid #4caf50;
}

.section-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 30px 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #2196f3;
}
</style>
{% endblock %}

{% block content %}
<h1>{% trans 'RBAC Management Dashboard' %}</h1>

{% if rbac_stats %}
<div class="rbac-dashboard">
    <div class="section-title">📊 System Overview</div>
    
    <div class="dashboard-stats">
        <div class="stat-card users">
            <div class="stat-number">{{ rbac_stats.total_users }}</div>
            <div class="stat-label">Active Users</div>
        </div>
        <div class="stat-card roles">
            <div class="stat-number">{{ rbac_stats.total_roles }}</div>
            <div class="stat-label">Total Roles</div>
        </div>
        <div class="stat-card permissions">
            <div class="stat-number">{{ rbac_stats.total_permissions }}</div>
            <div class="stat-label">Permissions</div>
        </div>
        <div class="stat-card assignments">
            <div class="stat-number">{{ rbac_stats.active_assignments }}</div>
            <div class="stat-label">Active Assignments</div>
        </div>
        {% if rbac_stats.pending_approvals > 0 %}
        <div class="stat-card pending">
            <div class="stat-number">{{ rbac_stats.pending_approvals }}</div>
            <div class="stat-label">Pending Approvals</div>
        </div>
        {% endif %}
    </div>
    
    <div class="section-title">🎭 Role Distribution</div>
    
    <div class="role-distribution">
        <div class="role-type-card system">
            <div class="stat-number">{{ rbac_stats.system_roles }}</div>
            <div class="stat-label">System Roles</div>
        </div>
        <div class="role-type-card school">
            <div class="stat-number">{{ rbac_stats.school_roles }}</div>
            <div class="stat-label">School Roles</div>
        </div>
        <div class="role-type-card custom">
            <div class="stat-number">{{ rbac_stats.custom_roles }}</div>
            <div class="stat-label">Custom Roles</div>
        </div>
    </div>
    
    <div class="section-title">🚀 Quick Actions</div>
    
    <div class="quick-actions">
        <div class="action-card">
            <h3>👥 User Management</h3>
            <a href="{% url 'admin:users_user_changelist' %}" class="action-link">Manage Users</a>
            <a href="{% url 'users:bulk-role-assignment' %}" class="action-link">Bulk Assign Roles</a>
        </div>
        
        <div class="action-card">
            <h3>🎭 Role Management</h3>
            <a href="{% url 'admin:users_role_changelist' %}" class="action-link">Manage Roles</a>
            <a href="{% url 'users:role-hierarchy' %}" class="action-link">View Hierarchy</a>
            <a href="{% url 'users:role-comparison' %}" class="action-link">Compare Roles</a>
        </div>
        
        <div class="action-card">
            <h3>🔐 Permission Management</h3>
            <a href="{% url 'admin:users_permission_changelist' %}" class="action-link">Manage Permissions</a>
            <a href="{% url 'users:permission-matrix' %}" class="action-link">Permission Matrix</a>
        </div>
        
        <div class="action-card">
            <h3>📊 Analytics & Reports</h3>
            <a href="{% url 'users:role-analytics' %}" class="action-link">Role Analytics</a>
            <a href="{% url 'admin:users_roleauditlog_changelist' %}" class="action-link">Audit Logs</a>
        </div>
    </div>
    
    {% if recent_activity %}
    <div class="section-title">📝 Recent Activity</div>
    
    <div class="recent-activity">
        <h3>Latest RBAC Operations</h3>
        <div class="activity-list">
            {% for log in recent_activity %}
            <div class="activity-item">
                <div class="activity-description">
                    <span class="activity-user">{{ log.performed_by.get_full_name }}</span>
                    {{ log.description|default:log.action_type }}
                    {% if log.target_user %}
                        for <strong>{{ log.target_user.get_full_name }}</strong>
                    {% endif %}
                    {% if log.target_role %}
                        ({{ log.target_role.name }})
                    {% endif %}
                </div>
                <div class="activity-time">{{ log.created_at|timesince }} ago</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<!-- Original Django admin content -->
<div id="content-main">
    {% if app_list %}
        {% for app in app_list %}
            <div class="app-{{ app.app_label }} module">
                <table>
                    <caption>
                        <a href="{{ app.app_url }}" class="section" title="{% blocktrans with name=app.name %}Models in the {{ name }} application{% endblocktrans %}">{{ app.name }}</a>
                    </caption>
                    {% for model in app.models %}
                        <tr class="model-{{ model.object_name|lower }}">
                            {% if model.admin_url %}
                                <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
                            {% else %}
                                <th scope="row">{{ model.name }}</th>
                            {% endif %}

                            {% if model.add_url %}
                                <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}

                            {% if model.admin_url %}
                                {% if model.view_only %}
                                    <td><a href="{{ model.admin_url }}" class="viewlink">{% trans 'View' %}</a></td>
                                {% else %}
                                    <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                                {% endif %}
                            {% else %}
                                <td>&nbsp;</td>
                            {% endif %}
                        </tr>
                    {% endfor %}
                </table>
            </div>
        {% endfor %}
    {% else %}
        <p>{% trans "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>
{% endblock %}
