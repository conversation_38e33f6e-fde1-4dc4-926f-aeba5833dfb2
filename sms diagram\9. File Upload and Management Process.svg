<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-1868" width="100%" xmlns="http://www.w3.org/2000/svg" class="flowchart" style="max-width: 100%;" viewBox="0 0 1241.71875 2858.375" role="graphics-document document" aria-roledescription="flowchart-v2" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-1868{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-1868 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-1868 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-1868 .error-icon{fill:#a44141;}#graph-1868 .error-text{fill:#ddd;stroke:#ddd;}#graph-1868 .edge-thickness-normal{stroke-width:1px;}#graph-1868 .edge-thickness-thick{stroke-width:3.5px;}#graph-1868 .edge-pattern-solid{stroke-dasharray:0;}#graph-1868 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-1868 .edge-pattern-dashed{stroke-dasharray:3;}#graph-1868 .edge-pattern-dotted{stroke-dasharray:2;}#graph-1868 .marker{fill:lightgrey;stroke:lightgrey;}#graph-1868 .marker.cross{stroke:lightgrey;}#graph-1868 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-1868 p{margin:0;}#graph-1868 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-1868 .cluster-label text{fill:#F9FFFE;}#graph-1868 .cluster-label span{color:#F9FFFE;}#graph-1868 .cluster-label span p{background-color:transparent;}#graph-1868 .label text,#graph-1868 span{fill:#ccc;color:#ccc;}#graph-1868 .node rect,#graph-1868 .node circle,#graph-1868 .node ellipse,#graph-1868 .node polygon,#graph-1868 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-1868 .rough-node .label text,#graph-1868 .node .label text,#graph-1868 .image-shape .label,#graph-1868 .icon-shape .label{text-anchor:middle;}#graph-1868 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#graph-1868 .rough-node .label,#graph-1868 .node .label,#graph-1868 .image-shape .label,#graph-1868 .icon-shape .label{text-align:center;}#graph-1868 .node.clickable{cursor:pointer;}#graph-1868 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#graph-1868 .arrowheadPath{fill:lightgrey;}#graph-1868 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#graph-1868 .flowchart-link{stroke:lightgrey;fill:none;}#graph-1868 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1868 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#graph-1868 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1868 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#graph-1868 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#graph-1868 .cluster text{fill:#F9FFFE;}#graph-1868 .cluster span{color:#F9FFFE;}#graph-1868 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#graph-1868 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#graph-1868 rect.text{fill:none;stroke-width:0;}#graph-1868 .icon-shape,#graph-1868 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#graph-1868 .icon-shape p,#graph-1868 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#graph-1868 .icon-shape rect,#graph-1868 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#graph-1868 .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#graph-1868 .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#graph-1868 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#graph-1868 .process&gt;*{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1868 .process span{fill:#E3F2FD!important;stroke:#2196F3!important;color:#000!important;}#graph-1868 .process tspan{fill:#000!important;}#graph-1868 .validation&gt;*{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1868 .validation span{fill:#FFF3E0!important;stroke:#FF9800!important;color:#000!important;}#graph-1868 .validation tspan{fill:#000!important;}#graph-1868 .error&gt;*{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1868 .error span{fill:#FFEBEE!important;stroke:#F44336!important;color:#000!important;}#graph-1868 .error tspan{fill:#000!important;}#graph-1868 .success&gt;*{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1868 .success span{fill:#E8F5E8!important;stroke:#4CAF50!important;color:#000!important;}#graph-1868 .success tspan{fill:#000!important;}#graph-1868 .notification&gt;*{fill:#F3E5F5!important;stroke:#9C27B0!important;color:#000!important;}#graph-1868 .notification span{fill:#F3E5F5!important;stroke:#9C27B0!important;color:#000!important;}#graph-1868 .notification tspan{fill:#000!important;}</style><g><marker id="graph-1868_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1868_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1868_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1868_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1; stroke-dasharray: 1, 0;"></circle></marker><marker id="graph-1868_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><marker id="graph-1868_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2; stroke-dasharray: 1, 0;"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M170.229,47L170.229,51.167C170.229,55.333,170.229,63.667,170.229,71.333C170.229,79,170.229,86,170.229,89.5L170.229,93" id="L_START_SELECT_FILE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M127.46,151L120.86,155.167C114.26,159.333,101.06,167.667,94.53,175.417C88,183.167,88.14,190.334,88.211,193.917L88.281,197.501" id="L_SELECT_FILE_VALIDATE_SIZE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M88.359,329.521L88.276,335.604C88.193,341.688,88.026,353.854,87.943,371.972C87.859,390.09,87.859,414.16,87.859,426.194L87.859,438.229" id="L_VALIDATE_SIZE_SIZE_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M122.359,442.229L138.588,429.528C154.817,416.826,187.276,391.424,203.505,361.887C219.734,332.351,219.734,298.681,219.734,267.01C219.734,235.34,219.734,205.67,216.227,187.151C212.72,168.632,205.706,161.265,202.199,157.581L198.692,153.897" id="L_SIZE_ERROR_SELECT_FILE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M130.605,287.275L156.159,300.399C181.713,313.523,232.82,339.772,258.448,358.48C284.076,377.188,284.225,388.354,284.299,393.938L284.374,399.521" id="L_VALIDATE_SIZE_VALIDATE_TYPE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M284.427,535.938L284.344,542.021C284.26,548.104,284.094,560.271,284.01,582.888C283.927,605.505,283.927,638.573,283.927,655.107L283.927,671.641" id="L_VALIDATE_TYPE_TYPE_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M325.414,675.641L351.843,658.44C378.272,641.24,431.131,606.839,457.56,572.437C483.99,538.035,483.99,503.632,483.99,469.229C483.99,434.826,483.99,400.424,483.99,366.387C483.99,332.351,483.99,298.681,483.99,267.01C483.99,235.34,483.99,205.67,443.634,184.147C403.279,162.624,322.568,149.247,282.213,142.559L241.858,135.871" id="L_TYPE_ERROR_SELECT_FILE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M330.213,490.151L361.064,503.866C391.915,517.58,453.616,545.009,484.541,564.307C515.467,583.604,515.615,594.771,515.69,600.354L515.764,605.938" id="L_VALIDATE_TYPE_CHECK_QUOTA_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M459.509,740.035L435.691,755.503C411.872,770.971,364.236,801.907,340.417,828.042C316.599,854.177,316.599,875.51,316.599,894.844C316.599,914.177,316.599,931.51,316.599,956.879C316.599,982.248,316.599,1015.653,316.599,1051.057C316.599,1086.462,316.599,1123.866,316.599,1164.025C316.599,1204.184,316.599,1247.097,316.599,1290.01C316.599,1332.924,316.599,1375.837,316.599,1407.96C316.599,1440.083,316.599,1461.417,316.599,1480.75C316.599,1500.083,316.599,1517.417,316.599,1541.719C316.599,1566.021,316.599,1597.292,316.599,1630.563C316.599,1663.833,316.599,1699.104,316.599,1727.406C316.599,1755.708,316.599,1777.042,316.599,1796.375C316.599,1815.708,316.599,1833.042,316.599,1845.208C316.599,1857.375,316.599,1864.375,316.599,1867.875L316.599,1871.375" id="L_CHECK_QUOTA_QUOTA_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M316.599,1929.375L316.599,1933.542C316.599,1937.708,316.599,1946.042,330.069,1955.386C343.539,1964.73,370.479,1975.085,383.949,1980.262L397.419,1985.44" id="L_QUOTA_ERROR_END_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M575.828,736.334L604.975,752.419C634.123,768.504,692.418,800.674,721.566,822.259C750.714,843.844,750.714,854.844,750.714,860.344L750.714,865.844" id="L_CHECK_QUOTA_START_UPLOAD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M675.839,920.651L661.061,925.35C646.283,930.049,616.727,939.446,601.95,955.681C587.172,971.915,587.172,994.986,587.172,1006.522L587.172,1018.057" id="L_START_UPLOAD_PROGRESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M557.026,1076.057L541.17,1090.26C525.313,1104.462,493.599,1132.866,490.701,1160.382C487.802,1187.897,513.719,1214.524,526.677,1227.837L539.636,1241.151" id="L_PROGRESS_UPLOAD_COMPLETE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M625.075,1236.174L633.528,1223.69C641.981,1211.206,658.886,1186.239,656.536,1160.076C654.186,1133.913,632.58,1106.555,621.777,1092.876L610.974,1079.196" id="L_UPLOAD_COMPLETE_PROGRESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M587.672,1382.25L587.589,1388.333C587.505,1394.417,587.339,1406.583,587.255,1418.167C587.172,1429.75,587.172,1440.75,587.172,1446.25L587.172,1451.75" id="L_UPLOAD_COMPLETE_VIRUS_SCAN_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M587.172,1509.75L587.172,1513.917C587.172,1518.083,587.172,1526.417,587.242,1534.167C587.312,1541.917,587.453,1549.084,587.523,1552.667L587.593,1556.251" id="L_VIRUS_SCAN_SCAN_RESULT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M587.672,1697.875L587.589,1703.958C587.505,1710.042,587.339,1722.208,587.255,1733.792C587.172,1745.375,587.172,1756.375,587.172,1761.875L587.172,1767.375" id="L_SCAN_RESULT_DELETE_FILE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M587.172,1825.375L587.172,1829.542C587.172,1833.708,587.172,1842.042,587.172,1849.708C587.172,1857.375,587.172,1864.375,587.172,1867.875L587.172,1871.375" id="L_DELETE_FILE_VIRUS_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M587.172,1929.375L587.172,1933.542C587.172,1937.708,587.172,1946.042,573.702,1955.386C560.232,1964.73,533.292,1975.085,519.822,1980.262L506.352,1985.44" id="L_VIRUS_ERROR_END_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M642.169,1643.378L700.136,1658.544C758.104,1673.711,874.039,1704.043,932.006,1724.709C989.974,1745.375,989.974,1756.375,989.974,1761.875L989.974,1767.375" id="L_SCAN_RESULT_EXTRACT_METADATA_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M989.974,1825.375L989.974,1829.542C989.974,1833.708,989.974,1842.042,989.974,1849.708C989.974,1857.375,989.974,1864.375,989.974,1867.875L989.974,1871.375" id="L_EXTRACT_METADATA_GENERATE_URL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M989.974,1929.375L989.974,1933.542C989.974,1937.708,989.974,1946.042,989.974,1953.708C989.974,1961.375,989.974,1968.375,989.974,1971.875L989.974,1975.375" id="L_GENERATE_URL_ASSIGN_STORAGE_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M989.974,2033.375L989.974,2037.542C989.974,2041.708,989.974,2050.042,989.974,2057.708C989.974,2065.375,989.974,2072.375,989.974,2075.875L989.974,2079.375" id="L_ASSIGN_STORAGE_CREATE_DB_RECORD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M989.974,2137.375L989.974,2141.542C989.974,2145.708,989.974,2154.042,989.974,2161.708C989.974,2169.375,989.974,2176.375,989.974,2179.875L989.974,2183.375" id="L_CREATE_DB_RECORD_SET_PERMISSIONS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M989.974,2241.375L989.974,2245.542C989.974,2249.708,989.974,2258.042,989.974,2265.708C989.974,2273.375,989.974,2280.375,989.974,2283.875L989.974,2287.375" id="L_SET_PERMISSIONS_NOTIFY_USERS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M883.177,2332.726L836.483,2339.001C789.788,2345.276,696.399,2357.825,649.705,2367.6C603.01,2377.375,603.01,2384.375,603.01,2387.875L603.01,2391.375" id="L_NOTIFY_USERS_COURSE_STUDENTS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M922.606,2345.375L912.21,2349.542C901.814,2353.708,881.022,2362.042,870.625,2369.708C860.229,2377.375,860.229,2384.375,860.229,2387.875L860.229,2391.375" id="L_NOTIFY_USERS_UPDATE_DASHBOARD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M1057.341,2345.375L1067.738,2349.542C1078.134,2353.708,1098.926,2362.042,1109.323,2369.708C1119.719,2377.375,1119.719,2384.375,1119.719,2387.875L1119.719,2391.375" id="L_NOTIFY_USERS_SEND_EMAIL_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M603.01,2449.375L603.01,2453.542C603.01,2457.708,603.01,2466.042,627.812,2475.2C652.613,2484.359,702.216,2494.342,727.017,2499.334L751.818,2504.326" id="L_COURSE_STUDENTS_NOTIFICATION_SENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M860.229,2449.375L860.229,2453.542C860.229,2457.708,860.229,2466.042,860.306,2473.709C860.382,2481.375,860.535,2488.376,860.611,2491.876L860.688,2495.376" id="L_UPDATE_DASHBOARD_NOTIFICATION_SENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M1119.719,2449.375L1119.719,2453.542C1119.719,2457.708,1119.719,2466.042,1094.917,2475.2C1070.116,2484.359,1020.514,2494.342,995.712,2499.334L970.911,2504.326" id="L_SEND_EMAIL_NOTIFICATION_SENT_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M861.365,2553.375L861.365,2557.542C861.365,2561.708,861.365,2570.042,861.365,2577.708C861.365,2585.375,861.365,2592.375,861.365,2595.875L861.365,2599.375" id="L_NOTIFICATION_SENT_AUDIT_LOG_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M861.365,2657.375L861.365,2661.542C861.365,2665.708,861.365,2674.042,861.365,2681.708C861.365,2689.375,861.365,2696.375,861.365,2699.875L861.365,2703.375" id="L_AUDIT_LOG_UPDATE_STATS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M861.365,2761.375L861.365,2765.542C861.365,2769.708,861.365,2778.042,861.365,2785.708C861.365,2793.375,861.365,2800.375,861.365,2803.875L861.365,2807.375" id="L_UPDATE_STATS_END_SUCCESS_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M775.748,923.844L779.611,928.01C783.474,932.177,791.201,940.51,795.134,948.261C799.068,956.011,799.208,963.178,799.278,966.761L799.349,970.345" id="L_START_UPLOAD_UPLOAD_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M799.427,1124.771L799.344,1130.854C799.26,1136.938,799.094,1149.104,804.862,1167.495C810.63,1185.885,822.333,1210.499,828.184,1222.806L834.036,1235.113" id="L_UPLOAD_ERROR_RETRY_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M884.643,1238.726L890.614,1225.817C896.585,1212.908,908.527,1187.089,914.498,1155.478C920.469,1123.866,920.469,1086.462,920.469,1051.057C920.469,1015.653,920.469,982.248,905.293,960.897C890.117,939.546,859.765,930.249,844.589,925.6L829.413,920.951" id="L_RETRY_START_UPLOAD_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path><path d="M860.198,1366.74L860.115,1375.408C860.031,1384.076,859.865,1401.413,859.781,1420.748C859.698,1440.083,859.698,1461.417,859.698,1480.75C859.698,1500.083,859.698,1517.417,859.698,1541.719C859.698,1566.021,859.698,1597.292,859.698,1630.563C859.698,1663.833,859.698,1699.104,859.698,1727.406C859.698,1755.708,859.698,1777.042,859.698,1796.375C859.698,1815.708,859.698,1833.042,859.698,1850.375C859.698,1867.708,859.698,1885.042,859.698,1902.375C859.698,1919.708,859.698,1937.042,802.659,1952.981C745.62,1968.921,631.541,1983.467,574.502,1990.74L517.463,1998.013" id="L_RETRY_END_ERROR_0" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="" marker-end="url(#graph-1868_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(87.859375, 366.0208435058594)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(283.92708587646484, 366.0208435058594)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(283.92708587646484, 572.4375152587891)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(515.3177108764648, 572.4375152587891)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(316.59896087646484, 1418.7500305175781)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(750.7135467529297, 832.8437652587891)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(675.7916703224182, 1161.2708587646484)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(587.171875, 1418.7500305175781)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(587.171875, 1734.3750305175781)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(989.9739646911621, 1734.3750305175781)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g class="label" transform="translate(0, 0)"><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(798.9270896911621, 1161.2708587646484)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(920.4687614440918, 1049.0573120117188)"><g class="label" transform="translate(-11.328125, -12)"><foreignObject width="22.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(859.697925567627, 1734.3750305175781)"><g class="label" transform="translate(-9.401041984558105, -12)"><foreignObject width="18.80208396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default" id="flowchart-START-0" transform="translate(170.22916793823242, 27.5)"><rect class="basic label-container" style="" rx="19.5" ry="19.5" x="-83.65625" y="-19.5" width="167.3125" height="39"></rect><g class="label" style="" transform="translate(-71.28125, -12)"><rect></rect><foreignObject width="142.5625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>File Upload Request</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-SELECT_FILE-1" transform="translate(170.22916793823242, 124)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-67.68229293823242" y="-27" width="135.36458587646484" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-37.68229293823242, -12)"><rect></rect><foreignObject width="75.36458587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Select File</p></span></div></foreignObject></g></g><g class="node default validation" id="flowchart-VALIDATE_SIZE-3" transform="translate(87.859375, 265.0104217529297)"><polygon points="64.01041793823242,0 128.02083587646484,-64.01041793823242 64.01041793823242,-128.02083587646484 0,-64.01041793823242" class="label-container" transform="translate(-64.01041793823242,64.01041793823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-37.01041793823242, -12)"><rect></rect><foreignObject width="74.02083587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Size Valid?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-SIZE_ERROR-5" transform="translate(87.859375, 469.2291793823242)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-79.859375" y="-27" width="159.71875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-49.859375, -12)"><rect></rect><foreignObject width="99.71875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>File Too Large</p></span></div></foreignObject></g></g><g class="node default validation" id="flowchart-VALIDATE_TYPE-9" transform="translate(283.92708587646484, 469.2291793823242)"><polygon points="66.20833587646484,0 132.4166717529297,-66.20833587646484 66.20833587646484,-132.4166717529297 0,-66.20833587646484" class="label-container" transform="translate(-66.20833587646484,66.20833587646484)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-39.208335876464844, -12)"><rect></rect><foreignObject width="78.41667175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Type Valid?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-TYPE_ERROR-11" transform="translate(283.92708587646484, 702.6406402587891)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-88.1875" y="-27" width="176.375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-58.1875, -12)"><rect></rect><foreignObject width="116.375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Invalid File Type</p></span></div></foreignObject></g></g><g class="node default validation" id="flowchart-CHECK_QUOTA-15" transform="translate(515.3177108764648, 702.6406402587891)"><polygon points="93.203125,0 186.40625,-93.203125 93.203125,-186.40625 0,-93.203125" class="label-container" transform="translate(-93.203125,93.203125)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-66.203125, -12)"><rect></rect><foreignObject width="132.40625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Storage Quota OK?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-QUOTA_ERROR-17" transform="translate(316.59896087646484, 1902.3750305175781)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-117.53125" y="-27" width="235.0625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-87.53125, -12)"><rect></rect><foreignObject width="175.0625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Storage Quota Exceeded</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-END_ERROR-19" transform="translate(451.8854179382324, 2006.3750305175781)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" rx="19.5" ry="19.5" x="-61.609375" y="-19.5" width="123.21875" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-49.234375, -12)"><rect></rect><foreignObject width="98.46875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Upload Failed</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-START_UPLOAD-21" transform="translate(750.7135467529297, 896.8437652587891)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-74.875" y="-27" width="149.75" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-44.875, -12)"><rect></rect><foreignObject width="89.75" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Start Upload</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-PROGRESS-23" transform="translate(587.171875, 1049.0573120117188)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-80.328125" y="-27" width="160.65625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-50.328125, -12)"><rect></rect><foreignObject width="100.65625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Show Progress</p></span></div></foreignObject></g></g><g class="node default validation" id="flowchart-UPLOAD_COMPLETE-25" transform="translate(587.171875, 1290.0104446411133)"><polygon points="91.73958587646484,0 183.4791717529297,-91.73958587646484 91.73958587646484,-183.4791717529297 0,-91.73958587646484" class="label-container" transform="translate(-91.73958587646484,91.73958587646484)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-64.73958587646484, -12)"><rect></rect><foreignObject width="129.4791717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Upload Complete?</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-VIRUS_SCAN-29" transform="translate(587.171875, 1482.7500305175781)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-66.34896087646484" y="-27" width="132.6979217529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-36.348960876464844, -12)"><rect></rect><foreignObject width="72.69792175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Virus Scan</p></span></div></foreignObject></g></g><g class="node default validation" id="flowchart-SCAN_RESULT-31" transform="translate(587.171875, 1628.5625305175781)"><polygon points="68.8125,0 137.625,-68.8125 68.8125,-137.625 0,-68.8125" class="label-container" transform="translate(-68.8125,68.8125)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-41.8125, -12)"><rect></rect><foreignObject width="83.625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Scan Clean?</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-DELETE_FILE-33" transform="translate(587.171875, 1798.3750305175781)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-101.421875" y="-27" width="202.84375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-71.421875, -12)"><rect></rect><foreignObject width="142.84375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Delete Infected File</p></span></div></foreignObject></g></g><g class="node default error" id="flowchart-VIRUS_ERROR-35" transform="translate(587.171875, 1902.3750305175781)"><rect class="basic label-container" style="fill:#FFEBEE !important;stroke:#F44336 !important" x="-103.04167175292969" y="-27" width="206.08334350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-73.04167175292969, -12)"><rect></rect><foreignObject width="146.08334350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Virus Detected Error</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-EXTRACT_METADATA-39" transform="translate(989.9739646911621, 1798.3750305175781)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-91.77083587646484" y="-27" width="183.5416717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-61.770835876464844, -12)"><rect></rect><foreignObject width="123.54167175292969" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Extract Metadata</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-GENERATE_URL-41" transform="translate(989.9739646911621, 1902.3750305175781)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-95.27604675292969" y="-27" width="190.55209350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-65.27604675292969, -12)"><rect></rect><foreignObject width="130.55209350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Generate File URL</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-ASSIGN_STORAGE-43" transform="translate(989.9739646911621, 2006.3750305175781)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-114.3125" y="-27" width="228.625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-84.3125, -12)"><rect></rect><foreignObject width="168.625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Assign Storage Location</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-CREATE_DB_RECORD-45" transform="translate(989.9739646911621, 2110.375030517578)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-116.078125" y="-27" width="232.15625" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-86.078125, -12)"><rect></rect><foreignObject width="172.15625" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Create Database Record</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-SET_PERMISSIONS-47" transform="translate(989.9739646911621, 2214.375030517578)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-110.38542175292969" y="-27" width="220.77084350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-80.38542175292969, -12)"><rect></rect><foreignObject width="160.77084350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Set Access Permissions</p></span></div></foreignObject></g></g><g class="node default notification" id="flowchart-NOTIFY_USERS-49" transform="translate(989.9739646911621, 2318.375030517578)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-106.796875" y="-27" width="213.59375" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-76.796875, -12)"><rect></rect><foreignObject width="153.59375" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Notify Relevant Users</p></span></div></foreignObject></g></g><g class="node default notification" id="flowchart-COURSE_STUDENTS-51" transform="translate(603.0104217529297, 2422.375030517578)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-111.72917175292969" y="-27" width="223.45834350585938" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-81.72917175292969, -12)"><rect></rect><foreignObject width="163.45834350585938" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Notify Course Students</p></span></div></foreignObject></g></g><g class="node default notification" id="flowchart-UPDATE_DASHBOARD-53" transform="translate(860.2291717529297, 2422.375030517578)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-95.48958587646484" y="-27" width="190.9791717529297" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-65.48958587646484, -12)"><rect></rect><foreignObject width="130.9791717529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update Dashboard</p></span></div></foreignObject></g></g><g class="node default notification" id="flowchart-SEND_EMAIL-55" transform="translate(1119.7187576293945, 2422.375030517578)"><rect class="basic label-container" style="fill:#F3E5F5 !important;stroke:#9C27B0 !important" x="-114" y="-27" width="228" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-84, -12)"><rect></rect><foreignObject width="168" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Send Email Notification</p></span></div></foreignObject></g></g><g class="node default success" id="flowchart-NOTIFICATION_SENT-57" transform="translate(861.3645896911621, 2526.375030517578)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" x="-105.625" y="-27" width="211.25" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-75.625, -12)"><rect></rect><foreignObject width="151.25" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>All Notifications Sent</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-AUDIT_LOG-63" transform="translate(861.3645896911621, 2630.375030517578)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-89.734375" y="-27" width="179.46875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-59.734375, -12)"><rect></rect><foreignObject width="119.46875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Create Audit Log</p></span></div></foreignObject></g></g><g class="node default process" id="flowchart-UPDATE_STATS-65" transform="translate(861.3645896911621, 2734.375030517578)"><rect class="basic label-container" style="fill:#E3F2FD !important;stroke:#2196F3 !important" x="-120.234375" y="-27" width="240.46875" height="54"></rect><g class="label" style="color:#000 !important" transform="translate(-90.234375, -12)"><rect></rect><foreignObject width="180.46875" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Update Storage Statistics</p></span></div></foreignObject></g></g><g class="node default success" id="flowchart-END_SUCCESS-67" transform="translate(861.3645896911621, 2830.875030517578)"><rect class="basic label-container" style="fill:#E8F5E8 !important;stroke:#4CAF50 !important" rx="19.5" ry="19.5" x="-76.41146087646484" y="-19.5" width="152.8229217529297" height="39"></rect><g class="label" style="color:#000 !important" transform="translate(-64.03646087646484, -12)"><rect></rect><foreignObject width="128.0729217529297" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Upload Successful</p></span></div></foreignObject></g></g><g class="node default validation" id="flowchart-UPLOAD_ERROR-69" transform="translate(798.9270896911621, 1049.0573120117188)"><polygon points="75.21354293823242,0 150.42708587646484,-75.21354293823242 75.21354293823242,-150.42708587646484 0,-75.21354293823242" class="label-container" transform="translate(-75.21354293823242,75.21354293823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-48.21354293823242, -12)"><rect></rect><foreignObject width="96.42708587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Upload Error?</p></span></div></foreignObject></g></g><g class="node default validation" id="flowchart-RETRY-71" transform="translate(859.697925567627, 1290.0104446411133)"><polygon points="76.22916793823242,0 152.45833587646484,-76.22916793823242 76.22916793823242,-152.45833587646484 0,-76.22916793823242" class="label-container" transform="translate(-76.22916793823242,76.22916793823242)" style="fill:#FFF3E0 !important;stroke:#FF9800 !important"></polygon><g class="label" style="color:#000 !important" transform="translate(-49.22916793823242, -12)"><rect></rect><foreignObject width="98.45833587646484" height="24"><div style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span style="color:#000 !important" class="nodeLabel"><p>Retry Upload?</p></span></div></foreignObject></g></g></g></g></g></svg>