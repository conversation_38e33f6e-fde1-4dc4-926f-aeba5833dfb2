<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" type="text/css"?>
<svg id="graph-735" width="100%" xmlns="http://www.w3.org/2000/svg" class="erDiagram" style="max-width: 100%;" viewBox="0.00000762939453125 0 2082.987060546875 1943.5" role="graphics-document document" aria-roledescription="er" height="100%" xmlns:xlink="http://www.w3.org/1999/xlink"><style>#graph-735{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#graph-735 .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#graph-735 .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#graph-735 .error-icon{fill:#a44141;}#graph-735 .error-text{fill:#ddd;stroke:#ddd;}#graph-735 .edge-thickness-normal{stroke-width:1px;}#graph-735 .edge-thickness-thick{stroke-width:3.5px;}#graph-735 .edge-pattern-solid{stroke-dasharray:0;}#graph-735 .edge-thickness-invisible{stroke-width:0;fill:none;}#graph-735 .edge-pattern-dashed{stroke-dasharray:3;}#graph-735 .edge-pattern-dotted{stroke-dasharray:2;}#graph-735 .marker{fill:lightgrey;stroke:lightgrey;}#graph-735 .marker.cross{stroke:lightgrey;}#graph-735 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#graph-735 p{margin:0;}#graph-735 .entityBox{fill:#1f2020;stroke:#ccc;}#graph-735 .relationshipLabelBox{fill:hsl(20, 1.5873015873%, 12.3529411765%);opacity:0.7;background-color:hsl(20, 1.5873015873%, 12.3529411765%);}#graph-735 .relationshipLabelBox rect{opacity:0.5;}#graph-735 .labelBkg{background-color:rgba(32.0000000001, 31.3333333334, 31.0000000001, 0.5);}#graph-735 .edgeLabel .label{fill:#ccc;font-size:14px;}#graph-735 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#graph-735 .edge-pattern-dashed{stroke-dasharray:8,8;}#graph-735 .node rect,#graph-735 .node circle,#graph-735 .node ellipse,#graph-735 .node polygon{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#graph-735 .relationshipLine{stroke:lightgrey;stroke-width:1;fill:none;}#graph-735 .marker{fill:none!important;stroke:lightgrey!important;stroke-width:1;}#graph-735 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}</style><g><defs><marker id="graph-735_er-onlyOneStart" class="marker onlyOne er" refX="0" refY="9" markerWidth="18" markerHeight="18" orient="auto"><path d="M9,0 L9,18 M15,0 L15,18"></path></marker></defs><defs><marker id="graph-735_er-onlyOneEnd" class="marker onlyOne er" refX="18" refY="9" markerWidth="18" markerHeight="18" orient="auto"><path d="M3,0 L3,18 M9,0 L9,18"></path></marker></defs><defs><marker id="graph-735_er-zeroOrOneStart" class="marker zeroOrOne er" refX="0" refY="9" markerWidth="30" markerHeight="18" orient="auto"><circle fill="white" cx="21" cy="9" r="6"></circle><path d="M9,0 L9,18"></path></marker></defs><defs><marker id="graph-735_er-zeroOrOneEnd" class="marker zeroOrOne er" refX="30" refY="9" markerWidth="30" markerHeight="18" orient="auto"><circle fill="white" cx="9" cy="9" r="6"></circle><path d="M21,0 L21,18"></path></marker></defs><defs><marker id="graph-735_er-oneOrMoreStart" class="marker oneOrMore er" refX="18" refY="18" markerWidth="45" markerHeight="36" orient="auto"><path d="M0,18 Q 18,0 36,18 Q 18,36 0,18 M42,9 L42,27"></path></marker></defs><defs><marker id="graph-735_er-oneOrMoreEnd" class="marker oneOrMore er" refX="27" refY="18" markerWidth="45" markerHeight="36" orient="auto"><path d="M3,9 L3,27 M9,18 Q27,0 45,18 Q27,36 9,18"></path></marker></defs><defs><marker id="graph-735_er-zeroOrMoreStart" class="marker zeroOrMore er" refX="18" refY="18" markerWidth="57" markerHeight="36" orient="auto"><circle fill="white" cx="48" cy="18" r="6"></circle><path d="M0,18 Q18,0 36,18 Q18,36 0,18"></path></marker></defs><defs><marker id="graph-735_er-zeroOrMoreEnd" class="marker zeroOrMore er" refX="39" refY="18" markerWidth="57" markerHeight="36" orient="auto"><circle fill="white" cx="9" cy="18" r="6"></circle><path d="M21,18 Q39,0 57,18 Q39,36 21,18"></path></marker></defs><g class="root"><g class="clusters"></g><g class="edgePaths"><path d="M938.406,350L938.406,358.417C938.406,366.833,938.406,383.667,938.406,400.5C938.406,417.333,938.406,434.167,938.406,442.583L938.406,451" id="id_entity-SCHOOLS-0_entity-USERS-1_0" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M790.849,219.644L681.417,249.787C571.984,279.929,353.12,340.215,243.688,385.899C134.255,431.583,134.255,462.667,134.255,478.208L134.255,493.75" id="id_entity-SCHOOLS-0_entity-ROLES-2_1" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1085.964,223.728L1183.16,253.19C1280.356,282.652,1474.748,341.576,1571.944,415.08C1669.141,488.583,1669.141,576.667,1669.141,664.75C1669.141,752.833,1669.141,840.917,1665.565,893.375C1661.989,945.833,1654.837,962.667,1651.262,971.083L1647.686,979.5" id="id_entity-SCHOOLS-0_entity-COURSES-4_2" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M800.339,718.952L711.162,753.96C621.986,788.968,443.634,858.984,344.715,920.221C245.797,981.458,226.312,1033.917,216.569,1060.146L206.827,1086.375" id="id_entity-USERS-1_entity-USER_ROLES-3_3" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M134.255,835.75L134.255,851.292C134.255,866.833,134.255,897.917,137.518,939.688C140.781,981.458,147.308,1033.917,150.571,1060.146L153.834,1086.375" id="id_entity-ROLES-2_entity-USER_ROLES-3_4" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1076.474,737.678L1136.843,769.565C1197.212,801.452,1317.95,865.226,1382.083,905.53C1446.216,945.833,1453.745,962.667,1457.51,971.083L1461.274,979.5" id="id_entity-USERS-1_entity-COURSES-4_5" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1418.245,1245.046L1323.475,1280.455C1228.705,1315.864,1039.165,1386.682,895.475,1452.925C751.785,1519.168,653.944,1580.836,605.024,1611.67L556.104,1642.504" id="id_entity-COURSES-4_entity-COURSE_STUDENTS-5_6" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M800.339,733.774L735.254,766.312C670.17,798.85,540.002,863.925,474.918,940.504C409.833,1017.083,409.833,1105.167,409.833,1193.25C409.833,1281.333,409.833,1369.417,411.872,1439.688C413.911,1509.958,417.989,1562.417,420.028,1588.646L422.067,1614.875" id="id_entity-USERS-1_entity-COURSE_STUDENTS-5_7" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1076.474,707.026L1197.296,744.022C1318.118,781.017,1559.762,855.009,1680.584,936.046C1801.406,1017.083,1801.406,1105.167,1801.406,1193.25C1801.406,1281.333,1801.406,1369.417,1780.236,1434.041C1759.065,1498.666,1716.724,1539.832,1695.553,1560.415L1674.383,1580.998" id="id_entity-USERS-1_entity-MARKS-6_8" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1556.875,1407L1556.875,1415.417C1556.875,1423.833,1556.875,1440.667,1556.007,1457.5C1555.138,1474.333,1553.402,1491.167,1552.533,1499.583L1551.665,1508" id="id_entity-COURSES-4_entity-MARKS-6_9" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1076.474,768.691L1111.964,795.409C1147.455,822.127,1218.436,875.564,1253.926,946.324C1289.417,1017.083,1289.417,1105.167,1289.417,1193.25C1289.417,1281.333,1289.417,1369.417,1305.321,1430.955C1321.225,1492.494,1353.033,1527.487,1368.937,1544.984L1384.841,1562.481" id="id_entity-USERS-1_entity-MARKS-6_10" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1695.505,1287.711L1737.035,1316.009C1778.565,1344.308,1861.625,1400.904,1903.155,1441.181C1944.685,1481.458,1944.685,1505.417,1944.685,1517.396L1944.685,1529.375" id="id_entity-COURSES-4_entity-MATERIALS-7_11" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1076.474,703.86L1208.939,741.384C1341.405,778.907,1606.335,853.953,1738.8,935.518C1871.266,1017.083,1871.266,1105.167,1871.266,1193.25C1871.266,1281.333,1871.266,1369.417,1874.594,1425.438C1877.922,1481.458,1884.579,1505.417,1887.907,1517.396L1891.235,1529.375" id="id_entity-USERS-1_entity-MATERIALS-7_12" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M837.297,878.5L833.315,886.917C829.334,895.333,821.371,912.167,812.049,929.399C802.727,946.632,792.046,964.264,786.705,973.08L781.365,981.896" id="id_entity-USERS-1_entity-NOTIFICATIONS-8_13" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M800.339,758.729L758.647,787.107C716.955,815.486,633.571,872.243,595.164,909.038C556.758,945.833,563.328,962.667,566.613,971.083L569.898,979.5" id="id_entity-USERS-1_entity-NOTIFICATIONS-8_14" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path><path d="M1026.685,878.5L1030.161,886.917C1033.637,895.333,1040.589,912.167,1044.066,929C1047.542,945.833,1047.542,962.667,1047.542,971.083L1047.542,979.5" id="id_entity-USERS-1_entity-AUDIT_LOGS-9_15" class="edge-thickness-normal edge-pattern-solid relationshipLine" style="" marker-start="url(#graph-735_er-onlyOneStart)" marker-end="url(#graph-735_er-zeroOrMoreEnd)"></path></g><g class="edgeLabels"><g class="edgeLabel" transform="translate(938.4062509536743, 400.5)"><g class="label" transform="translate(-10.338541984558105, -10.5)"><foreignObject width="20.67708396911621" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>has</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(134.25521087646484, 400.5)"><g class="label" transform="translate(-22.78125, -10.5)"><foreignObject width="45.5625" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>defines</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1669.140622138977, 664.75)"><g class="label" transform="translate(-18.307292938232422, -10.5)"><foreignObject width="36.614585876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>offers</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(265.28125286102295, 929)"><g class="label" transform="translate(-26.401042938232422, -10.5)"><foreignObject width="52.802085876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>assigned</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(134.25521087646484, 929)"><g class="label" transform="translate(-19.34895896911621, -10.5)"><foreignObject width="38.69791793823242" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>grants</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1438.6875009536743, 929)"><g class="label" transform="translate(-24.213542938232422, -10.5)"><foreignObject width="48.427085876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>teaches</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(849.625002861023, 1457.5)"><g class="label" transform="translate(-21.08333396911621, -10.5)"><foreignObject width="42.16666793823242" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>enrolls</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(409.83333683013916, 1193.25)"><g class="label" transform="translate(-35.458335876464844, -10.5)"><foreignObject width="70.91667175292969" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>enrolled_in</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1801.4062452316284, 1193.25)"><g class="label" transform="translate(-25.901042938232422, -10.5)"><foreignObject width="51.802085876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>receives</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1556.8749990463257, 1457.5)"><g class="label" transform="translate(-31.026042938232422, -10.5)"><foreignObject width="62.052085876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>graded_in</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1289.4166688919067, 1193.25)"><g class="label" transform="translate(-35.69791793823242, -10.5)"><foreignObject width="71.39583587646484" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>entered_by</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1944.6848888397217, 1457.5)"><g class="label" transform="translate(-26.15625, -10.5)"><foreignObject width="52.3125" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>contains</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1871.265622138977, 1193.25)"><g class="label" transform="translate(-23.95833396911621, -10.5)"><foreignObject width="47.91666793823242" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>uploads</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(813.4088525772095, 929)"><g class="label" transform="translate(-25.901042938232422, -10.5)"><foreignObject width="51.802085876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>receives</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(550.187502861023, 929)"><g class="label" transform="translate(-17.213542938232422, -10.5)"><foreignObject width="34.427085876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>sends</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(1047.5416650772095, 929)"><g class="label" transform="translate(-28.151042938232422, -10.5)"><foreignObject width="56.302085876464844" height="21"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel"><p>performs</p></span></div></foreignObject></g></g></g><g class="nodes"><g class="node default" id="entity-SCHOOLS-0" transform="translate(938.4062509536743, 179)"><g style=""><path d="M-147.55729484558105 -171 L147.55729484558105 -171 L147.55729484558105 171 L-147.55729484558105 171" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-147.55729484558105 -171 C-53.71589939674149 -171, 40.12549605209807 -171, 147.55729484558105 -171 M-147.55729484558105 -171 C-86.93300653363772 -171, -26.308718221694406 -171, 147.55729484558105 -171 M147.55729484558105 -171 C147.55729484558105 -77.96645807194007, 147.55729484558105 15.067083856119865, 147.55729484558105 171 M147.55729484558105 -171 C147.55729484558105 -71.83618913983446, 147.55729484558105 27.32762172033108, 147.55729484558105 171 M147.55729484558105 171 C82.37052193111683 171, 17.183749016652598 171, -147.55729484558105 171 M147.55729484558105 171 C62.97831484342632 171, -21.600665158728418 171, -147.55729484558105 171 M-147.55729484558105 171 C-147.55729484558105 98.20943083917233, -147.55729484558105 25.418861678344655, -147.55729484558105 -171 M-147.55729484558105 171 C-147.55729484558105 89.32471305110907, -147.55729484558105 7.649426102218143, -147.55729484558105 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-147.55729484558105 -128.25 L147.55729484558105 -128.25 L147.55729484558105 -85.5 L-147.55729484558105 -85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-147.55729484558105 -128.25 C-78.07783003436064 -128.25, -8.598365223140235 -128.25, 147.55729484558105 -128.25 M-147.55729484558105 -128.25 C-68.93605279609872 -128.25, 9.685189253383612 -128.25, 147.55729484558105 -128.25 M147.55729484558105 -128.25 C147.55729484558105 -118.8066107434577, 147.55729484558105 -109.3632214869154, 147.55729484558105 -85.5 M147.55729484558105 -128.25 C147.55729484558105 -111.85505897946405, 147.55729484558105 -95.4601179589281, 147.55729484558105 -85.5 M147.55729484558105 -85.5 C66.81859411511289 -85.5, -13.92010661535528 -85.5, -147.55729484558105 -85.5 M147.55729484558105 -85.5 C78.2642865774165 -85.5, 8.971278309251943 -85.5, -147.55729484558105 -85.5 M-147.55729484558105 -85.5 C-147.55729484558105 -94.31826942904651, -147.55729484558105 -103.13653885809302, -147.55729484558105 -128.25 M-147.55729484558105 -85.5 C-147.55729484558105 -96.37912093937786, -147.55729484558105 -107.25824187875573, -147.55729484558105 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-147.55729484558105 -85.5 L147.55729484558105 -85.5 L147.55729484558105 -42.75 L-147.55729484558105 -42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-147.55729484558105 -85.5 C-35.312328131156946 -85.5, 76.93263858326716 -85.5, 147.55729484558105 -85.5 M-147.55729484558105 -85.5 C-68.13003483334339 -85.5, 11.29722517889428 -85.5, 147.55729484558105 -85.5 M147.55729484558105 -85.5 C147.55729484558105 -73.21898382112576, 147.55729484558105 -60.937967642251515, 147.55729484558105 -42.75 M147.55729484558105 -85.5 C147.55729484558105 -70.73179042076193, 147.55729484558105 -55.96358084152385, 147.55729484558105 -42.75 M147.55729484558105 -42.75 C56.97834343431555 -42.75, -33.60060797694996 -42.75, -147.55729484558105 -42.75 M147.55729484558105 -42.75 C46.35054859317496 -42.75, -54.85619765923113 -42.75, -147.55729484558105 -42.75 M-147.55729484558105 -42.75 C-147.55729484558105 -56.78766674175942, -147.55729484558105 -70.82533348351885, -147.55729484558105 -85.5 M-147.55729484558105 -42.75 C-147.55729484558105 -55.92285366424953, -147.55729484558105 -69.09570732849906, -147.55729484558105 -85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-147.55729484558105 -42.75 L147.55729484558105 -42.75 L147.55729484558105 0 L-147.55729484558105 0" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-147.55729484558105 -42.75 C-73.05080778247972 -42.75, 1.4556792806216095 -42.75, 147.55729484558105 -42.75 M-147.55729484558105 -42.75 C-32.04927129715743 -42.75, 83.45875225126619 -42.75, 147.55729484558105 -42.75 M147.55729484558105 -42.75 C147.55729484558105 -31.656619337060537, 147.55729484558105 -20.563238674121074, 147.55729484558105 0 M147.55729484558105 -42.75 C147.55729484558105 -29.67620536768543, 147.55729484558105 -16.60241073537086, 147.55729484558105 0 M147.55729484558105 0 C33.398246714723086 0, -80.76080141613488 0, -147.55729484558105 0 M147.55729484558105 0 C44.488324024421075 0, -58.580646796738904 0, -147.55729484558105 0 M-147.55729484558105 0 C-147.55729484558105 -13.157774476533364, -147.55729484558105 -26.315548953066727, -147.55729484558105 -42.75 M-147.55729484558105 0 C-147.55729484558105 -16.86545811420022, -147.55729484558105 -33.73091622840044, -147.55729484558105 -42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-147.55729484558105 0 L147.55729484558105 0 L147.55729484558105 42.75 L-147.55729484558105 42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-147.55729484558105 0 C-34.237768286161895 0, 79.08175827325726 0, 147.55729484558105 0 M-147.55729484558105 0 C-57.16390737331412 0, 33.22948009895282 0, 147.55729484558105 0 M147.55729484558105 0 C147.55729484558105 12.937901500267284, 147.55729484558105 25.875803000534567, 147.55729484558105 42.75 M147.55729484558105 0 C147.55729484558105 9.496304525763984, 147.55729484558105 18.992609051527968, 147.55729484558105 42.75 M147.55729484558105 42.75 C54.602560598367376 42.75, -38.3521736488463 42.75, -147.55729484558105 42.75 M147.55729484558105 42.75 C76.91100124566887 42.75, 6.264707645756687 42.75, -147.55729484558105 42.75 M-147.55729484558105 42.75 C-147.55729484558105 32.02492777683088, -147.55729484558105 21.299855553661764, -147.55729484558105 0 M-147.55729484558105 42.75 C-147.55729484558105 31.879688239065597, -147.55729484558105 21.009376478131195, -147.55729484558105 0" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-147.55729484558105 42.75 L147.55729484558105 42.75 L147.55729484558105 85.5 L-147.55729484558105 85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-147.55729484558105 42.75 C-29.648999699489522 42.75, 88.25929544660201 42.75, 147.55729484558105 42.75 M-147.55729484558105 42.75 C-88.28649219283825 42.75, -29.01568954009545 42.75, 147.55729484558105 42.75 M147.55729484558105 42.75 C147.55729484558105 55.12634801568946, 147.55729484558105 67.50269603137892, 147.55729484558105 85.5 M147.55729484558105 42.75 C147.55729484558105 59.16498071480714, 147.55729484558105 75.57996142961429, 147.55729484558105 85.5 M147.55729484558105 85.5 C39.14060089144061 85.5, -69.27609306269983 85.5, -147.55729484558105 85.5 M147.55729484558105 85.5 C67.33730720752804 85.5, -12.882680430524971 85.5, -147.55729484558105 85.5 M-147.55729484558105 85.5 C-147.55729484558105 72.91051436182676, -147.55729484558105 60.32102872365353, -147.55729484558105 42.75 M-147.55729484558105 85.5 C-147.55729484558105 70.55242407560634, -147.55729484558105 55.60484815121269, -147.55729484558105 42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-147.55729484558105 85.5 L147.55729484558105 85.5 L147.55729484558105 128.25 L-147.55729484558105 128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-147.55729484558105 85.5 C-35.023733947503914 85.5, 77.50982695057323 85.5, 147.55729484558105 85.5 M-147.55729484558105 85.5 C-73.36043394824577 85.5, 0.836426949089514 85.5, 147.55729484558105 85.5 M147.55729484558105 85.5 C147.55729484558105 98.26346127518725, 147.55729484558105 111.02692255037451, 147.55729484558105 128.25 M147.55729484558105 85.5 C147.55729484558105 102.13005182901293, 147.55729484558105 118.76010365802587, 147.55729484558105 128.25 M147.55729484558105 128.25 C83.93460570397221 128.25, 20.311916562363365 128.25, -147.55729484558105 128.25 M147.55729484558105 128.25 C70.32789884191168 128.25, -6.901497161757703 128.25, -147.55729484558105 128.25 M-147.55729484558105 128.25 C-147.55729484558105 118.11610084086652, -147.55729484558105 107.98220168173303, -147.55729484558105 85.5 M-147.55729484558105 128.25 C-147.55729484558105 117.87266134585161, -147.55729484558105 107.49532269170321, -147.55729484558105 85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-147.55729484558105 128.25 L147.55729484558105 128.25 L147.55729484558105 171 L-147.55729484558105 171" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-147.55729484558105 128.25 C-81.36399399513897 128.25, -15.170693144696884 128.25, 147.55729484558105 128.25 M-147.55729484558105 128.25 C-43.42009846100734 128.25, 60.71709792356637 128.25, 147.55729484558105 128.25 M147.55729484558105 128.25 C147.55729484558105 139.6207957323418, 147.55729484558105 150.99159146468358, 147.55729484558105 171 M147.55729484558105 128.25 C147.55729484558105 143.29378101914844, 147.55729484558105 158.3375620382969, 147.55729484558105 171 M147.55729484558105 171 C73.01140286207432 171, -1.534489121432415 171, -147.55729484558105 171 M147.55729484558105 171 C48.54478395159077 171, -50.46772694239951 171, -147.55729484558105 171 M-147.55729484558105 171 C-147.55729484558105 158.4225380767126, -147.55729484558105 145.84507615342523, -147.55729484558105 128.25 M-147.55729484558105 171 C-147.55729484558105 154.48124207473964, -147.55729484558105 137.96248414947928, -147.55729484558105 128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-32.546875, -161.625)" style=""><foreignObject width="65.09375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: start;"><span class="nodeLabel"><p>SCHOOLS</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-135.05729484558105, -118.875)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-33.713544845581055, -118.875)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(116.92187690734863, -118.875)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(160.05729484558105, -118.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-135.05729484558105, -76.125)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-33.713544845581055, -76.125)" style=""><foreignObject width="39.15625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: start;"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(116.92187690734863, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(160.05729484558105, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-135.05729484558105, -33.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-33.713544845581055, -33.375)" style=""><foreignObject width="54.44791793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: start;"><span class="nodeLabel"><p>country</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(116.92187690734863, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(160.05729484558105, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-135.05729484558105, 9.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-33.713544845581055, 9.375)" style=""><foreignObject width="125.63542175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 242px; text-align: start;"><span class="nodeLabel"><p>subscription_plan</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(116.92187690734863, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(160.05729484558105, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-135.05729484558105, 52.125)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-33.713544845581055, 52.125)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(116.92187690734863, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(160.05729484558105, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-135.05729484558105, 94.875)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-33.713544845581055, 94.875)" style=""><foreignObject width="42.79166793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;"><span class="nodeLabel"><p>status</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(116.92187690734863, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(160.05729484558105, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-135.05729484558105, 137.625)" style=""><foreignObject width="29.67708396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>json</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-33.713544845581055, 137.625)" style=""><foreignObject width="55.708335876464844" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 167px; text-align: start;"><span class="nodeLabel"><p>settings</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(116.92187690734863, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(160.05729484558105, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-147.55729484558105 -128.25 C-79.2151612496404 -128.25, -10.87302765369975 -128.25, 147.55729484558105 -128.25 M-147.55729484558105 -128.25 C-40.14071287319048 -128.25, 67.2758690992001 -128.25, 147.55729484558105 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-46.213544845581055 -128.25 C-46.213544845581055 -14.332416066839528, -46.213544845581055 99.58516786632094, -46.213544845581055 171 M-46.213544845581055 -128.25 C-46.213544845581055 -65.09573459255122, -46.213544845581055 -1.9414691851024486, -46.213544845581055 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M104.42187690734863 -128.25 C104.42187690734863 -13.418404823388613, 104.42187690734863 101.41319035322277, 104.42187690734863 171 M104.42187690734863 -128.25 C104.42187690734863 -21.152178946245968, 104.42187690734863 85.94564210750806, 104.42187690734863 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-147.55729484558105 -128.25 C-71.88511738985345 -128.25, 3.787060065874158 -128.25, 147.55729484558105 -128.25 M-147.55729484558105 -128.25 C-39.52025023636544 -128.25, 68.51679437285017 -128.25, 147.55729484558105 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-USERS-1" transform="translate(938.4062509536743, 664.75)"><g style=""><path d="M-138.0677089691162 -213.75 L138.0677089691162 -213.75 L138.0677089691162 213.75 L-138.0677089691162 213.75" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-138.0677089691162 -213.75 C-61.29625155672041 -213.75, 15.475205855675398 -213.75, 138.0677089691162 -213.75 M-138.0677089691162 -213.75 C-67.92735195989411 -213.75, 2.2130050493279896 -213.75, 138.0677089691162 -213.75 M138.0677089691162 -213.75 C138.0677089691162 -58.1535845983546, 138.0677089691162 97.4428308032908, 138.0677089691162 213.75 M138.0677089691162 -213.75 C138.0677089691162 -104.76023534692641, 138.0677089691162 4.229529306147185, 138.0677089691162 213.75 M138.0677089691162 213.75 C82.77022289634782 213.75, 27.472736823579424 213.75, -138.0677089691162 213.75 M138.0677089691162 213.75 C39.17899638997196 213.75, -59.7097161891723 213.75, -138.0677089691162 213.75 M-138.0677089691162 213.75 C-138.0677089691162 95.56857188892751, -138.0677089691162 -22.612856222144984, -138.0677089691162 -213.75 M-138.0677089691162 213.75 C-138.0677089691162 68.17494598589016, -138.0677089691162 -77.40010802821968, -138.0677089691162 -213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.0677089691162 -171 L138.0677089691162 -171 L138.0677089691162 -128.25 L-138.0677089691162 -128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.0677089691162 -171 C-71.89034473130707 -171, -5.712980493497923 -171, 138.0677089691162 -171 M-138.0677089691162 -171 C-43.97780356960995 -171, 50.11210182989632 -171, 138.0677089691162 -171 M138.0677089691162 -171 C138.0677089691162 -161.27173386497805, 138.0677089691162 -151.5434677299561, 138.0677089691162 -128.25 M138.0677089691162 -171 C138.0677089691162 -161.77828069827385, 138.0677089691162 -152.55656139654772, 138.0677089691162 -128.25 M138.0677089691162 -128.25 C38.64627213478214 -128.25, -60.77516469955194 -128.25, -138.0677089691162 -128.25 M138.0677089691162 -128.25 C58.7604335984429 -128.25, -20.546841772230408 -128.25, -138.0677089691162 -128.25 M-138.0677089691162 -128.25 C-138.0677089691162 -142.91399278656465, -138.0677089691162 -157.5779855731293, -138.0677089691162 -171 M-138.0677089691162 -128.25 C-138.0677089691162 -143.13266459383132, -138.0677089691162 -158.01532918766264, -138.0677089691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.0677089691162 -128.25 L138.0677089691162 -128.25 L138.0677089691162 -85.5 L-138.0677089691162 -85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.0677089691162 -128.25 C-78.83113425687995 -128.25, -19.5945595446437 -128.25, 138.0677089691162 -128.25 M-138.0677089691162 -128.25 C-68.3213461203529 -128.25, 1.4250167284104123 -128.25, 138.0677089691162 -128.25 M138.0677089691162 -128.25 C138.0677089691162 -119.46703674888144, 138.0677089691162 -110.68407349776288, 138.0677089691162 -85.5 M138.0677089691162 -128.25 C138.0677089691162 -119.00757426081974, 138.0677089691162 -109.76514852163947, 138.0677089691162 -85.5 M138.0677089691162 -85.5 C79.91129862204639 -85.5, 21.754888274976565 -85.5, -138.0677089691162 -85.5 M138.0677089691162 -85.5 C56.89801883450258 -85.5, -24.271671300111052 -85.5, -138.0677089691162 -85.5 M-138.0677089691162 -85.5 C-138.0677089691162 -95.72096869178935, -138.0677089691162 -105.9419373835787, -138.0677089691162 -128.25 M-138.0677089691162 -85.5 C-138.0677089691162 -99.43457712563328, -138.0677089691162 -113.36915425126658, -138.0677089691162 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.0677089691162 -85.5 L138.0677089691162 -85.5 L138.0677089691162 -42.75 L-138.0677089691162 -42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.0677089691162 -85.5 C-50.888567184994486 -85.5, 36.29057459912724 -85.5, 138.0677089691162 -85.5 M-138.0677089691162 -85.5 C-39.01098833813022 -85.5, 60.045732292855774 -85.5, 138.0677089691162 -85.5 M138.0677089691162 -85.5 C138.0677089691162 -71.86914795347298, 138.0677089691162 -58.238295906945936, 138.0677089691162 -42.75 M138.0677089691162 -85.5 C138.0677089691162 -69.48615707787707, 138.0677089691162 -53.47231415575413, 138.0677089691162 -42.75 M138.0677089691162 -42.75 C65.5997659263809 -42.75, -6.868177116354417 -42.75, -138.0677089691162 -42.75 M138.0677089691162 -42.75 C76.68356594456517 -42.75, 15.299422920014138 -42.75, -138.0677089691162 -42.75 M-138.0677089691162 -42.75 C-138.0677089691162 -55.088413755620635, -138.0677089691162 -67.42682751124127, -138.0677089691162 -85.5 M-138.0677089691162 -42.75 C-138.0677089691162 -56.64988510976377, -138.0677089691162 -70.54977021952755, -138.0677089691162 -85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.0677089691162 -42.75 L138.0677089691162 -42.75 L138.0677089691162 0 L-138.0677089691162 0" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.0677089691162 -42.75 C-67.19721537864241 -42.75, 3.673278211831388 -42.75, 138.0677089691162 -42.75 M-138.0677089691162 -42.75 C-43.80847873024631 -42.75, 50.4507515086236 -42.75, 138.0677089691162 -42.75 M138.0677089691162 -42.75 C138.0677089691162 -29.262016262709025, 138.0677089691162 -15.774032525418047, 138.0677089691162 0 M138.0677089691162 -42.75 C138.0677089691162 -29.64742142979864, 138.0677089691162 -16.54484285959728, 138.0677089691162 0 M138.0677089691162 0 C31.256368507651786 0, -75.55497195381264 0, -138.0677089691162 0 M138.0677089691162 0 C57.39016022860362 0, -23.287388511908972 0, -138.0677089691162 0 M-138.0677089691162 0 C-138.0677089691162 -9.771560564273603, -138.0677089691162 -19.543121128547206, -138.0677089691162 -42.75 M-138.0677089691162 0 C-138.0677089691162 -10.383576289639851, -138.0677089691162 -20.767152579279703, -138.0677089691162 -42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.0677089691162 0 L138.0677089691162 0 L138.0677089691162 42.75 L-138.0677089691162 42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.0677089691162 0 C-44.355202042432865 0, 49.35730488425048 0, 138.0677089691162 0 M-138.0677089691162 0 C-69.68947055059427 0, -1.3112321320723197 0, 138.0677089691162 0 M138.0677089691162 0 C138.0677089691162 15.010173601000073, 138.0677089691162 30.020347202000146, 138.0677089691162 42.75 M138.0677089691162 0 C138.0677089691162 11.160023076435289, 138.0677089691162 22.320046152870578, 138.0677089691162 42.75 M138.0677089691162 42.75 C39.66721856512348 42.75, -58.73327183886926 42.75, -138.0677089691162 42.75 M138.0677089691162 42.75 C32.933779324508635 42.75, -72.20015032009894 42.75, -138.0677089691162 42.75 M-138.0677089691162 42.75 C-138.0677089691162 30.90194085178628, -138.0677089691162 19.053881703572557, -138.0677089691162 0 M-138.0677089691162 42.75 C-138.0677089691162 30.37287110956539, -138.0677089691162 17.995742219130783, -138.0677089691162 0" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.0677089691162 42.75 L138.0677089691162 42.75 L138.0677089691162 85.5 L-138.0677089691162 85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.0677089691162 42.75 C-52.05132007445627 42.75, 33.96506882020367 42.75, 138.0677089691162 42.75 M-138.0677089691162 42.75 C-46.822600067537365 42.75, 44.42250883404148 42.75, 138.0677089691162 42.75 M138.0677089691162 42.75 C138.0677089691162 53.845092033831406, 138.0677089691162 64.94018406766281, 138.0677089691162 85.5 M138.0677089691162 42.75 C138.0677089691162 55.97178913362285, 138.0677089691162 69.1935782672457, 138.0677089691162 85.5 M138.0677089691162 85.5 C34.63124098578972 85.5, -68.80522699753678 85.5, -138.0677089691162 85.5 M138.0677089691162 85.5 C82.28326243968483 85.5, 26.498815910253455 85.5, -138.0677089691162 85.5 M-138.0677089691162 85.5 C-138.0677089691162 70.29102162543568, -138.0677089691162 55.08204325087135, -138.0677089691162 42.75 M-138.0677089691162 85.5 C-138.0677089691162 76.082568988632, -138.0677089691162 66.66513797726402, -138.0677089691162 42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.0677089691162 85.5 L138.0677089691162 85.5 L138.0677089691162 128.25 L-138.0677089691162 128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.0677089691162 85.5 C-70.06964646541927 85.5, -2.0715839617223253 85.5, 138.0677089691162 85.5 M-138.0677089691162 85.5 C-52.232922011795665 85.5, 33.60186494552488 85.5, 138.0677089691162 85.5 M138.0677089691162 85.5 C138.0677089691162 100.28903860013554, 138.0677089691162 115.07807720027108, 138.0677089691162 128.25 M138.0677089691162 85.5 C138.0677089691162 94.44756752291086, 138.0677089691162 103.39513504582172, 138.0677089691162 128.25 M138.0677089691162 128.25 C39.81917264487868 128.25, -58.42936367935886 128.25, -138.0677089691162 128.25 M138.0677089691162 128.25 C46.88485230897125 128.25, -44.29800435117372 128.25, -138.0677089691162 128.25 M-138.0677089691162 128.25 C-138.0677089691162 118.51806868399265, -138.0677089691162 108.78613736798532, -138.0677089691162 85.5 M-138.0677089691162 128.25 C-138.0677089691162 115.42300000909553, -138.0677089691162 102.59600001819106, -138.0677089691162 85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.0677089691162 128.25 L138.0677089691162 128.25 L138.0677089691162 171 L-138.0677089691162 171" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.0677089691162 128.25 C-28.67349465827607 128.25, 80.72071965256407 128.25, 138.0677089691162 128.25 M-138.0677089691162 128.25 C-61.156346354785725 128.25, 15.75501625954476 128.25, 138.0677089691162 128.25 M138.0677089691162 128.25 C138.0677089691162 141.58043519933045, 138.0677089691162 154.9108703986609, 138.0677089691162 171 M138.0677089691162 128.25 C138.0677089691162 142.24515999277267, 138.0677089691162 156.2403199855453, 138.0677089691162 171 M138.0677089691162 171 C50.181570016580054 171, -37.704568935956104 171, -138.0677089691162 171 M138.0677089691162 171 C47.48634356230238 171, -43.09502184451145 171, -138.0677089691162 171 M-138.0677089691162 171 C-138.0677089691162 155.689333102066, -138.0677089691162 140.378666204132, -138.0677089691162 128.25 M-138.0677089691162 171 C-138.0677089691162 155.19052767362, -138.0677089691162 139.38105534724, -138.0677089691162 128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.0677089691162 171 L138.0677089691162 171 L138.0677089691162 213.75 L-138.0677089691162 213.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.0677089691162 171 C-43.4764138484807 171, 51.114881272154804 171, 138.0677089691162 171 M-138.0677089691162 171 C-46.91674624388722 171, 44.234216481341775 171, 138.0677089691162 171 M138.0677089691162 171 C138.0677089691162 182.30657320123925, 138.0677089691162 193.61314640247846, 138.0677089691162 213.75 M138.0677089691162 171 C138.0677089691162 180.49209254905858, 138.0677089691162 189.98418509811717, 138.0677089691162 213.75 M138.0677089691162 213.75 C61.204827197721286 213.75, -15.658054573673638 213.75, -138.0677089691162 213.75 M138.0677089691162 213.75 C67.54539364647552 213.75, -2.9769216761651762 213.75, -138.0677089691162 213.75 M-138.0677089691162 213.75 C-138.0677089691162 200.77340928387963, -138.0677089691162 187.7968185677593, -138.0677089691162 171 M-138.0677089691162 213.75 C-138.0677089691162 198.22265868227464, -138.0677089691162 182.69531736454928, -138.0677089691162 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-21.828125, -204.375)" style=""><foreignObject width="43.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>USERS</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, -161.625)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, -161.625)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, -161.625)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, -161.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, -118.875)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, -118.875)" style=""><foreignObject width="66.90625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: start;"><span class="nodeLabel"><p>school_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, -118.875)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, -118.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, -76.125)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, -76.125)" style=""><foreignObject width="39.69791793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 144px; text-align: start;"><span class="nodeLabel"><p>email</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, -33.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, -33.375)" style=""><foreignObject width="77.0625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>first_name</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, 9.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, 9.375)" style=""><foreignObject width="73.5" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 182px; text-align: start;"><span class="nodeLabel"><p>last_name</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, 52.125)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, 52.125)" style=""><foreignObject width="106.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 222px; text-align: start;"><span class="nodeLabel"><p>password_hash</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, 94.875)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, 94.875)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, 137.625)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, 137.625)" style=""><foreignObject width="68.97917175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 180px; text-align: start;"><span class="nodeLabel"><p>last_login</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-125.56770896911621, 180.375)" style=""><foreignObject width="56.6875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 163px; text-align: start;"><span class="nodeLabel"><p>boolean</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.22395896911621, 180.375)" style=""><foreignObject width="63.22916793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 173px; text-align: start;"><span class="nodeLabel"><p>is_active</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.43229103088379, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(150.5677089691162, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-138.0677089691162 -171 C-65.78871152873877 -171, 6.490285911638665 -171, 138.0677089691162 -171 M-138.0677089691162 -171 C-81.58832732116046 -171, -25.108945673204715 -171, 138.0677089691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-36.72395896911621 -171 C-36.72395896911621 -40.933716497616274, -36.72395896911621 89.13256700476745, -36.72395896911621 213.75 M-36.72395896911621 -171 C-36.72395896911621 -34.29273623533845, -36.72395896911621 102.4145275293231, -36.72395896911621 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M94.93229103088379 -171 C94.93229103088379 -82.39106272733227, 94.93229103088379 6.217874545335462, 94.93229103088379 213.75 M94.93229103088379 -171 C94.93229103088379 -35.118737262616435, 94.93229103088379 100.76252547476713, 94.93229103088379 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-138.0677089691162 -171 C-57.552193857909444 -171, 22.963321253297323 -171, 138.0677089691162 -171 M-138.0677089691162 -171 C-50.352988171957264 -171, 37.36173262520168 -171, 138.0677089691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-ROLES-2" transform="translate(134.25521087646484, 664.75)"><g style=""><path d="M-126.25520896911621 -171 L126.25520896911621 -171 L126.25520896911621 171 L-126.25520896911621 171" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-126.25520896911621 -171 C-72.40739709263903 -171, -18.559585216161864 -171, 126.25520896911621 -171 M-126.25520896911621 -171 C-49.9395522832955 -171, 26.376104402525215 -171, 126.25520896911621 -171 M126.25520896911621 -171 C126.25520896911621 -62.72672192031868, 126.25520896911621 45.54655615936264, 126.25520896911621 171 M126.25520896911621 -171 C126.25520896911621 -88.41035897468373, 126.25520896911621 -5.820717949367463, 126.25520896911621 171 M126.25520896911621 171 C46.99600840930995 171, -32.26319215049631 171, -126.25520896911621 171 M126.25520896911621 171 C60.566269658309366 171, -5.122669652497478 171, -126.25520896911621 171 M-126.25520896911621 171 C-126.25520896911621 101.19727871549917, -126.25520896911621 31.394557430998333, -126.25520896911621 -171 M-126.25520896911621 171 C-126.25520896911621 62.67002581886784, -126.25520896911621 -45.659948362264316, -126.25520896911621 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.25520896911621 -128.25 L126.25520896911621 -128.25 L126.25520896911621 -85.5 L-126.25520896911621 -85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.25520896911621 -128.25 C-49.14470367121119 -128.25, 27.965801626693832 -128.25, 126.25520896911621 -128.25 M-126.25520896911621 -128.25 C-39.57927270241491 -128.25, 47.09666356428639 -128.25, 126.25520896911621 -128.25 M126.25520896911621 -128.25 C126.25520896911621 -119.31392503484228, 126.25520896911621 -110.37785006968457, 126.25520896911621 -85.5 M126.25520896911621 -128.25 C126.25520896911621 -113.03699500314553, 126.25520896911621 -97.82399000629108, 126.25520896911621 -85.5 M126.25520896911621 -85.5 C61.41337199534763 -85.5, -3.4284649784209478 -85.5, -126.25520896911621 -85.5 M126.25520896911621 -85.5 C56.67171310130047 -85.5, -12.911782766515273 -85.5, -126.25520896911621 -85.5 M-126.25520896911621 -85.5 C-126.25520896911621 -100.40963488370564, -126.25520896911621 -115.31926976741127, -126.25520896911621 -128.25 M-126.25520896911621 -85.5 C-126.25520896911621 -97.62514381923117, -126.25520896911621 -109.75028763846234, -126.25520896911621 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-126.25520896911621 -85.5 L126.25520896911621 -85.5 L126.25520896911621 -42.75 L-126.25520896911621 -42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-126.25520896911621 -85.5 C-58.34110443942389 -85.5, 9.573000090268437 -85.5, 126.25520896911621 -85.5 M-126.25520896911621 -85.5 C-47.34768474105334 -85.5, 31.559839487009526 -85.5, 126.25520896911621 -85.5 M126.25520896911621 -85.5 C126.25520896911621 -73.66157407680558, 126.25520896911621 -61.82314815361116, 126.25520896911621 -42.75 M126.25520896911621 -85.5 C126.25520896911621 -69.26652637411777, 126.25520896911621 -53.03305274823556, 126.25520896911621 -42.75 M126.25520896911621 -42.75 C68.79152211978058 -42.75, 11.327835270444936 -42.75, -126.25520896911621 -42.75 M126.25520896911621 -42.75 C32.182795619622155 -42.75, -61.8896177298719 -42.75, -126.25520896911621 -42.75 M-126.25520896911621 -42.75 C-126.25520896911621 -51.64988227475909, -126.25520896911621 -60.54976454951819, -126.25520896911621 -85.5 M-126.25520896911621 -42.75 C-126.25520896911621 -56.91287050284155, -126.25520896911621 -71.0757410056831, -126.25520896911621 -85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.25520896911621 -42.75 L126.25520896911621 -42.75 L126.25520896911621 0 L-126.25520896911621 0" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.25520896911621 -42.75 C-63.07412518744538 -42.75, 0.10695859422544629 -42.75, 126.25520896911621 -42.75 M-126.25520896911621 -42.75 C-48.268626447498775 -42.75, 29.71795607411866 -42.75, 126.25520896911621 -42.75 M126.25520896911621 -42.75 C126.25520896911621 -26.665572723729404, 126.25520896911621 -10.581145447458809, 126.25520896911621 0 M126.25520896911621 -42.75 C126.25520896911621 -33.931673745028675, 126.25520896911621 -25.113347490057357, 126.25520896911621 0 M126.25520896911621 0 C47.18438169940556 0, -31.886445570305085 0, -126.25520896911621 0 M126.25520896911621 0 C27.120009670180906 0, -72.0151896287544 0, -126.25520896911621 0 M-126.25520896911621 0 C-126.25520896911621 -10.32264500056763, -126.25520896911621 -20.64529000113526, -126.25520896911621 -42.75 M-126.25520896911621 0 C-126.25520896911621 -15.956137447652818, -126.25520896911621 -31.912274895305636, -126.25520896911621 -42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-126.25520896911621 0 L126.25520896911621 0 L126.25520896911621 42.75 L-126.25520896911621 42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-126.25520896911621 0 C-71.58999762667614 0, -16.92478628423605 0, 126.25520896911621 0 M-126.25520896911621 0 C-63.95686966678405 0, -1.6585303644518916 0, 126.25520896911621 0 M126.25520896911621 0 C126.25520896911621 14.622323266007905, 126.25520896911621 29.24464653201581, 126.25520896911621 42.75 M126.25520896911621 0 C126.25520896911621 12.762442031430748, 126.25520896911621 25.524884062861496, 126.25520896911621 42.75 M126.25520896911621 42.75 C64.86869415610246 42.75, 3.4821793430887027 42.75, -126.25520896911621 42.75 M126.25520896911621 42.75 C26.251313335324852 42.75, -73.7525822984665 42.75, -126.25520896911621 42.75 M-126.25520896911621 42.75 C-126.25520896911621 28.38945970158703, -126.25520896911621 14.028919403174058, -126.25520896911621 0 M-126.25520896911621 42.75 C-126.25520896911621 25.7876332890494, -126.25520896911621 8.825266578098798, -126.25520896911621 0" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.25520896911621 42.75 L126.25520896911621 42.75 L126.25520896911621 85.5 L-126.25520896911621 85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.25520896911621 42.75 C-70.20160614887916 42.75, -14.148003328642119 42.75, 126.25520896911621 42.75 M-126.25520896911621 42.75 C-27.85639316138503 42.75, 70.54242264634615 42.75, 126.25520896911621 42.75 M126.25520896911621 42.75 C126.25520896911621 54.775580873443246, 126.25520896911621 66.80116174688649, 126.25520896911621 85.5 M126.25520896911621 42.75 C126.25520896911621 59.165067835803136, 126.25520896911621 75.58013567160627, 126.25520896911621 85.5 M126.25520896911621 85.5 C56.000215550168164 85.5, -14.254777868779883 85.5, -126.25520896911621 85.5 M126.25520896911621 85.5 C61.254192808187995 85.5, -3.746823352740222 85.5, -126.25520896911621 85.5 M-126.25520896911621 85.5 C-126.25520896911621 72.37290633902003, -126.25520896911621 59.24581267804007, -126.25520896911621 42.75 M-126.25520896911621 85.5 C-126.25520896911621 76.80512637588244, -126.25520896911621 68.11025275176488, -126.25520896911621 42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-126.25520896911621 85.5 L126.25520896911621 85.5 L126.25520896911621 128.25 L-126.25520896911621 128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-126.25520896911621 85.5 C-51.51801237313791 85.5, 23.219184222840397 85.5, 126.25520896911621 85.5 M-126.25520896911621 85.5 C-32.956761811906716 85.5, 60.34168534530278 85.5, 126.25520896911621 85.5 M126.25520896911621 85.5 C126.25520896911621 98.64037529602734, 126.25520896911621 111.78075059205469, 126.25520896911621 128.25 M126.25520896911621 85.5 C126.25520896911621 101.0067374906798, 126.25520896911621 116.51347498135961, 126.25520896911621 128.25 M126.25520896911621 128.25 C65.93288561752328 128.25, 5.610562265930341 128.25, -126.25520896911621 128.25 M126.25520896911621 128.25 C37.561864533744625 128.25, -51.13147990162696 128.25, -126.25520896911621 128.25 M-126.25520896911621 128.25 C-126.25520896911621 115.39810313077109, -126.25520896911621 102.54620626154218, -126.25520896911621 85.5 M-126.25520896911621 128.25 C-126.25520896911621 116.21005385051812, -126.25520896911621 104.17010770103624, -126.25520896911621 85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.25520896911621 128.25 L126.25520896911621 128.25 L126.25520896911621 171 L-126.25520896911621 171" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.25520896911621 128.25 C-35.924999836129274 128.25, 54.40520929685766 128.25, 126.25520896911621 128.25 M-126.25520896911621 128.25 C-35.33538831575447 128.25, 55.58443233760727 128.25, 126.25520896911621 128.25 M126.25520896911621 128.25 C126.25520896911621 143.48122520510879, 126.25520896911621 158.71245041021754, 126.25520896911621 171 M126.25520896911621 128.25 C126.25520896911621 139.54657928944386, 126.25520896911621 150.84315857888774, 126.25520896911621 171 M126.25520896911621 171 C30.66332843133428 171, -64.92855210644765 171, -126.25520896911621 171 M126.25520896911621 171 C43.23527426965593 171, -39.78466042980435 171, -126.25520896911621 171 M-126.25520896911621 171 C-126.25520896911621 154.80603712726634, -126.25520896911621 138.61207425453264, -126.25520896911621 128.25 M-126.25520896911621 171 C-126.25520896911621 156.60541752600585, -126.25520896911621 142.2108350520117, -126.25520896911621 128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-22.234375, -161.625)" style=""><foreignObject width="44.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 146px; text-align: start;"><span class="nodeLabel"><p>ROLES</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.75520896911621, -118.875)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.411458969116211, -118.875)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.61979103088379, -118.875)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.7552089691162, -118.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.75520896911621, -76.125)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.411458969116211, -76.125)" style=""><foreignObject width="66.90625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: start;"><span class="nodeLabel"><p>school_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.61979103088379, -76.125)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.7552089691162, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.75520896911621, -33.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.411458969116211, -33.375)" style=""><foreignObject width="39.15625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: start;"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.61979103088379, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.7552089691162, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.75520896911621, 9.375)" style=""><foreignObject width="29.67708396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>json</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.411458969116211, 9.375)" style=""><foreignObject width="83.03125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: start;"><span class="nodeLabel"><p>permissions</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.61979103088379, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.7552089691162, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.75520896911621, 52.125)" style=""><foreignObject width="56.6875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 163px; text-align: start;"><span class="nodeLabel"><p>boolean</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.411458969116211, 52.125)" style=""><foreignObject width="71.19792175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 180px; text-align: start;"><span class="nodeLabel"><p>is_default</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.61979103088379, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.7552089691162, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.75520896911621, 94.875)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.411458969116211, 94.875)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.61979103088379, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.7552089691162, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.75520896911621, 137.625)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.411458969116211, 137.625)" style=""><foreignObject width="79.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: start;"><span class="nodeLabel"><p>description</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.61979103088379, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.7552089691162, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-126.25520896911621 -128.25 C-55.116878316740554 -128.25, 16.021452335635104 -128.25, 126.25520896911621 -128.25 M-126.25520896911621 -128.25 C-57.73170918654597 -128.25, 10.79179059602427 -128.25, 126.25520896911621 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-24.91145896911621 -128.25 C-24.91145896911621 -46.91668730683439, -24.91145896911621 34.41662538633122, -24.91145896911621 171 M-24.91145896911621 -128.25 C-24.91145896911621 -56.695632979592915, -24.91145896911621 14.85873404081417, -24.91145896911621 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M83.11979103088379 -128.25 C83.11979103088379 -61.95664364239167, 83.11979103088379 4.336712715216663, 83.11979103088379 171 M83.11979103088379 -128.25 C83.11979103088379 -67.66842837714356, 83.11979103088379 -7.086856754287112, 83.11979103088379 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-126.25520896911621 -128.25 C-74.77849621817636 -128.25, -23.301783467236504 -128.25, 126.25520896911621 -128.25 M-126.25520896911621 -128.25 C-56.10222937915698 -128.25, 14.05075021080225 -128.25, 126.25520896911621 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-USER_ROLES-3" transform="translate(167.13021183013916, 1193.25)"><g style=""><path d="M-127.2447919845581 -106.875 L127.2447919845581 -106.875 L127.2447919845581 106.875 L-127.2447919845581 106.875" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-127.2447919845581 -106.875 C-73.86363225447013 -106.875, -20.482472524382146 -106.875, 127.2447919845581 -106.875 M-127.2447919845581 -106.875 C-72.6194644276531 -106.875, -17.994136870748108 -106.875, 127.2447919845581 -106.875 M127.2447919845581 -106.875 C127.2447919845581 -56.4241126161333, 127.2447919845581 -5.9732252322666, 127.2447919845581 106.875 M127.2447919845581 -106.875 C127.2447919845581 -48.82878985132283, 127.2447919845581 9.217420297354337, 127.2447919845581 106.875 M127.2447919845581 106.875 C32.42710942314754 106.875, -62.39057313826302 106.875, -127.2447919845581 106.875 M127.2447919845581 106.875 C56.80158386214414 106.875, -13.641624260269822 106.875, -127.2447919845581 106.875 M-127.2447919845581 106.875 C-127.2447919845581 22.19993078241812, -127.2447919845581 -62.47513843516376, -127.2447919845581 -106.875 M-127.2447919845581 106.875 C-127.2447919845581 35.45618210431603, -127.2447919845581 -35.962635791367944, -127.2447919845581 -106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-127.2447919845581 -64.125 L127.2447919845581 -64.125 L127.2447919845581 -21.375 L-127.2447919845581 -21.375" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-127.2447919845581 -64.125 C-69.65522715389673 -64.125, -12.065662323235372 -64.125, 127.2447919845581 -64.125 M-127.2447919845581 -64.125 C-42.207037125283975 -64.125, 42.830717733990156 -64.125, 127.2447919845581 -64.125 M127.2447919845581 -64.125 C127.2447919845581 -50.948905246539404, 127.2447919845581 -37.77281049307881, 127.2447919845581 -21.375 M127.2447919845581 -64.125 C127.2447919845581 -55.0591011695413, 127.2447919845581 -45.9932023390826, 127.2447919845581 -21.375 M127.2447919845581 -21.375 C63.39832411567481 -21.375, -0.448143753208484 -21.375, -127.2447919845581 -21.375 M127.2447919845581 -21.375 C54.80774249652424 -21.375, -17.629306991509623 -21.375, -127.2447919845581 -21.375 M-127.2447919845581 -21.375 C-127.2447919845581 -32.671178841669175, -127.2447919845581 -43.96735768333835, -127.2447919845581 -64.125 M-127.2447919845581 -21.375 C-127.2447919845581 -35.357292732577555, -127.2447919845581 -49.33958546515511, -127.2447919845581 -64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-127.2447919845581 -21.375 L127.2447919845581 -21.375 L127.2447919845581 21.375 L-127.2447919845581 21.375" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-127.2447919845581 -21.375 C-49.497518229199585 -21.375, 28.249755526158935 -21.375, 127.2447919845581 -21.375 M-127.2447919845581 -21.375 C-31.07649466080443 -21.375, 65.09180266294925 -21.375, 127.2447919845581 -21.375 M127.2447919845581 -21.375 C127.2447919845581 -12.81246189929809, 127.2447919845581 -4.249923798596178, 127.2447919845581 21.375 M127.2447919845581 -21.375 C127.2447919845581 -5.605462885571132, 127.2447919845581 10.164074228857736, 127.2447919845581 21.375 M127.2447919845581 21.375 C45.867521477888005 21.375, -35.509749028782096 21.375, -127.2447919845581 21.375 M127.2447919845581 21.375 C32.724978799736064 21.375, -61.79483438508598 21.375, -127.2447919845581 21.375 M-127.2447919845581 21.375 C-127.2447919845581 5.500413022926695, -127.2447919845581 -10.37417395414661, -127.2447919845581 -21.375 M-127.2447919845581 21.375 C-127.2447919845581 4.365761958727262, -127.2447919845581 -12.643476082545476, -127.2447919845581 -21.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-127.2447919845581 21.375 L127.2447919845581 21.375 L127.2447919845581 64.125 L-127.2447919845581 64.125" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-127.2447919845581 21.375 C-50.94196370771384 21.375, 25.360864569130428 21.375, 127.2447919845581 21.375 M-127.2447919845581 21.375 C-42.682567142144606 21.375, 41.87965770026889 21.375, 127.2447919845581 21.375 M127.2447919845581 21.375 C127.2447919845581 35.47753987190222, 127.2447919845581 49.58007974380444, 127.2447919845581 64.125 M127.2447919845581 21.375 C127.2447919845581 35.42010930806016, 127.2447919845581 49.46521861612032, 127.2447919845581 64.125 M127.2447919845581 64.125 C62.98062670797363 64.125, -1.283538568610851 64.125, -127.2447919845581 64.125 M127.2447919845581 64.125 C58.222333516212444 64.125, -10.800124952133217 64.125, -127.2447919845581 64.125 M-127.2447919845581 64.125 C-127.2447919845581 51.854383277054, -127.2447919845581 39.583766554108, -127.2447919845581 21.375 M-127.2447919845581 64.125 C-127.2447919845581 54.76212110797454, -127.2447919845581 45.39924221594907, -127.2447919845581 21.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-127.2447919845581 64.125 L127.2447919845581 64.125 L127.2447919845581 106.875 L-127.2447919845581 106.875" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-127.2447919845581 64.125 C-61.24959860353768 64.125, 4.745594777482751 64.125, 127.2447919845581 64.125 M-127.2447919845581 64.125 C-26.61765736095468 64.125, 74.00947726264874 64.125, 127.2447919845581 64.125 M127.2447919845581 64.125 C127.2447919845581 79.25888376538343, 127.2447919845581 94.39276753076686, 127.2447919845581 106.875 M127.2447919845581 64.125 C127.2447919845581 74.73328831669488, 127.2447919845581 85.34157663338976, 127.2447919845581 106.875 M127.2447919845581 106.875 C47.41470298612727 106.875, -32.41538601230357 106.875, -127.2447919845581 106.875 M127.2447919845581 106.875 C45.69303504213367 106.875, -35.85872190029076 106.875, -127.2447919845581 106.875 M-127.2447919845581 106.875 C-127.2447919845581 91.40854657155967, -127.2447919845581 75.94209314311934, -127.2447919845581 64.125 M-127.2447919845581 106.875 C-127.2447919845581 91.14107794756046, -127.2447919845581 75.40715589512092, -127.2447919845581 64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-44.40625, -97.5)" style=""><foreignObject width="88.8125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: start;"><span class="nodeLabel"><p>USER_ROLES</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-114.7447919845581, -54.75)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-13.401041984558105, -54.75)" style=""><foreignObject width="52.03125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 157px; text-align: start;"><span class="nodeLabel"><p>user_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.1302080154419, -54.75)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(139.7447919845581, -54.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-114.7447919845581, -12)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-13.401041984558105, -12)" style=""><foreignObject width="50.125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 155px; text-align: start;"><span class="nodeLabel"><p>role_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.1302080154419, -12)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(139.7447919845581, -12)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-114.7447919845581, 30.75)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-13.401041984558105, 30.75)" style=""><foreignObject width="83.47917175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 197px; text-align: start;"><span class="nodeLabel"><p>assigned_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.1302080154419, 30.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(139.7447919845581, 30.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-114.7447919845581, 73.5)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-13.401041984558105, 73.5)" style=""><foreignObject width="85.53125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 199px; text-align: start;"><span class="nodeLabel"><p>assigned_by</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.1302080154419, 73.5)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(139.7447919845581, 73.5)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-127.2447919845581 -64.125 C-43.36519277355302 -64.125, 40.51440643745207 -64.125, 127.2447919845581 -64.125 M-127.2447919845581 -64.125 C-28.53221978803252 -64.125, 70.18035240849306 -64.125, 127.2447919845581 -64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-25.901041984558105 -64.125 C-25.901041984558105 -14.114755551064896, -25.901041984558105 35.89548889787021, -25.901041984558105 106.875 M-25.901041984558105 -64.125 C-25.901041984558105 -14.390242747960158, -25.901041984558105 35.34451450407968, -25.901041984558105 106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M84.6302080154419 -64.125 C84.6302080154419 -27.609299743087938, 84.6302080154419 8.906400513824124, 84.6302080154419 106.875 M84.6302080154419 -64.125 C84.6302080154419 -8.991586760879947, 84.6302080154419 46.141826478240105, 84.6302080154419 106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-127.2447919845581 -64.125 C-49.466313130977454 -64.125, 28.312165722603197 -64.125, 127.2447919845581 -64.125 M-127.2447919845581 -64.125 C-72.67880324403154 -64.125, -18.112814503504993 -64.125, 127.2447919845581 -64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-COURSES-4" transform="translate(1556.8749990463257, 1193.25)"><g style=""><path d="M-138.6302089691162 -213.75 L138.6302089691162 -213.75 L138.6302089691162 213.75 L-138.6302089691162 213.75" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-138.6302089691162 -213.75 C-30.926622224437793 -213.75, 76.77696452024063 -213.75, 138.6302089691162 -213.75 M-138.6302089691162 -213.75 C-58.72699995027813 -213.75, 21.176209068559956 -213.75, 138.6302089691162 -213.75 M138.6302089691162 -213.75 C138.6302089691162 -105.48424140611661, 138.6302089691162 2.781517187766781, 138.6302089691162 213.75 M138.6302089691162 -213.75 C138.6302089691162 -80.5894032930589, 138.6302089691162 52.57119341388221, 138.6302089691162 213.75 M138.6302089691162 213.75 C33.344641738166715 213.75, -71.94092549278278 213.75, -138.6302089691162 213.75 M138.6302089691162 213.75 C59.68606189023514 213.75, -19.258085188645936 213.75, -138.6302089691162 213.75 M-138.6302089691162 213.75 C-138.6302089691162 126.05901313927188, -138.6302089691162 38.368026278543766, -138.6302089691162 -213.75 M-138.6302089691162 213.75 C-138.6302089691162 73.7332208007416, -138.6302089691162 -66.28355839851679, -138.6302089691162 -213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.6302089691162 -171 L138.6302089691162 -171 L138.6302089691162 -128.25 L-138.6302089691162 -128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.6302089691162 -171 C-47.42226886080938 -171, 43.78567124749745 -171, 138.6302089691162 -171 M-138.6302089691162 -171 C-30.64516411038197 -171, 77.33988074835227 -171, 138.6302089691162 -171 M138.6302089691162 -171 C138.6302089691162 -156.41612121715096, 138.6302089691162 -141.8322424343019, 138.6302089691162 -128.25 M138.6302089691162 -171 C138.6302089691162 -160.1517439146272, 138.6302089691162 -149.30348782925438, 138.6302089691162 -128.25 M138.6302089691162 -128.25 C73.94241405844114 -128.25, 9.254619147766078 -128.25, -138.6302089691162 -128.25 M138.6302089691162 -128.25 C51.86226252605617 -128.25, -34.90568391700387 -128.25, -138.6302089691162 -128.25 M-138.6302089691162 -128.25 C-138.6302089691162 -143.57511997134574, -138.6302089691162 -158.90023994269148, -138.6302089691162 -171 M-138.6302089691162 -128.25 C-138.6302089691162 -142.8772115209189, -138.6302089691162 -157.5044230418378, -138.6302089691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.6302089691162 -128.25 L138.6302089691162 -128.25 L138.6302089691162 -85.5 L-138.6302089691162 -85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.6302089691162 -128.25 C-64.57606816993915 -128.25, 9.478072629237914 -128.25, 138.6302089691162 -128.25 M-138.6302089691162 -128.25 C-77.0934545890897 -128.25, -15.556700209063195 -128.25, 138.6302089691162 -128.25 M138.6302089691162 -128.25 C138.6302089691162 -119.22751051524799, 138.6302089691162 -110.20502103049597, 138.6302089691162 -85.5 M138.6302089691162 -128.25 C138.6302089691162 -117.38464927911116, 138.6302089691162 -106.5192985582223, 138.6302089691162 -85.5 M138.6302089691162 -85.5 C61.296263786899104 -85.5, -16.037681395318003 -85.5, -138.6302089691162 -85.5 M138.6302089691162 -85.5 C52.91918346069579 -85.5, -32.79184204772463 -85.5, -138.6302089691162 -85.5 M-138.6302089691162 -85.5 C-138.6302089691162 -98.33622324784682, -138.6302089691162 -111.17244649569363, -138.6302089691162 -128.25 M-138.6302089691162 -85.5 C-138.6302089691162 -102.11325546344511, -138.6302089691162 -118.72651092689021, -138.6302089691162 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.6302089691162 -85.5 L138.6302089691162 -85.5 L138.6302089691162 -42.75 L-138.6302089691162 -42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.6302089691162 -85.5 C-51.96239600464027 -85.5, 34.705416959835674 -85.5, 138.6302089691162 -85.5 M-138.6302089691162 -85.5 C-50.853381833208445 -85.5, 36.92344530269932 -85.5, 138.6302089691162 -85.5 M138.6302089691162 -85.5 C138.6302089691162 -75.13225099399924, 138.6302089691162 -64.76450198799847, 138.6302089691162 -42.75 M138.6302089691162 -85.5 C138.6302089691162 -74.00418787622436, 138.6302089691162 -62.50837575244873, 138.6302089691162 -42.75 M138.6302089691162 -42.75 C67.42254556517486 -42.75, -3.785117838766496 -42.75, -138.6302089691162 -42.75 M138.6302089691162 -42.75 C63.97551660090335 -42.75, -10.679175767309516 -42.75, -138.6302089691162 -42.75 M-138.6302089691162 -42.75 C-138.6302089691162 -52.10858778850707, -138.6302089691162 -61.467175577014146, -138.6302089691162 -85.5 M-138.6302089691162 -42.75 C-138.6302089691162 -58.91767210274625, -138.6302089691162 -75.0853442054925, -138.6302089691162 -85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.6302089691162 -42.75 L138.6302089691162 -42.75 L138.6302089691162 0 L-138.6302089691162 0" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.6302089691162 -42.75 C-42.909280146895256 -42.75, 52.8116486753257 -42.75, 138.6302089691162 -42.75 M-138.6302089691162 -42.75 C-43.526368015468265 -42.75, 51.57747293817968 -42.75, 138.6302089691162 -42.75 M138.6302089691162 -42.75 C138.6302089691162 -29.644843054719814, 138.6302089691162 -16.539686109439625, 138.6302089691162 0 M138.6302089691162 -42.75 C138.6302089691162 -29.49128128978728, 138.6302089691162 -16.232562579574562, 138.6302089691162 0 M138.6302089691162 0 C41.78563248157633 0, -55.05894400596355 0, -138.6302089691162 0 M138.6302089691162 0 C72.3070963513771 0, 5.983983733637984 0, -138.6302089691162 0 M-138.6302089691162 0 C-138.6302089691162 -15.697634320434723, -138.6302089691162 -31.395268640869446, -138.6302089691162 -42.75 M-138.6302089691162 0 C-138.6302089691162 -15.525657019421699, -138.6302089691162 -31.051314038843397, -138.6302089691162 -42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.6302089691162 0 L138.6302089691162 0 L138.6302089691162 42.75 L-138.6302089691162 42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.6302089691162 0 C-79.69419835564665 0, -20.7581877421771 0, 138.6302089691162 0 M-138.6302089691162 0 C-80.87220253336653 0, -23.11419609761687 0, 138.6302089691162 0 M138.6302089691162 0 C138.6302089691162 11.026300261000927, 138.6302089691162 22.052600522001853, 138.6302089691162 42.75 M138.6302089691162 0 C138.6302089691162 11.805898460960677, 138.6302089691162 23.611796921921353, 138.6302089691162 42.75 M138.6302089691162 42.75 C39.95101949770911 42.75, -58.72816997369799 42.75, -138.6302089691162 42.75 M138.6302089691162 42.75 C75.71480543550652 42.75, 12.799401901896815 42.75, -138.6302089691162 42.75 M-138.6302089691162 42.75 C-138.6302089691162 29.78472806200743, -138.6302089691162 16.819456124014863, -138.6302089691162 0 M-138.6302089691162 42.75 C-138.6302089691162 33.198405793986865, -138.6302089691162 23.646811587973726, -138.6302089691162 0" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.6302089691162 42.75 L138.6302089691162 42.75 L138.6302089691162 85.5 L-138.6302089691162 85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.6302089691162 42.75 C-74.1243758637137 42.75, -9.618542758311179 42.75, 138.6302089691162 42.75 M-138.6302089691162 42.75 C-34.79775597415609 42.75, 69.03469702080403 42.75, 138.6302089691162 42.75 M138.6302089691162 42.75 C138.6302089691162 55.39551090056645, 138.6302089691162 68.0410218011329, 138.6302089691162 85.5 M138.6302089691162 42.75 C138.6302089691162 52.5593359572016, 138.6302089691162 62.368671914403194, 138.6302089691162 85.5 M138.6302089691162 85.5 C65.48270738261847 85.5, -7.664794203879268 85.5, -138.6302089691162 85.5 M138.6302089691162 85.5 C55.33052078807005 85.5, -27.969167392976118 85.5, -138.6302089691162 85.5 M-138.6302089691162 85.5 C-138.6302089691162 75.92570517965535, -138.6302089691162 66.3514103593107, -138.6302089691162 42.75 M-138.6302089691162 85.5 C-138.6302089691162 68.41826430560751, -138.6302089691162 51.33652861121501, -138.6302089691162 42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.6302089691162 85.5 L138.6302089691162 85.5 L138.6302089691162 128.25 L-138.6302089691162 128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.6302089691162 85.5 C-37.906342564557036 85.5, 62.81752384000214 85.5, 138.6302089691162 85.5 M-138.6302089691162 85.5 C-57.427205513353826 85.5, 23.77579794240856 85.5, 138.6302089691162 85.5 M138.6302089691162 85.5 C138.6302089691162 96.33914278622653, 138.6302089691162 107.17828557245305, 138.6302089691162 128.25 M138.6302089691162 85.5 C138.6302089691162 95.54487791916209, 138.6302089691162 105.58975583832417, 138.6302089691162 128.25 M138.6302089691162 128.25 C47.433411652769394 128.25, -43.763385663577424 128.25, -138.6302089691162 128.25 M138.6302089691162 128.25 C47.7653588610289 128.25, -43.09949124705841 128.25, -138.6302089691162 128.25 M-138.6302089691162 128.25 C-138.6302089691162 119.61963161175689, -138.6302089691162 110.98926322351376, -138.6302089691162 85.5 M-138.6302089691162 128.25 C-138.6302089691162 117.44510680545598, -138.6302089691162 106.64021361091194, -138.6302089691162 85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-138.6302089691162 128.25 L138.6302089691162 128.25 L138.6302089691162 171 L-138.6302089691162 171" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-138.6302089691162 128.25 C-65.61373175926668 128.25, 7.402745450582842 128.25, 138.6302089691162 128.25 M-138.6302089691162 128.25 C-56.098230578321306 128.25, 26.433747812473598 128.25, 138.6302089691162 128.25 M138.6302089691162 128.25 C138.6302089691162 137.1477550150153, 138.6302089691162 146.04551003003058, 138.6302089691162 171 M138.6302089691162 128.25 C138.6302089691162 137.98953170698616, 138.6302089691162 147.7290634139723, 138.6302089691162 171 M138.6302089691162 171 C65.97683929637724 171, -6.67653037636174 171, -138.6302089691162 171 M138.6302089691162 171 C60.69460186876478 171, -17.241005231586655 171, -138.6302089691162 171 M-138.6302089691162 171 C-138.6302089691162 156.88633180971672, -138.6302089691162 142.77266361943344, -138.6302089691162 128.25 M-138.6302089691162 171 C-138.6302089691162 160.58448037065853, -138.6302089691162 150.16896074131702, -138.6302089691162 128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-138.6302089691162 171 L138.6302089691162 171 L138.6302089691162 213.75 L-138.6302089691162 213.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-138.6302089691162 171 C-63.66213313993761 171, 11.305942689240993 171, 138.6302089691162 171 M-138.6302089691162 171 C-81.06799266916227 171, -23.50577636920832 171, 138.6302089691162 171 M138.6302089691162 171 C138.6302089691162 187.03746273450074, 138.6302089691162 203.07492546900147, 138.6302089691162 213.75 M138.6302089691162 171 C138.6302089691162 186.53952980062525, 138.6302089691162 202.07905960125052, 138.6302089691162 213.75 M138.6302089691162 213.75 C74.72704330509919 213.75, 10.823877641082177 213.75, -138.6302089691162 213.75 M138.6302089691162 213.75 C74.64125464023826 213.75, 10.652300311360293 213.75, -138.6302089691162 213.75 M-138.6302089691162 213.75 C-138.6302089691162 197.2292168640716, -138.6302089691162 180.70843372814318, -138.6302089691162 171 M-138.6302089691162 213.75 C-138.6302089691162 200.34926792343938, -138.6302089691162 186.94853584687877, -138.6302089691162 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-32, -204.375)" style=""><foreignObject width="64" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 168px; text-align: start;"><span class="nodeLabel"><p>COURSES</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, -161.625)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, -161.625)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, -161.625)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, -161.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, -118.875)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, -118.875)" style=""><foreignObject width="66.90625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 175px; text-align: start;"><span class="nodeLabel"><p>school_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, -118.875)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, -118.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, -76.125)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, -76.125)" style=""><foreignObject width="39.15625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 142px; text-align: start;"><span class="nodeLabel"><p>name</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, -33.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, -33.375)" style=""><foreignObject width="34.15625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 137px; text-align: start;"><span class="nodeLabel"><p>code</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, 9.375)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, 9.375)" style=""><foreignObject width="79.48958587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>lecturer_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, 9.375)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, 52.125)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, 52.125)" style=""><foreignObject width="84.625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: start;"><span class="nodeLabel"><p>department</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, 94.875)" style=""><foreignObject width="19.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 123px; text-align: start;"><span class="nodeLabel"><p>int</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, 94.875)" style=""><foreignObject width="49.16666793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 156px; text-align: start;"><span class="nodeLabel"><p>credits</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, 137.625)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, 137.625)" style=""><foreignObject width="107.78125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 216px; text-align: start;"><span class="nodeLabel"><p>academic_year</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-126.13020896911621, 180.375)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-24.78645896911621, 180.375)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(107.99479103088379, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(151.1302089691162, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-138.6302089691162 -171 C-63.06876147706906 -171, 12.49268601497809 -171, 138.6302089691162 -171 M-138.6302089691162 -171 C-53.38477419638829 -171, 31.860660576339626 -171, 138.6302089691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-37.28645896911621 -171 C-37.28645896911621 -56.66700990148705, -37.28645896911621 57.6659801970259, -37.28645896911621 213.75 M-37.28645896911621 -171 C-37.28645896911621 -47.75152916182256, -37.28645896911621 75.49694167635488, -37.28645896911621 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M95.49479103088379 -171 C95.49479103088379 -79.79603882818472, 95.49479103088379 11.40792234363056, 95.49479103088379 213.75 M95.49479103088379 -171 C95.49479103088379 -58.13851671820194, 95.49479103088379 54.72296656359612, 95.49479103088379 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-138.6302089691162 -171 C-44.54121148643817 -171, 49.54778599623987 -171, 138.6302089691162 -171 M-138.6302089691162 -171 C-57.1667387522873 -171, 24.296731464541608 -171, 138.6302089691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-COURSE_STUDENTS-5" transform="translate(430.37500381469727, 1721.75)"><g style=""><path d="M-125.7291669845581 -106.875 L125.7291669845581 -106.875 L125.7291669845581 106.875 L-125.7291669845581 106.875" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-125.7291669845581 -106.875 C-60.812434470731645 -106.875, 4.104298043094815 -106.875, 125.7291669845581 -106.875 M-125.7291669845581 -106.875 C-61.288281988131104 -106.875, 3.1526030082958982 -106.875, 125.7291669845581 -106.875 M125.7291669845581 -106.875 C125.7291669845581 -58.098852405901724, 125.7291669845581 -9.322704811803447, 125.7291669845581 106.875 M125.7291669845581 -106.875 C125.7291669845581 -61.57881846461699, 125.7291669845581 -16.282636929233973, 125.7291669845581 106.875 M125.7291669845581 106.875 C51.41832298179476 106.875, -22.892521020968587 106.875, -125.7291669845581 106.875 M125.7291669845581 106.875 C63.07389275216047 106.875, 0.4186185197628305 106.875, -125.7291669845581 106.875 M-125.7291669845581 106.875 C-125.7291669845581 35.3473266217749, -125.7291669845581 -36.180346756450206, -125.7291669845581 -106.875 M-125.7291669845581 106.875 C-125.7291669845581 34.384103017433674, -125.7291669845581 -38.10679396513265, -125.7291669845581 -106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-125.7291669845581 -64.125 L125.7291669845581 -64.125 L125.7291669845581 -21.375 L-125.7291669845581 -21.375" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-125.7291669845581 -64.125 C-37.32465445457835 -64.125, 51.079858075401404 -64.125, 125.7291669845581 -64.125 M-125.7291669845581 -64.125 C-71.2641651360967 -64.125, -16.79916328763528 -64.125, 125.7291669845581 -64.125 M125.7291669845581 -64.125 C125.7291669845581 -48.93457785923379, 125.7291669845581 -33.74415571846758, 125.7291669845581 -21.375 M125.7291669845581 -64.125 C125.7291669845581 -48.40804983681156, 125.7291669845581 -32.69109967362312, 125.7291669845581 -21.375 M125.7291669845581 -21.375 C67.36821599929084 -21.375, 9.00726501402356 -21.375, -125.7291669845581 -21.375 M125.7291669845581 -21.375 C41.92747804253834 -21.375, -41.874210899481426 -21.375, -125.7291669845581 -21.375 M-125.7291669845581 -21.375 C-125.7291669845581 -32.046814807780095, -125.7291669845581 -42.71862961556019, -125.7291669845581 -64.125 M-125.7291669845581 -21.375 C-125.7291669845581 -35.79091147708046, -125.7291669845581 -50.20682295416093, -125.7291669845581 -64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-125.7291669845581 -21.375 L125.7291669845581 -21.375 L125.7291669845581 21.375 L-125.7291669845581 21.375" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-125.7291669845581 -21.375 C-48.78562258595518 -21.375, 28.157921812647743 -21.375, 125.7291669845581 -21.375 M-125.7291669845581 -21.375 C-40.90940893997633 -21.375, 43.910349104605444 -21.375, 125.7291669845581 -21.375 M125.7291669845581 -21.375 C125.7291669845581 -9.333178513961625, 125.7291669845581 2.708642972076749, 125.7291669845581 21.375 M125.7291669845581 -21.375 C125.7291669845581 -8.976858268754599, 125.7291669845581 3.421283462490802, 125.7291669845581 21.375 M125.7291669845581 21.375 C44.45423329610962 21.375, -36.820700392338864 21.375, -125.7291669845581 21.375 M125.7291669845581 21.375 C71.1209576634786 21.375, 16.51274834239912 21.375, -125.7291669845581 21.375 M-125.7291669845581 21.375 C-125.7291669845581 6.376273243673788, -125.7291669845581 -8.622453512652424, -125.7291669845581 -21.375 M-125.7291669845581 21.375 C-125.7291669845581 8.35553399296924, -125.7291669845581 -4.663932014061519, -125.7291669845581 -21.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-125.7291669845581 21.375 L125.7291669845581 21.375 L125.7291669845581 64.125 L-125.7291669845581 64.125" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-125.7291669845581 21.375 C-33.029660067285334 21.375, 59.66984684998744 21.375, 125.7291669845581 21.375 M-125.7291669845581 21.375 C-68.05722108117286 21.375, -10.385275177787591 21.375, 125.7291669845581 21.375 M125.7291669845581 21.375 C125.7291669845581 38.14439194468612, 125.7291669845581 54.91378388937225, 125.7291669845581 64.125 M125.7291669845581 21.375 C125.7291669845581 35.05651336838596, 125.7291669845581 48.738026736771914, 125.7291669845581 64.125 M125.7291669845581 64.125 C41.77625613954571 64.125, -42.17665470546669 64.125, -125.7291669845581 64.125 M125.7291669845581 64.125 C61.561723873543116 64.125, -2.605719237471874 64.125, -125.7291669845581 64.125 M-125.7291669845581 64.125 C-125.7291669845581 47.2060859498292, -125.7291669845581 30.287171899658404, -125.7291669845581 21.375 M-125.7291669845581 64.125 C-125.7291669845581 47.72369292313985, -125.7291669845581 31.3223858462797, -125.7291669845581 21.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-125.7291669845581 64.125 L125.7291669845581 64.125 L125.7291669845581 106.875 L-125.7291669845581 106.875" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-125.7291669845581 64.125 C-61.71543612483946 64.125, 2.2982947348791924 64.125, 125.7291669845581 64.125 M-125.7291669845581 64.125 C-30.086824378347785 64.125, 65.55551822786254 64.125, 125.7291669845581 64.125 M125.7291669845581 64.125 C125.7291669845581 72.71090834468042, 125.7291669845581 81.29681668936085, 125.7291669845581 106.875 M125.7291669845581 64.125 C125.7291669845581 77.22298582438255, 125.7291669845581 90.32097164876511, 125.7291669845581 106.875 M125.7291669845581 106.875 C41.81606083584845 106.875, -42.09704531286121 106.875, -125.7291669845581 106.875 M125.7291669845581 106.875 C51.984770203255806 106.875, -21.759626578046493 106.875, -125.7291669845581 106.875 M-125.7291669845581 106.875 C-125.7291669845581 92.31045282564166, -125.7291669845581 77.74590565128332, -125.7291669845581 64.125 M-125.7291669845581 106.875 C-125.7291669845581 97.71790655962751, -125.7291669845581 88.56081311925502, -125.7291669845581 64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-68.81771087646484, -97.5)" style=""><foreignObject width="137.6354217529297" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 244px; text-align: start;"><span class="nodeLabel"><p>COURSE_STUDENTS</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.2291669845581, -54.75)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-11.885416984558105, -54.75)" style=""><foreignObject width="68.54167175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: start;"><span class="nodeLabel"><p>course_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.6145830154419, -54.75)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.2291669845581, -54.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.2291669845581, -12)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-11.885416984558105, -12)" style=""><foreignObject width="76.15625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>student_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.6145830154419, -12)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.2291669845581, -12)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.2291669845581, 30.75)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-11.885416984558105, 30.75)" style=""><foreignObject width="82.5" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 192px; text-align: start;"><span class="nodeLabel"><p>enrolled_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.6145830154419, 30.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.2291669845581, 30.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.2291669845581, 73.5)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-11.885416984558105, 73.5)" style=""><foreignObject width="42.79166793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;"><span class="nodeLabel"><p>status</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.6145830154419, 73.5)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.2291669845581, 73.5)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-125.7291669845581 -64.125 C-26.79305398415211 -64.125, 72.14305901625389 -64.125, 125.7291669845581 -64.125 M-125.7291669845581 -64.125 C-28.031325736253464 -64.125, 69.66651551205118 -64.125, 125.7291669845581 -64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-24.385416984558105 -64.125 C-24.385416984558105 -8.952850234255585, -24.385416984558105 46.21929953148883, -24.385416984558105 106.875 M-24.385416984558105 -64.125 C-24.385416984558105 -28.10972881928454, -24.385416984558105 7.905542361430918, -24.385416984558105 106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M83.1145830154419 -64.125 C83.1145830154419 -2.55913242763647, 83.1145830154419 59.00673514472706, 83.1145830154419 106.875 M83.1145830154419 -64.125 C83.1145830154419 -12.574787304288684, 83.1145830154419 38.97542539142263, 83.1145830154419 106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-125.7291669845581 -64.125 C-49.55449247765718 -64.125, 26.62018202924375 -64.125, 125.7291669845581 -64.125 M-125.7291669845581 -64.125 C-69.87110388703569 -64.125, -14.01304078951327 -64.125, 125.7291669845581 -64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-MARKS-6" transform="translate(1529.611982345581, 1721.75)"><g style=""><path d="M-144.7708339691162 -213.75 L144.7708339691162 -213.75 L144.7708339691162 213.75 L-144.7708339691162 213.75" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-144.7708339691162 -213.75 C-41.393122672733284 -213.75, 61.98458862364964 -213.75, 144.7708339691162 -213.75 M-144.7708339691162 -213.75 C-30.0616118529513 -213.75, 84.64761026321361 -213.75, 144.7708339691162 -213.75 M144.7708339691162 -213.75 C144.7708339691162 -51.76489950815076, 144.7708339691162 110.22020098369848, 144.7708339691162 213.75 M144.7708339691162 -213.75 C144.7708339691162 -99.29768162956321, 144.7708339691162 15.15463674087357, 144.7708339691162 213.75 M144.7708339691162 213.75 C69.40373305440085 213.75, -5.96336786031452 213.75, -144.7708339691162 213.75 M144.7708339691162 213.75 C43.53681378684344 213.75, -57.69720639542933 213.75, -144.7708339691162 213.75 M-144.7708339691162 213.75 C-144.7708339691162 63.00558490154697, -144.7708339691162 -87.73883019690606, -144.7708339691162 -213.75 M-144.7708339691162 213.75 C-144.7708339691162 67.12460211539371, -144.7708339691162 -79.50079576921257, -144.7708339691162 -213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-144.7708339691162 -171 L144.7708339691162 -171 L144.7708339691162 -128.25 L-144.7708339691162 -128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-144.7708339691162 -171 C-73.87037976987106 -171, -2.969925570625918 -171, 144.7708339691162 -171 M-144.7708339691162 -171 C-71.2540296573732 -171, 2.262774654369821 -171, 144.7708339691162 -171 M144.7708339691162 -171 C144.7708339691162 -158.72340592717325, 144.7708339691162 -146.4468118543465, 144.7708339691162 -128.25 M144.7708339691162 -171 C144.7708339691162 -154.31209572784007, 144.7708339691162 -137.62419145568015, 144.7708339691162 -128.25 M144.7708339691162 -128.25 C64.48999315999303 -128.25, -15.790847649130143 -128.25, -144.7708339691162 -128.25 M144.7708339691162 -128.25 C41.16749682643591 -128.25, -62.435840316244395 -128.25, -144.7708339691162 -128.25 M-144.7708339691162 -128.25 C-144.7708339691162 -143.53055974801833, -144.7708339691162 -158.81111949603667, -144.7708339691162 -171 M-144.7708339691162 -128.25 C-144.7708339691162 -144.275139846836, -144.7708339691162 -160.30027969367202, -144.7708339691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-144.7708339691162 -128.25 L144.7708339691162 -128.25 L144.7708339691162 -85.5 L-144.7708339691162 -85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-144.7708339691162 -128.25 C-73.37510027526037 -128.25, -1.9793665814045198 -128.25, 144.7708339691162 -128.25 M-144.7708339691162 -128.25 C-83.79677714128329 -128.25, -22.82272031345036 -128.25, 144.7708339691162 -128.25 M144.7708339691162 -128.25 C144.7708339691162 -113.48255942762609, 144.7708339691162 -98.71511885525219, 144.7708339691162 -85.5 M144.7708339691162 -128.25 C144.7708339691162 -119.4462739267041, 144.7708339691162 -110.6425478534082, 144.7708339691162 -85.5 M144.7708339691162 -85.5 C69.31992917788045 -85.5, -6.130975613355304 -85.5, -144.7708339691162 -85.5 M144.7708339691162 -85.5 C85.87573058822375 -85.5, 26.980627207331295 -85.5, -144.7708339691162 -85.5 M-144.7708339691162 -85.5 C-144.7708339691162 -102.3476769486606, -144.7708339691162 -119.19535389732121, -144.7708339691162 -128.25 M-144.7708339691162 -85.5 C-144.7708339691162 -95.1505088719197, -144.7708339691162 -104.8010177438394, -144.7708339691162 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-144.7708339691162 -85.5 L144.7708339691162 -85.5 L144.7708339691162 -42.75 L-144.7708339691162 -42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-144.7708339691162 -85.5 C-59.55041849772259 -85.5, 25.669996973671033 -85.5, 144.7708339691162 -85.5 M-144.7708339691162 -85.5 C-71.13086523308925 -85.5, 2.5091035029377053 -85.5, 144.7708339691162 -85.5 M144.7708339691162 -85.5 C144.7708339691162 -70.52254067485326, 144.7708339691162 -55.54508134970652, 144.7708339691162 -42.75 M144.7708339691162 -85.5 C144.7708339691162 -70.00631229698445, 144.7708339691162 -54.51262459396889, 144.7708339691162 -42.75 M144.7708339691162 -42.75 C72.13421093741186 -42.75, -0.502412094292481 -42.75, -144.7708339691162 -42.75 M144.7708339691162 -42.75 C47.46863843905322 -42.75, -49.833557091009766 -42.75, -144.7708339691162 -42.75 M-144.7708339691162 -42.75 C-144.7708339691162 -57.249048846067055, -144.7708339691162 -71.74809769213411, -144.7708339691162 -85.5 M-144.7708339691162 -42.75 C-144.7708339691162 -51.375983907964056, -144.7708339691162 -60.00196781592811, -144.7708339691162 -85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-144.7708339691162 -42.75 L144.7708339691162 -42.75 L144.7708339691162 0 L-144.7708339691162 0" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-144.7708339691162 -42.75 C-75.94712609740955 -42.75, -7.123418225702892 -42.75, 144.7708339691162 -42.75 M-144.7708339691162 -42.75 C-38.659016693976156 -42.75, 67.4528005811639 -42.75, 144.7708339691162 -42.75 M144.7708339691162 -42.75 C144.7708339691162 -25.951103576823055, 144.7708339691162 -9.15220715364611, 144.7708339691162 0 M144.7708339691162 -42.75 C144.7708339691162 -25.7534378839557, 144.7708339691162 -8.756875767911403, 144.7708339691162 0 M144.7708339691162 0 C34.7913398586127 0, -75.18815425189081 0, -144.7708339691162 0 M144.7708339691162 0 C48.00134631738953 0, -48.768141334337145 0, -144.7708339691162 0 M-144.7708339691162 0 C-144.7708339691162 -13.892201353840223, -144.7708339691162 -27.784402707680446, -144.7708339691162 -42.75 M-144.7708339691162 0 C-144.7708339691162 -11.944498877521019, -144.7708339691162 -23.888997755042038, -144.7708339691162 -42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-144.7708339691162 0 L144.7708339691162 0 L144.7708339691162 42.75 L-144.7708339691162 42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-144.7708339691162 0 C-36.04474284473578 0, 72.68134827964465 0, 144.7708339691162 0 M-144.7708339691162 0 C-52.62195090619848 0, 39.526932156719255 0, 144.7708339691162 0 M144.7708339691162 0 C144.7708339691162 11.623040725105382, 144.7708339691162 23.246081450210763, 144.7708339691162 42.75 M144.7708339691162 0 C144.7708339691162 8.650942327978363, 144.7708339691162 17.301884655956727, 144.7708339691162 42.75 M144.7708339691162 42.75 C67.60189867423918 42.75, -9.56703662063785 42.75, -144.7708339691162 42.75 M144.7708339691162 42.75 C48.16133362723838 42.75, -48.448166714639456 42.75, -144.7708339691162 42.75 M-144.7708339691162 42.75 C-144.7708339691162 32.132295710686215, -144.7708339691162 21.514591421372426, -144.7708339691162 0 M-144.7708339691162 42.75 C-144.7708339691162 33.96362892439625, -144.7708339691162 25.177257848792497, -144.7708339691162 0" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-144.7708339691162 42.75 L144.7708339691162 42.75 L144.7708339691162 85.5 L-144.7708339691162 85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-144.7708339691162 42.75 C-49.01100092005734 42.75, 46.74883212900153 42.75, 144.7708339691162 42.75 M-144.7708339691162 42.75 C-58.45344152678234 42.75, 27.863950915551527 42.75, 144.7708339691162 42.75 M144.7708339691162 42.75 C144.7708339691162 56.797141263073584, 144.7708339691162 70.84428252614717, 144.7708339691162 85.5 M144.7708339691162 42.75 C144.7708339691162 54.69959647887003, 144.7708339691162 66.64919295774006, 144.7708339691162 85.5 M144.7708339691162 85.5 C82.755105326842 85.5, 20.739376684567773 85.5, -144.7708339691162 85.5 M144.7708339691162 85.5 C55.14290191665954 85.5, -34.48503013579713 85.5, -144.7708339691162 85.5 M-144.7708339691162 85.5 C-144.7708339691162 74.61019722352108, -144.7708339691162 63.72039444704215, -144.7708339691162 42.75 M-144.7708339691162 85.5 C-144.7708339691162 75.79644798737075, -144.7708339691162 66.09289597474151, -144.7708339691162 42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-144.7708339691162 85.5 L144.7708339691162 85.5 L144.7708339691162 128.25 L-144.7708339691162 128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-144.7708339691162 85.5 C-55.895430562043956 85.5, 32.9799728450283 85.5, 144.7708339691162 85.5 M-144.7708339691162 85.5 C-68.82905081887226 85.5, 7.112732331371689 85.5, 144.7708339691162 85.5 M144.7708339691162 85.5 C144.7708339691162 101.27556986384802, 144.7708339691162 117.05113972769604, 144.7708339691162 128.25 M144.7708339691162 85.5 C144.7708339691162 95.45278472846815, 144.7708339691162 105.40556945693628, 144.7708339691162 128.25 M144.7708339691162 128.25 C82.25343973237716 128.25, 19.736045495638123 128.25, -144.7708339691162 128.25 M144.7708339691162 128.25 C69.1914887612143 128.25, -6.387856446687607 128.25, -144.7708339691162 128.25 M-144.7708339691162 128.25 C-144.7708339691162 115.19903270942804, -144.7708339691162 102.14806541885608, -144.7708339691162 85.5 M-144.7708339691162 128.25 C-144.7708339691162 114.20468640540611, -144.7708339691162 100.15937281081221, -144.7708339691162 85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-144.7708339691162 128.25 L144.7708339691162 128.25 L144.7708339691162 171 L-144.7708339691162 171" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-144.7708339691162 128.25 C-53.185910803260356 128.25, 38.3990123625955 128.25, 144.7708339691162 128.25 M-144.7708339691162 128.25 C-65.42437672101153 128.25, 13.922080527093158 128.25, 144.7708339691162 128.25 M144.7708339691162 128.25 C144.7708339691162 138.63833182063638, 144.7708339691162 149.02666364127276, 144.7708339691162 171 M144.7708339691162 128.25 C144.7708339691162 138.24786509419343, 144.7708339691162 148.24573018838686, 144.7708339691162 171 M144.7708339691162 171 C79.83370355328321 171, 14.896573137450218 171, -144.7708339691162 171 M144.7708339691162 171 C86.00323558151872 171, 27.235637193921235 171, -144.7708339691162 171 M-144.7708339691162 171 C-144.7708339691162 154.53700902677622, -144.7708339691162 138.0740180535524, -144.7708339691162 128.25 M-144.7708339691162 171 C-144.7708339691162 156.91494591864637, -144.7708339691162 142.82989183729273, -144.7708339691162 128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-144.7708339691162 171 L144.7708339691162 171 L144.7708339691162 213.75 L-144.7708339691162 213.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-144.7708339691162 171 C-61.3131233472744 171, 22.144587274567414 171, 144.7708339691162 171 M-144.7708339691162 171 C-32.26928746629035 171, 80.23225903653551 171, 144.7708339691162 171 M144.7708339691162 171 C144.7708339691162 186.92040411326005, 144.7708339691162 202.8408082265201, 144.7708339691162 213.75 M144.7708339691162 171 C144.7708339691162 181.3897499472318, 144.7708339691162 191.77949989446358, 144.7708339691162 213.75 M144.7708339691162 213.75 C72.0335722565609 213.75, -0.7036894559944074 213.75, -144.7708339691162 213.75 M144.7708339691162 213.75 C74.936775990196 213.75, 5.102718011275783 213.75, -144.7708339691162 213.75 M-144.7708339691162 213.75 C-144.7708339691162 201.68848724360436, -144.7708339691162 189.6269744872087, -144.7708339691162 171 M-144.7708339691162 213.75 C-144.7708339691162 197.1012525012339, -144.7708339691162 180.45250500246777, -144.7708339691162 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-23.50520896911621, -204.375)" style=""><foreignObject width="47.01041793823242" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 152px; text-align: start;"><span class="nodeLabel"><p>MARKS</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, -161.625)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, -161.625)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, -161.625)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, -161.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, -118.875)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, -118.875)" style=""><foreignObject width="76.15625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>student_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, -118.875)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, -118.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, -76.125)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, -76.125)" style=""><foreignObject width="68.54167175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: start;"><span class="nodeLabel"><p>course_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, -76.125)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, -33.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, -33.375)" style=""><foreignObject width="120.0625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 237px; text-align: start;"><span class="nodeLabel"><p>assignment_type</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, 9.375)" style=""><foreignObject width="56.53125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 162px; text-align: start;"><span class="nodeLabel"><p>decimal</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, 9.375)" style=""><foreignObject width="37.9375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 143px; text-align: start;"><span class="nodeLabel"><p>score</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, 52.125)" style=""><foreignObject width="56.53125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 162px; text-align: start;"><span class="nodeLabel"><p>decimal</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, 52.125)" style=""><foreignObject width="76.03125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: start;"><span class="nodeLabel"><p>max_score</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, 94.875)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, 94.875)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, 137.625)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, 137.625)" style=""><foreignObject width="82.10417175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: start;"><span class="nodeLabel"><p>updated_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-132.2708339691162, 180.375)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-30.92708396911621, 180.375)" style=""><foreignObject width="81.59375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 190px; text-align: start;"><span class="nodeLabel"><p>entered_by</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(114.13541603088379, 180.375)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(157.2708339691162, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-144.7708339691162 -171 C-60.29797916741808 -171, 24.174875634280056 -171, 144.7708339691162 -171 M-144.7708339691162 -171 C-49.21732616768412 -171, 46.33618163374797 -171, 144.7708339691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-43.42708396911621 -171 C-43.42708396911621 -60.352494472014925, -43.42708396911621 50.29501105597015, -43.42708396911621 213.75 M-43.42708396911621 -171 C-43.42708396911621 -26.275342013782023, -43.42708396911621 118.44931597243595, -43.42708396911621 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M101.63541603088379 -171 C101.63541603088379 -75.22846935943461, 101.63541603088379 20.543061281130775, 101.63541603088379 213.75 M101.63541603088379 -171 C101.63541603088379 -39.20150475560325, 101.63541603088379 92.5969904887935, 101.63541603088379 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-144.7708339691162 -171 C-65.53807829121354 -171, 13.694677386689136 -171, 144.7708339691162 -171 M-144.7708339691162 -171 C-75.99675735741732 -171, -7.222680745718435 -171, 144.7708339691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-MATERIALS-7" transform="translate(1944.6848888397217, 1721.75)"><g style=""><path d="M-130.3020839691162 -192.375 L130.3020839691162 -192.375 L130.3020839691162 192.375 L-130.3020839691162 192.375" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-130.3020839691162 -192.375 C-35.60641738427111 -192.375, 59.08924920057399 -192.375, 130.3020839691162 -192.375 M-130.3020839691162 -192.375 C-37.27321475953801 -192.375, 55.7556544500402 -192.375, 130.3020839691162 -192.375 M130.3020839691162 -192.375 C130.3020839691162 -96.62039526723277, 130.3020839691162 -0.8657905344655319, 130.3020839691162 192.375 M130.3020839691162 -192.375 C130.3020839691162 -99.46506186347678, 130.3020839691162 -6.555123726953553, 130.3020839691162 192.375 M130.3020839691162 192.375 C59.324888397353575 192.375, -11.65230717440906 192.375, -130.3020839691162 192.375 M130.3020839691162 192.375 C38.69567326839227 192.375, -52.91073743233167 192.375, -130.3020839691162 192.375 M-130.3020839691162 192.375 C-130.3020839691162 111.0381926113779, -130.3020839691162 29.70138522275579, -130.3020839691162 -192.375 M-130.3020839691162 192.375 C-130.3020839691162 71.50652442524907, -130.3020839691162 -49.36195114950186, -130.3020839691162 -192.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-130.3020839691162 -149.625 L130.3020839691162 -149.625 L130.3020839691162 -106.875 L-130.3020839691162 -106.875" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-130.3020839691162 -149.625 C-74.35458436826539 -149.625, -18.40708476741456 -149.625, 130.3020839691162 -149.625 M-130.3020839691162 -149.625 C-44.76703922064057 -149.625, 40.76800552783507 -149.625, 130.3020839691162 -149.625 M130.3020839691162 -149.625 C130.3020839691162 -136.02407099378044, 130.3020839691162 -122.42314198756088, 130.3020839691162 -106.875 M130.3020839691162 -149.625 C130.3020839691162 -140.90170311448367, 130.3020839691162 -132.1784062289673, 130.3020839691162 -106.875 M130.3020839691162 -106.875 C49.93630240009267 -106.875, -30.429479168930868 -106.875, -130.3020839691162 -106.875 M130.3020839691162 -106.875 C56.279979849236 -106.875, -17.74212427064421 -106.875, -130.3020839691162 -106.875 M-130.3020839691162 -106.875 C-130.3020839691162 -116.61264667163272, -130.3020839691162 -126.35029334326543, -130.3020839691162 -149.625 M-130.3020839691162 -106.875 C-130.3020839691162 -117.28731081919467, -130.3020839691162 -127.69962163838935, -130.3020839691162 -149.625" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-130.3020839691162 -106.875 L130.3020839691162 -106.875 L130.3020839691162 -64.125 L-130.3020839691162 -64.125" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-130.3020839691162 -106.875 C-33.17336972723798 -106.875, 63.955344514640245 -106.875, 130.3020839691162 -106.875 M-130.3020839691162 -106.875 C-35.37583890749421 -106.875, 59.550406154127785 -106.875, 130.3020839691162 -106.875 M130.3020839691162 -106.875 C130.3020839691162 -92.92314978296538, 130.3020839691162 -78.97129956593076, 130.3020839691162 -64.125 M130.3020839691162 -106.875 C130.3020839691162 -95.04551447025517, 130.3020839691162 -83.21602894051034, 130.3020839691162 -64.125 M130.3020839691162 -64.125 C30.238764199162546 -64.125, -69.82455557079112 -64.125, -130.3020839691162 -64.125 M130.3020839691162 -64.125 C27.704322897598374 -64.125, -74.89343817391946 -64.125, -130.3020839691162 -64.125 M-130.3020839691162 -64.125 C-130.3020839691162 -78.28228867350299, -130.3020839691162 -92.439577347006, -130.3020839691162 -106.875 M-130.3020839691162 -64.125 C-130.3020839691162 -74.20359145885465, -130.3020839691162 -84.28218291770928, -130.3020839691162 -106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-130.3020839691162 -64.125 L130.3020839691162 -64.125 L130.3020839691162 -21.375 L-130.3020839691162 -21.375" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-130.3020839691162 -64.125 C-69.16772064387216 -64.125, -8.033357318628106 -64.125, 130.3020839691162 -64.125 M-130.3020839691162 -64.125 C-26.595328890786135 -64.125, 77.11142618754394 -64.125, 130.3020839691162 -64.125 M130.3020839691162 -64.125 C130.3020839691162 -55.09754935849381, 130.3020839691162 -46.07009871698761, 130.3020839691162 -21.375 M130.3020839691162 -64.125 C130.3020839691162 -49.59174990092522, 130.3020839691162 -35.05849980185045, 130.3020839691162 -21.375 M130.3020839691162 -21.375 C40.03119352397614 -21.375, -50.239696921163926 -21.375, -130.3020839691162 -21.375 M130.3020839691162 -21.375 C68.77154620973016 -21.375, 7.241008450344097 -21.375, -130.3020839691162 -21.375 M-130.3020839691162 -21.375 C-130.3020839691162 -33.74439400956658, -130.3020839691162 -46.113788019133146, -130.3020839691162 -64.125 M-130.3020839691162 -21.375 C-130.3020839691162 -37.07519565041198, -130.3020839691162 -52.775391300823955, -130.3020839691162 -64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-130.3020839691162 -21.375 L130.3020839691162 -21.375 L130.3020839691162 21.375 L-130.3020839691162 21.375" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-130.3020839691162 -21.375 C-70.26925247802134 -21.375, -10.23642098692649 -21.375, 130.3020839691162 -21.375 M-130.3020839691162 -21.375 C-71.58633735086234 -21.375, -12.870590732608463 -21.375, 130.3020839691162 -21.375 M130.3020839691162 -21.375 C130.3020839691162 -8.38240936327079, 130.3020839691162 4.610181273458419, 130.3020839691162 21.375 M130.3020839691162 -21.375 C130.3020839691162 -10.548614033900254, 130.3020839691162 0.27777193219949226, 130.3020839691162 21.375 M130.3020839691162 21.375 C54.05437311689714 21.375, -22.193337735321933 21.375, -130.3020839691162 21.375 M130.3020839691162 21.375 C55.687567436448276 21.375, -18.92694909621966 21.375, -130.3020839691162 21.375 M-130.3020839691162 21.375 C-130.3020839691162 6.39283687102159, -130.3020839691162 -8.58932625795682, -130.3020839691162 -21.375 M-130.3020839691162 21.375 C-130.3020839691162 6.355022687478778, -130.3020839691162 -8.664954625042444, -130.3020839691162 -21.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-130.3020839691162 21.375 L130.3020839691162 21.375 L130.3020839691162 64.125 L-130.3020839691162 64.125" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-130.3020839691162 21.375 C-38.43449928604967 21.375, 53.43308539701687 21.375, 130.3020839691162 21.375 M-130.3020839691162 21.375 C-74.12208619154352 21.375, -17.942088413970822 21.375, 130.3020839691162 21.375 M130.3020839691162 21.375 C130.3020839691162 37.13109325381354, 130.3020839691162 52.88718650762708, 130.3020839691162 64.125 M130.3020839691162 21.375 C130.3020839691162 32.03416778799798, 130.3020839691162 42.69333557599596, 130.3020839691162 64.125 M130.3020839691162 64.125 C62.78014146407719 64.125, -4.741801040961832 64.125, -130.3020839691162 64.125 M130.3020839691162 64.125 C33.945605171620386 64.125, -62.41087362587544 64.125, -130.3020839691162 64.125 M-130.3020839691162 64.125 C-130.3020839691162 47.74528145044842, -130.3020839691162 31.36556290089684, -130.3020839691162 21.375 M-130.3020839691162 64.125 C-130.3020839691162 49.65356959358259, -130.3020839691162 35.18213918716518, -130.3020839691162 21.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-130.3020839691162 64.125 L130.3020839691162 64.125 L130.3020839691162 106.875 L-130.3020839691162 106.875" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-130.3020839691162 64.125 C-55.3693305064278 64.125, 19.56342295626061 64.125, 130.3020839691162 64.125 M-130.3020839691162 64.125 C-52.370448068297335 64.125, 25.56118783252154 64.125, 130.3020839691162 64.125 M130.3020839691162 64.125 C130.3020839691162 73.39117189618598, 130.3020839691162 82.65734379237196, 130.3020839691162 106.875 M130.3020839691162 64.125 C130.3020839691162 79.34892980757893, 130.3020839691162 94.57285961515785, 130.3020839691162 106.875 M130.3020839691162 106.875 C56.55200471023443 106.875, -17.198074548647355 106.875, -130.3020839691162 106.875 M130.3020839691162 106.875 C57.49239050325326 106.875, -15.317302962609688 106.875, -130.3020839691162 106.875 M-130.3020839691162 106.875 C-130.3020839691162 91.72732850827826, -130.3020839691162 76.57965701655652, -130.3020839691162 64.125 M-130.3020839691162 106.875 C-130.3020839691162 95.21126408769538, -130.3020839691162 83.54752817539077, -130.3020839691162 64.125" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-130.3020839691162 106.875 L130.3020839691162 106.875 L130.3020839691162 149.625 L-130.3020839691162 149.625" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-130.3020839691162 106.875 C-46.51188207544975 106.875, 37.278319818216715 106.875, 130.3020839691162 106.875 M-130.3020839691162 106.875 C-75.76961631507311 106.875, -21.23714866102999 106.875, 130.3020839691162 106.875 M130.3020839691162 106.875 C130.3020839691162 119.95644120517944, 130.3020839691162 133.0378824103589, 130.3020839691162 149.625 M130.3020839691162 106.875 C130.3020839691162 121.59280130135659, 130.3020839691162 136.31060260271317, 130.3020839691162 149.625 M130.3020839691162 149.625 C71.03562410010176 149.625, 11.769164231087288 149.625, -130.3020839691162 149.625 M130.3020839691162 149.625 C43.78059446468468 149.625, -42.740895039746846 149.625, -130.3020839691162 149.625 M-130.3020839691162 149.625 C-130.3020839691162 140.32769797889952, -130.3020839691162 131.03039595779904, -130.3020839691162 106.875 M-130.3020839691162 149.625 C-130.3020839691162 137.53299629512267, -130.3020839691162 125.44099259024534, -130.3020839691162 106.875" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-130.3020839691162 149.625 L130.3020839691162 149.625 L130.3020839691162 192.375 L-130.3020839691162 192.375" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-130.3020839691162 149.625 C-66.0563481575864 149.625, -1.8106123460565868 149.625, 130.3020839691162 149.625 M-130.3020839691162 149.625 C-30.47893125045526 149.625, 69.34422146820569 149.625, 130.3020839691162 149.625 M130.3020839691162 149.625 C130.3020839691162 162.3451263320658, 130.3020839691162 175.06525266413158, 130.3020839691162 192.375 M130.3020839691162 149.625 C130.3020839691162 159.6849002058559, 130.3020839691162 169.7448004117118, 130.3020839691162 192.375 M130.3020839691162 192.375 C73.37366180543577 192.375, 16.44523964175532 192.375, -130.3020839691162 192.375 M130.3020839691162 192.375 C65.89072498178798 192.375, 1.479365994459755 192.375, -130.3020839691162 192.375 M-130.3020839691162 192.375 C-130.3020839691162 183.57675307292368, -130.3020839691162 174.77850614584736, -130.3020839691162 149.625 M-130.3020839691162 192.375 C-130.3020839691162 176.57510856025206, -130.3020839691162 160.77521712050412, -130.3020839691162 149.625" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-38.046875, -183)" style=""><foreignObject width="76.09375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>MATERIALS</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, -140.25)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, -140.25)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, -140.25)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, -140.25)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, -97.5)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, -97.5)" style=""><foreignObject width="68.54167175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: start;"><span class="nodeLabel"><p>course_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, -97.5)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, -97.5)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, -54.75)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, -54.75)" style=""><foreignObject width="30.697917938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;"><span class="nodeLabel"><p>title</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, -54.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, -54.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, -12)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, -12)" style=""><foreignObject width="52" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 157px; text-align: start;"><span class="nodeLabel"><p>file_url</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, -12)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, -12)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, 30.75)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, 30.75)" style=""><foreignObject width="64.1875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 171px; text-align: start;"><span class="nodeLabel"><p>file_type</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, 30.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, 30.75)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, 73.5)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, 73.5)" style=""><foreignObject width="91.125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: start;"><span class="nodeLabel"><p>uploaded_by</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, 73.5)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, 73.5)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, 116.25)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, 116.25)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, 116.25)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, 116.25)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-117.80208396911621, 159)" style=""><foreignObject width="19.65625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 123px; text-align: start;"><span class="nodeLabel"><p>int</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-16.45833396911621, 159)" style=""><foreignObject width="59.677085876464844" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 168px; text-align: start;"><span class="nodeLabel"><p>file_size</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(99.66666603088379, 159)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(142.8020839691162, 159)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-130.3020839691162 -149.625 C-28.164623804007306 -149.625, 73.9728363611016 -149.625, 130.3020839691162 -149.625 M-130.3020839691162 -149.625 C-28.331740611544788 -149.625, 73.63860274602663 -149.625, 130.3020839691162 -149.625" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-28.95833396911621 -149.625 C-28.95833396911621 -32.540052961731476, -28.95833396911621 84.54489407653705, -28.95833396911621 192.375 M-28.95833396911621 -149.625 C-28.95833396911621 -29.36898698533622, -28.95833396911621 90.88702602932756, -28.95833396911621 192.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M87.16666603088379 -149.625 C87.16666603088379 -33.64061071233232, 87.16666603088379 82.34377857533536, 87.16666603088379 192.375 M87.16666603088379 -149.625 C87.16666603088379 -43.348220085823286, 87.16666603088379 62.92855982835343, 87.16666603088379 192.375" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-130.3020839691162 -149.625 C-58.14904049614313 -149.625, 14.004002976829952 -149.625, 130.3020839691162 -149.625 M-130.3020839691162 -149.625 C-34.657120172470584 -149.625, 60.98784362417504 -149.625, 130.3020839691162 -149.625" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-NOTIFICATIONS-8" transform="translate(653.3281259536743, 1193.25)"><g style=""><path d="M-128.0364589691162 -213.75 L128.0364589691162 -213.75 L128.0364589691162 213.75 L-128.0364589691162 213.75" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-128.0364589691162 -213.75 C-71.1987821304852 -213.75, -14.361105291854201 -213.75, 128.0364589691162 -213.75 M-128.0364589691162 -213.75 C-32.244981518093354 -213.75, 63.5464959329295 -213.75, 128.0364589691162 -213.75 M128.0364589691162 -213.75 C128.0364589691162 -125.01972162722772, 128.0364589691162 -36.28944325445545, 128.0364589691162 213.75 M128.0364589691162 -213.75 C128.0364589691162 -90.88611406674248, 128.0364589691162 31.97777186651504, 128.0364589691162 213.75 M128.0364589691162 213.75 C75.71635611895952 213.75, 23.396253268802838 213.75, -128.0364589691162 213.75 M128.0364589691162 213.75 C68.97155572939539 213.75, 9.906652489674556 213.75, -128.0364589691162 213.75 M-128.0364589691162 213.75 C-128.0364589691162 57.3442705274276, -128.0364589691162 -99.0614589451448, -128.0364589691162 -213.75 M-128.0364589691162 213.75 C-128.0364589691162 66.69961513361605, -128.0364589691162 -80.3507697327679, -128.0364589691162 -213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-128.0364589691162 -171 L128.0364589691162 -171 L128.0364589691162 -128.25 L-128.0364589691162 -128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-128.0364589691162 -171 C-57.958827230256375 -171, 12.11880450860346 -171, 128.0364589691162 -171 M-128.0364589691162 -171 C-69.68747050384926 -171, -11.338482038582313 -171, 128.0364589691162 -171 M128.0364589691162 -171 C128.0364589691162 -160.6994510550867, 128.0364589691162 -150.39890211017342, 128.0364589691162 -128.25 M128.0364589691162 -171 C128.0364589691162 -158.18313104799802, 128.0364589691162 -145.36626209599606, 128.0364589691162 -128.25 M128.0364589691162 -128.25 C67.6734099793407 -128.25, 7.310360989565197 -128.25, -128.0364589691162 -128.25 M128.0364589691162 -128.25 C28.95044000467496 -128.25, -70.13557895976629 -128.25, -128.0364589691162 -128.25 M-128.0364589691162 -128.25 C-128.0364589691162 -142.61925254626613, -128.0364589691162 -156.98850509253225, -128.0364589691162 -171 M-128.0364589691162 -128.25 C-128.0364589691162 -140.33517525517826, -128.0364589691162 -152.4203505103565, -128.0364589691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-128.0364589691162 -128.25 L128.0364589691162 -128.25 L128.0364589691162 -85.5 L-128.0364589691162 -85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-128.0364589691162 -128.25 C-63.42890549817275 -128.25, 1.1786479727707047 -128.25, 128.0364589691162 -128.25 M-128.0364589691162 -128.25 C-28.38371751270249 -128.25, 71.26902394371123 -128.25, 128.0364589691162 -128.25 M128.0364589691162 -128.25 C128.0364589691162 -112.1644828457017, 128.0364589691162 -96.0789656914034, 128.0364589691162 -85.5 M128.0364589691162 -128.25 C128.0364589691162 -114.96369955973513, 128.0364589691162 -101.67739911947027, 128.0364589691162 -85.5 M128.0364589691162 -85.5 C48.104954225983505 -85.5, -31.8265505171492 -85.5, -128.0364589691162 -85.5 M128.0364589691162 -85.5 C58.15684403572695 -85.5, -11.722770897662315 -85.5, -128.0364589691162 -85.5 M-128.0364589691162 -85.5 C-128.0364589691162 -94.79944953340808, -128.0364589691162 -104.09889906681616, -128.0364589691162 -128.25 M-128.0364589691162 -85.5 C-128.0364589691162 -97.01902938995639, -128.0364589691162 -108.53805877991277, -128.0364589691162 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-128.0364589691162 -85.5 L128.0364589691162 -85.5 L128.0364589691162 -42.75 L-128.0364589691162 -42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-128.0364589691162 -85.5 C-47.6000218886652 -85.5, 32.83641519178582 -85.5, 128.0364589691162 -85.5 M-128.0364589691162 -85.5 C-73.26337665420753 -85.5, -18.490294339298856 -85.5, 128.0364589691162 -85.5 M128.0364589691162 -85.5 C128.0364589691162 -71.81113474174461, 128.0364589691162 -58.122269483489205, 128.0364589691162 -42.75 M128.0364589691162 -85.5 C128.0364589691162 -74.2928425058183, 128.0364589691162 -63.08568501163659, 128.0364589691162 -42.75 M128.0364589691162 -42.75 C32.5196044133058 -42.75, -62.99725014250461 -42.75, -128.0364589691162 -42.75 M128.0364589691162 -42.75 C26.473882533384213 -42.75, -75.08869390234779 -42.75, -128.0364589691162 -42.75 M-128.0364589691162 -42.75 C-128.0364589691162 -53.93802079860244, -128.0364589691162 -65.12604159720487, -128.0364589691162 -85.5 M-128.0364589691162 -42.75 C-128.0364589691162 -57.56503154615135, -128.0364589691162 -72.3800630923027, -128.0364589691162 -85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-128.0364589691162 -42.75 L128.0364589691162 -42.75 L128.0364589691162 0 L-128.0364589691162 0" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-128.0364589691162 -42.75 C-30.7379425123017 -42.75, 66.56057394451281 -42.75, 128.0364589691162 -42.75 M-128.0364589691162 -42.75 C-37.71854944623129 -42.75, 52.599360076653625 -42.75, 128.0364589691162 -42.75 M128.0364589691162 -42.75 C128.0364589691162 -32.789354545775254, 128.0364589691162 -22.828709091550504, 128.0364589691162 0 M128.0364589691162 -42.75 C128.0364589691162 -34.120565363543484, 128.0364589691162 -25.491130727086965, 128.0364589691162 0 M128.0364589691162 0 C46.333895248225204 0, -35.3686684726658 0, -128.0364589691162 0 M128.0364589691162 0 C58.78637064481458 0, -10.463717679487047 0, -128.0364589691162 0 M-128.0364589691162 0 C-128.0364589691162 -11.704725778757231, -128.0364589691162 -23.409451557514462, -128.0364589691162 -42.75 M-128.0364589691162 0 C-128.0364589691162 -11.381656952756973, -128.0364589691162 -22.763313905513947, -128.0364589691162 -42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-128.0364589691162 0 L128.0364589691162 0 L128.0364589691162 42.75 L-128.0364589691162 42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-128.0364589691162 0 C-60.976920497132156 0, 6.0826179748519 0, 128.0364589691162 0 M-128.0364589691162 0 C-50.47048128533788 0, 27.095496398440446 0, 128.0364589691162 0 M128.0364589691162 0 C128.0364589691162 9.474143992809557, 128.0364589691162 18.948287985619114, 128.0364589691162 42.75 M128.0364589691162 0 C128.0364589691162 16.60023187402653, 128.0364589691162 33.20046374805306, 128.0364589691162 42.75 M128.0364589691162 42.75 C64.03955691791333 42.75, 0.04265486671043561 42.75, -128.0364589691162 42.75 M128.0364589691162 42.75 C42.095827334172995 42.75, -43.84480430077022 42.75, -128.0364589691162 42.75 M-128.0364589691162 42.75 C-128.0364589691162 28.956691563721293, -128.0364589691162 15.16338312744259, -128.0364589691162 0 M-128.0364589691162 42.75 C-128.0364589691162 30.61542321503203, -128.0364589691162 18.480846430064066, -128.0364589691162 0" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-128.0364589691162 42.75 L128.0364589691162 42.75 L128.0364589691162 85.5 L-128.0364589691162 85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-128.0364589691162 42.75 C-36.2649614379464 42.75, 55.50653609322342 42.75, 128.0364589691162 42.75 M-128.0364589691162 42.75 C-33.518683256593974 42.75, 60.99909245592826 42.75, 128.0364589691162 42.75 M128.0364589691162 42.75 C128.0364589691162 55.58947141462241, 128.0364589691162 68.42894282924482, 128.0364589691162 85.5 M128.0364589691162 42.75 C128.0364589691162 52.94887818609574, 128.0364589691162 63.14775637219148, 128.0364589691162 85.5 M128.0364589691162 85.5 C69.52545696236763 85.5, 11.01445495561903 85.5, -128.0364589691162 85.5 M128.0364589691162 85.5 C75.6560182413993 85.5, 23.27557751368238 85.5, -128.0364589691162 85.5 M-128.0364589691162 85.5 C-128.0364589691162 68.72814568564363, -128.0364589691162 51.95629137128724, -128.0364589691162 42.75 M-128.0364589691162 85.5 C-128.0364589691162 75.90161010164923, -128.0364589691162 66.30322020329845, -128.0364589691162 42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-128.0364589691162 85.5 L128.0364589691162 85.5 L128.0364589691162 128.25 L-128.0364589691162 128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-128.0364589691162 85.5 C-68.09611583587224 85.5, -8.155772702628255 85.5, 128.0364589691162 85.5 M-128.0364589691162 85.5 C-60.47381688808932 85.5, 7.088825192937577 85.5, 128.0364589691162 85.5 M128.0364589691162 85.5 C128.0364589691162 94.05722218571094, 128.0364589691162 102.61444437142187, 128.0364589691162 128.25 M128.0364589691162 85.5 C128.0364589691162 97.67101872155601, 128.0364589691162 109.842037443112, 128.0364589691162 128.25 M128.0364589691162 128.25 C49.40856275450949 128.25, -29.219333460097232 128.25, -128.0364589691162 128.25 M128.0364589691162 128.25 C34.85239868618403 128.25, -58.33166159674815 128.25, -128.0364589691162 128.25 M-128.0364589691162 128.25 C-128.0364589691162 112.32082658747686, -128.0364589691162 96.3916531749537, -128.0364589691162 85.5 M-128.0364589691162 128.25 C-128.0364589691162 112.14858100604468, -128.0364589691162 96.04716201208934, -128.0364589691162 85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-128.0364589691162 128.25 L128.0364589691162 128.25 L128.0364589691162 171 L-128.0364589691162 171" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-128.0364589691162 128.25 C-41.018960269217246 128.25, 45.99853843068172 128.25, 128.0364589691162 128.25 M-128.0364589691162 128.25 C-41.54789912816754 128.25, 44.940660712781124 128.25, 128.0364589691162 128.25 M128.0364589691162 128.25 C128.0364589691162 138.00858702105884, 128.0364589691162 147.7671740421177, 128.0364589691162 171 M128.0364589691162 128.25 C128.0364589691162 143.14989584343934, 128.0364589691162 158.04979168687865, 128.0364589691162 171 M128.0364589691162 171 C31.792808565309798 171, -64.45084183849661 171, -128.0364589691162 171 M128.0364589691162 171 C36.4280353575361 171, -55.180388254044004 171, -128.0364589691162 171 M-128.0364589691162 171 C-128.0364589691162 155.01349509524704, -128.0364589691162 139.02699019049408, -128.0364589691162 128.25 M-128.0364589691162 171 C-128.0364589691162 162.18536668622843, -128.0364589691162 153.3707333724569, -128.0364589691162 128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-128.0364589691162 171 L128.0364589691162 171 L128.0364589691162 213.75 L-128.0364589691162 213.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-128.0364589691162 171 C-37.96692686509979 171, 52.102605238916624 171, 128.0364589691162 171 M-128.0364589691162 171 C-61.335241394816975 171, 5.36597617948226 171, 128.0364589691162 171 M128.0364589691162 171 C128.0364589691162 180.42610879963183, 128.0364589691162 189.85221759926367, 128.0364589691162 213.75 M128.0364589691162 171 C128.0364589691162 185.2797676971983, 128.0364589691162 199.55953539439665, 128.0364589691162 213.75 M128.0364589691162 213.75 C70.8005736812508 213.75, 13.564688393385396 213.75, -128.0364589691162 213.75 M128.0364589691162 213.75 C57.04709298147675 213.75, -13.942273006162708 213.75, -128.0364589691162 213.75 M-128.0364589691162 213.75 C-128.0364589691162 200.74230121127198, -128.0364589691162 187.73460242254393, -128.0364589691162 171 M-128.0364589691162 213.75 C-128.0364589691162 203.50597029659374, -128.0364589691162 193.2619405931875, -128.0364589691162 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-53.734375, -204.375)" style=""><foreignObject width="107.46875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 219px; text-align: start;"><span class="nodeLabel"><p>NOTIFICATIONS</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, -161.625)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, -161.625)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, -161.625)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, -161.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, -118.875)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, -118.875)" style=""><foreignObject width="86.59375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 196px; text-align: start;"><span class="nodeLabel"><p>recipient_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, -118.875)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, -118.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, -76.125)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, -76.125)" style=""><foreignObject width="69.67708587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 176px; text-align: start;"><span class="nodeLabel"><p>sender_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, -76.125)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, -33.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, -33.375)" style=""><foreignObject width="31.875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 137px; text-align: start;"><span class="nodeLabel"><p>type</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, 9.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, 9.375)" style=""><foreignObject width="30.697917938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 136px; text-align: start;"><span class="nodeLabel"><p>title</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, 52.125)" style=""><foreignObject width="29.4375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 135px; text-align: start;"><span class="nodeLabel"><p>text</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, 52.125)" style=""><foreignObject width="60.125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 169px; text-align: start;"><span class="nodeLabel"><p>message</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, 94.875)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, 94.875)" style=""><foreignObject width="55.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 161px; text-align: start;"><span class="nodeLabel"><p>read_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, 137.625)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, 137.625)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-115.53645896911621, 180.375)" style=""><foreignObject width="29.67708396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>json</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-14.192708969116211, 180.375)" style=""><foreignObject width="68.83333587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 177px; text-align: start;"><span class="nodeLabel"><p>metadata</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(97.40104103088379, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(140.5364589691162, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-128.0364589691162 -171 C-29.95379122932539 -171, 68.12887651046543 -171, 128.0364589691162 -171 M-128.0364589691162 -171 C-38.96632218308049 -171, 50.103814602955225 -171, 128.0364589691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-26.69270896911621 -171 C-26.69270896911621 -24.57296556512844, -26.69270896911621 121.85406886974312, -26.69270896911621 213.75 M-26.69270896911621 -171 C-26.69270896911621 -58.126254387278806, -26.69270896911621 54.74749122544239, -26.69270896911621 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M84.90104103088379 -171 C84.90104103088379 -91.06744577695032, 84.90104103088379 -11.134891553900644, 84.90104103088379 213.75 M84.90104103088379 -171 C84.90104103088379 -28.0194527025983, 84.90104103088379 114.9610945948034, 84.90104103088379 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-128.0364589691162 -171 C-40.1339899268024 -171, 47.76847911551141 -171, 128.0364589691162 -171 M-128.0364589691162 -171 C-67.00433864994184 -171, -5.972218330767475 -171, 128.0364589691162 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g><g class="node default" id="entity-AUDIT_LOGS-9" transform="translate(1047.5416650772095, 1193.25)"><g style=""><path d="M-126.17708396911621 -213.75 L126.17708396911621 -213.75 L126.17708396911621 213.75 L-126.17708396911621 213.75" stroke="none" stroke-width="0" fill="#1f2020"></path><path d="M-126.17708396911621 -213.75 C-25.60488488857243 -213.75, 74.96731419197135 -213.75, 126.17708396911621 -213.75 M-126.17708396911621 -213.75 C-69.66440723873477 -213.75, -13.151730508353324 -213.75, 126.17708396911621 -213.75 M126.17708396911621 -213.75 C126.17708396911621 -123.25788130709427, 126.17708396911621 -32.76576261418853, 126.17708396911621 213.75 M126.17708396911621 -213.75 C126.17708396911621 -89.54540550994587, 126.17708396911621 34.65918898010827, 126.17708396911621 213.75 M126.17708396911621 213.75 C29.045492361171696 213.75, -68.08609924677282 213.75, -126.17708396911621 213.75 M126.17708396911621 213.75 C70.54341842949898 213.75, 14.909752889881744 213.75, -126.17708396911621 213.75 M-126.17708396911621 213.75 C-126.17708396911621 55.19123526859238, -126.17708396911621 -103.36752946281524, -126.17708396911621 -213.75 M-126.17708396911621 213.75 C-126.17708396911621 111.37068629616557, -126.17708396911621 8.991372592331146, -126.17708396911621 -213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.17708396911621 -171 L126.17708396911621 -171 L126.17708396911621 -128.25 L-126.17708396911621 -128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.17708396911621 -171 C-37.07309661715928 -171, 52.03089073479765 -171, 126.17708396911621 -171 M-126.17708396911621 -171 C-51.23890248459303 -171, 23.699278999930158 -171, 126.17708396911621 -171 M126.17708396911621 -171 C126.17708396911621 -159.29945993540667, 126.17708396911621 -147.59891987081335, 126.17708396911621 -128.25 M126.17708396911621 -171 C126.17708396911621 -157.35210884612565, 126.17708396911621 -143.70421769225126, 126.17708396911621 -128.25 M126.17708396911621 -128.25 C75.7062120810135 -128.25, 25.235340192910797 -128.25, -126.17708396911621 -128.25 M126.17708396911621 -128.25 C29.634802662202645 -128.25, -66.90747864471092 -128.25, -126.17708396911621 -128.25 M-126.17708396911621 -128.25 C-126.17708396911621 -141.47782415457354, -126.17708396911621 -154.70564830914708, -126.17708396911621 -171 M-126.17708396911621 -128.25 C-126.17708396911621 -140.06528371578412, -126.17708396911621 -151.88056743156827, -126.17708396911621 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-126.17708396911621 -128.25 L126.17708396911621 -128.25 L126.17708396911621 -85.5 L-126.17708396911621 -85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-126.17708396911621 -128.25 C-37.57659575953768 -128.25, 51.02389245004085 -128.25, 126.17708396911621 -128.25 M-126.17708396911621 -128.25 C-63.35242871465246 -128.25, -0.5277734601887119 -128.25, 126.17708396911621 -128.25 M126.17708396911621 -128.25 C126.17708396911621 -116.83436876895418, 126.17708396911621 -105.41873753790837, 126.17708396911621 -85.5 M126.17708396911621 -128.25 C126.17708396911621 -117.80719863829421, 126.17708396911621 -107.36439727658842, 126.17708396911621 -85.5 M126.17708396911621 -85.5 C56.39387991963599 -85.5, -13.389324129844226 -85.5, -126.17708396911621 -85.5 M126.17708396911621 -85.5 C26.479956748504264 -85.5, -73.21717047210768 -85.5, -126.17708396911621 -85.5 M-126.17708396911621 -85.5 C-126.17708396911621 -98.77970365578656, -126.17708396911621 -112.05940731157311, -126.17708396911621 -128.25 M-126.17708396911621 -85.5 C-126.17708396911621 -94.2768126947862, -126.17708396911621 -103.05362538957239, -126.17708396911621 -128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.17708396911621 -85.5 L126.17708396911621 -85.5 L126.17708396911621 -42.75 L-126.17708396911621 -42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.17708396911621 -85.5 C-40.2533261131573 -85.5, 45.67043174280161 -85.5, 126.17708396911621 -85.5 M-126.17708396911621 -85.5 C-44.463048092693626 -85.5, 37.25098778372896 -85.5, 126.17708396911621 -85.5 M126.17708396911621 -85.5 C126.17708396911621 -68.78839120964079, 126.17708396911621 -52.07678241928158, 126.17708396911621 -42.75 M126.17708396911621 -85.5 C126.17708396911621 -76.21359861262104, 126.17708396911621 -66.92719722524208, 126.17708396911621 -42.75 M126.17708396911621 -42.75 C29.073159971714276 -42.75, -68.03076402568766 -42.75, -126.17708396911621 -42.75 M126.17708396911621 -42.75 C33.81943891646716 -42.75, -58.538206136181884 -42.75, -126.17708396911621 -42.75 M-126.17708396911621 -42.75 C-126.17708396911621 -51.90676038833368, -126.17708396911621 -61.06352077666736, -126.17708396911621 -85.5 M-126.17708396911621 -42.75 C-126.17708396911621 -54.260039861558326, -126.17708396911621 -65.77007972311665, -126.17708396911621 -85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-126.17708396911621 -42.75 L126.17708396911621 -42.75 L126.17708396911621 0 L-126.17708396911621 0" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-126.17708396911621 -42.75 C-32.24344791675361 -42.75, 61.69018813560899 -42.75, 126.17708396911621 -42.75 M-126.17708396911621 -42.75 C-57.78243375142161 -42.75, 10.612216466272997 -42.75, 126.17708396911621 -42.75 M126.17708396911621 -42.75 C126.17708396911621 -33.12096523047259, 126.17708396911621 -23.491930460945184, 126.17708396911621 0 M126.17708396911621 -42.75 C126.17708396911621 -30.613265184561207, 126.17708396911621 -18.476530369122415, 126.17708396911621 0 M126.17708396911621 0 C44.0282504381733 0, -38.12058309276961 0, -126.17708396911621 0 M126.17708396911621 0 C74.97484139319327 0, 23.772598817270335 0, -126.17708396911621 0 M-126.17708396911621 0 C-126.17708396911621 -8.858036368580619, -126.17708396911621 -17.716072737161237, -126.17708396911621 -42.75 M-126.17708396911621 0 C-126.17708396911621 -8.777368364083305, -126.17708396911621 -17.55473672816661, -126.17708396911621 -42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.17708396911621 0 L126.17708396911621 0 L126.17708396911621 42.75 L-126.17708396911621 42.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.17708396911621 0 C-71.85774146985358 0, -17.538398970590947 0, 126.17708396911621 0 M-126.17708396911621 0 C-69.29220828843643 0, -12.40733260775663 0, 126.17708396911621 0 M126.17708396911621 0 C126.17708396911621 8.709901132132323, 126.17708396911621 17.419802264264646, 126.17708396911621 42.75 M126.17708396911621 0 C126.17708396911621 14.037434607247109, 126.17708396911621 28.074869214494218, 126.17708396911621 42.75 M126.17708396911621 42.75 C27.167790168130182 42.75, -71.84150363285585 42.75, -126.17708396911621 42.75 M126.17708396911621 42.75 C61.60636797378618 42.75, -2.964348021543856 42.75, -126.17708396911621 42.75 M-126.17708396911621 42.75 C-126.17708396911621 26.05029579998091, -126.17708396911621 9.350591599961817, -126.17708396911621 0 M-126.17708396911621 42.75 C-126.17708396911621 29.19919088655343, -126.17708396911621 15.648381773106866, -126.17708396911621 0" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-126.17708396911621 42.75 L126.17708396911621 42.75 L126.17708396911621 85.5 L-126.17708396911621 85.5" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-126.17708396911621 42.75 C-42.88512502237788 42.75, 40.406833924360456 42.75, 126.17708396911621 42.75 M-126.17708396911621 42.75 C-26.180022984932265 42.75, 73.81703799925168 42.75, 126.17708396911621 42.75 M126.17708396911621 42.75 C126.17708396911621 53.70831237933383, 126.17708396911621 64.66662475866767, 126.17708396911621 85.5 M126.17708396911621 42.75 C126.17708396911621 51.75112782035425, 126.17708396911621 60.7522556407085, 126.17708396911621 85.5 M126.17708396911621 85.5 C25.729305309470305 85.5, -74.7184733501756 85.5, -126.17708396911621 85.5 M126.17708396911621 85.5 C73.01346481257599 85.5, 19.849845656035768 85.5, -126.17708396911621 85.5 M-126.17708396911621 85.5 C-126.17708396911621 76.13042859208718, -126.17708396911621 66.76085718417437, -126.17708396911621 42.75 M-126.17708396911621 85.5 C-126.17708396911621 71.14064650855286, -126.17708396911621 56.78129301710571, -126.17708396911621 42.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.17708396911621 85.5 L126.17708396911621 85.5 L126.17708396911621 128.25 L-126.17708396911621 128.25" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.17708396911621 85.5 C-57.76258888846681 85.5, 10.651906192182594 85.5, 126.17708396911621 85.5 M-126.17708396911621 85.5 C-30.570815954944777 85.5, 65.03545205922666 85.5, 126.17708396911621 85.5 M126.17708396911621 85.5 C126.17708396911621 94.34743864396441, 126.17708396911621 103.19487728792882, 126.17708396911621 128.25 M126.17708396911621 85.5 C126.17708396911621 98.26502769659722, 126.17708396911621 111.03005539319442, 126.17708396911621 128.25 M126.17708396911621 128.25 C43.65923566318966 128.25, -38.85861264273689 128.25, -126.17708396911621 128.25 M126.17708396911621 128.25 C67.2876442259377 128.25, 8.398204482759198 128.25, -126.17708396911621 128.25 M-126.17708396911621 128.25 C-126.17708396911621 111.88697258683979, -126.17708396911621 95.52394517367958, -126.17708396911621 85.5 M-126.17708396911621 128.25 C-126.17708396911621 116.37957933286961, -126.17708396911621 104.50915866573922, -126.17708396911621 85.5" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-even"><path d="M-126.17708396911621 128.25 L126.17708396911621 128.25 L126.17708396911621 171 L-126.17708396911621 171" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 2.3529411765%)"></path><path d="M-126.17708396911621 128.25 C-35.85618227996716 128.25, 54.464719409181896 128.25, 126.17708396911621 128.25 M-126.17708396911621 128.25 C-48.64576795917533 128.25, 28.88554805076555 128.25, 126.17708396911621 128.25 M126.17708396911621 128.25 C126.17708396911621 143.3150101593389, 126.17708396911621 158.38002031867782, 126.17708396911621 171 M126.17708396911621 128.25 C126.17708396911621 143.80512394138722, 126.17708396911621 159.36024788277442, 126.17708396911621 171 M126.17708396911621 171 C35.46650863498013 171, -55.24406669915595 171, -126.17708396911621 171 M126.17708396911621 171 C69.93942872376059 171, 13.70177347840496 171, -126.17708396911621 171 M-126.17708396911621 171 C-126.17708396911621 154.28686191243608, -126.17708396911621 137.57372382487216, -126.17708396911621 128.25 M-126.17708396911621 171 C-126.17708396911621 155.68451765869497, -126.17708396911621 140.36903531738994, -126.17708396911621 128.25" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g style="" class="row-rect-odd"><path d="M-126.17708396911621 171 L126.17708396911621 171 L126.17708396911621 213.75 L-126.17708396911621 213.75" stroke="none" stroke-width="0" fill="hsl(180, 1.5873015873%, 17.3529411765%)"></path><path d="M-126.17708396911621 171 C-64.91944972114302 171, -3.6618154731698525 171, 126.17708396911621 171 M-126.17708396911621 171 C-28.722846130398082 171, 68.73139170832005 171, 126.17708396911621 171 M126.17708396911621 171 C126.17708396911621 182.45026833905436, 126.17708396911621 193.90053667810875, 126.17708396911621 213.75 M126.17708396911621 171 C126.17708396911621 180.296387429432, 126.17708396911621 189.59277485886398, 126.17708396911621 213.75 M126.17708396911621 213.75 C71.38053048055158 213.75, 16.583976991986958 213.75, -126.17708396911621 213.75 M126.17708396911621 213.75 C68.54633689416127 213.75, 10.915589819206318 213.75, -126.17708396911621 213.75 M-126.17708396911621 213.75 C-126.17708396911621 200.9414419346985, -126.17708396911621 188.132883869397, -126.17708396911621 171 M-126.17708396911621 213.75 C-126.17708396911621 199.88161029015765, -126.17708396911621 186.0132205803153, -126.17708396911621 171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="label name" transform="translate(-44.578125, -204.375)" style=""><foreignObject width="89.15625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 194px; text-align: start;"><span class="nodeLabel"><p>AUDIT_LOGS</p></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, -161.625)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, -161.625)" style=""><foreignObject width="13.479166984558105" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 115px; text-align: start;"><span class="nodeLabel"><p>id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, -161.625)" style=""><foreignObject width="18.135417938232422" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 119px; text-align: start;"><span class="nodeLabel"><p>PK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, -161.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, -118.875)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, -118.875)" style=""><foreignObject width="52.03125" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 157px; text-align: start;"><span class="nodeLabel"><p>user_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, -118.875)" style=""><foreignObject width="17.61458396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 118px; text-align: start;"><span class="nodeLabel"><p>FK</p></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, -118.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, -76.125)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, -76.125)" style=""><foreignObject width="44.5625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 150px; text-align: start;"><span class="nodeLabel"><p>action</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, -76.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, -33.375)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, -33.375)" style=""><foreignObject width="82.875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 193px; text-align: start;"><span class="nodeLabel"><p>entity_type</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, -33.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, 9.375)" style=""><foreignObject width="30.96875" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>uuid</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, 9.375)" style=""><foreignObject width="64.47917175292969" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 173px; text-align: start;"><span class="nodeLabel"><p>entity_id</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, 9.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, 52.125)" style=""><foreignObject width="29.67708396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>json</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, 52.125)" style=""><foreignObject width="75.52083587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 184px; text-align: start;"><span class="nodeLabel"><p>old_values</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, 52.125)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, 94.875)" style=""><foreignObject width="29.67708396911621" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 134px; text-align: start;"><span class="nodeLabel"><p>json</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, 94.875)" style=""><foreignObject width="82.67708587646484" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 191px; text-align: start;"><span class="nodeLabel"><p>new_values</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, 94.875)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, 137.625)" style=""><foreignObject width="40.375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 148px; text-align: start;"><span class="nodeLabel"><p>string</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, 137.625)" style=""><foreignObject width="76" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>ip_address</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, 137.625)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-type" transform="translate(-113.67708396911621, 180.375)" style=""><foreignObject width="76.34375" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 186px; text-align: start;"><span class="nodeLabel"><p>timestamp</p></span></div></foreignObject></g><g class="label attribute-name" transform="translate(-12.333333969116211, 180.375)" style=""><foreignObject width="78.40625" height="24"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 187px; text-align: start;"><span class="nodeLabel"><p>created_at</p></span></div></foreignObject></g><g class="label attribute-keys" transform="translate(95.54166603088379, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="label attribute-comment" transform="translate(138.6770839691162, 180.375)" style=""><foreignObject width="0" height="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 100px; text-align: start;"><span class="nodeLabel"></span></div></foreignObject></g><g class="divider"><path d="M-126.17708396911621 -171 C-26.780797866054257 -171, 72.6154882370077 -171, 126.17708396911621 -171 M-126.17708396911621 -171 C-56.68078738507309 -171, 12.815509198970034 -171, 126.17708396911621 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-24.83333396911621 -171 C-24.83333396911621 -72.20943570980332, -24.83333396911621 26.58112858039337, -24.83333396911621 213.75 M-24.83333396911621 -171 C-24.83333396911621 -43.05919500243469, -24.83333396911621 84.88160999513062, -24.83333396911621 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M83.04166603088379 -171 C83.04166603088379 -74.43788321457743, 83.04166603088379 22.12423357084515, 83.04166603088379 213.75 M83.04166603088379 -171 C83.04166603088379 -88.80294800473385, 83.04166603088379 -6.605896009467699, 83.04166603088379 213.75" stroke="#ccc" stroke-width="1.3" fill="none"></path></g><g class="divider"><path d="M-126.17708396911621 -171 C-27.968332977834876 -171, 70.24041801344646 -171, 126.17708396911621 -171 M-126.17708396911621 -171 C-36.01440678136565 -171, 54.14827040638491 -171, 126.17708396911621 -171" stroke="#ccc" stroke-width="1.3" fill="none"></path></g></g></g></g></g></svg>