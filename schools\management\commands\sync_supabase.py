"""
Django management command to sync data between SQLite and Supabase.
"""
import os
from django.core.management.base import BaseCommand
from django.db import transaction
from supabase import create_client, Client
from schools.models import School
from users.models import User
from courses.models import Course
from datetime import datetime
import uuid


class Command(BaseCommand):
    help = 'Sync data between Django SQLite and Supabase PostgreSQL'

    def add_arguments(self, parser):
        parser.add_argument(
            '--direction',
            type=str,
            choices=['pull', 'push', 'both'],
            default='pull',
            help='Sync direction: pull (Supabase→Django), push (Django→Supabase), both'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be synced without making changes'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== Supabase Sync Tool ===\n'))
        
        # Initialize Supabase client
        url = os.getenv('SUPABASE_URL')
        key = os.getenv('SUPABASE_KEY')
        
        if not url or not key:
            self.stdout.write(self.style.ERROR('Missing Supabase credentials in .env file'))
            return
        
        self.supabase: Client = create_client(url, key)
        self.dry_run = options['dry_run']
        
        if self.dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made\n'))
        
        direction = options['direction']
        
        if direction in ['pull', 'both']:
            self.pull_from_supabase()
        
        if direction in ['push', 'both']:
            self.push_to_supabase()
        
        self.stdout.write(self.style.SUCCESS('\n✅ Sync completed successfully!'))

    def pull_from_supabase(self):
        """Pull data from Supabase to Django SQLite"""
        self.stdout.write(self.style.HTTP_INFO('--- Pulling data from Supabase to Django ---'))
        
        try:
            # Sync Schools
            self.stdout.write('Syncing schools...')
            schools_response = self.supabase.table('schools').select('*').execute()
            schools_data = schools_response.data
            
            with transaction.atomic():
                for school_data in schools_data:
                    if not self.dry_run:
                        school, created = School.objects.update_or_create(
                            id=school_data['id'],
                            defaults={
                                'name': school_data['name'],
                                'slug': school_data['slug'],
                                'email': school_data['email'],
                                'phone': school_data.get('phone') or '',
                                'website': school_data.get('website') or '',
                                'address_line_1': school_data['address_line_1'],
                                'address_line_2': school_data.get('address_line_2') or '',
                                'city': school_data['city'],
                                'state_province': school_data['state_province'],
                                'postal_code': school_data['postal_code'],
                                'country': school_data['country'],
                                'timezone': school_data.get('timezone') or 'UTC',
                                'academic_year_start': school_data['academic_year_start'],
                                'academic_year_end': school_data['academic_year_end'],
                                'subscription_plan': school_data['subscription_plan'],
                                'status': school_data['status'],
                                'max_students': school_data.get('max_students') or 100,
                                'max_staff': school_data.get('max_staff') or 20,
                                'created_by': school_data['created_by']
                            }
                        )
                        action = 'Created' if created else 'Updated'
                        self.stdout.write(f'  {action}: {school.name}')
                    else:
                        self.stdout.write(f'  Would sync: {school_data["name"]}')
            
            # Sync Users
            self.stdout.write('Syncing users...')
            users_response = self.supabase.table('users').select('*').execute()
            users_data = users_response.data
            
            with transaction.atomic():
                for user_data in users_data:
                    if not self.dry_run:
                        # Get the school object
                        try:
                            school = School.objects.get(id=user_data['school_id']) if user_data.get('school_id') else None
                        except School.DoesNotExist:
                            school = None
                        
                        user, created = User.objects.update_or_create(
                            id=user_data['id'],
                            defaults={
                                'email': user_data['email'],
                                'password': user_data['password'],
                                'first_name': user_data.get('first_name') or '',
                                'last_name': user_data.get('last_name') or '',
                                'middle_name': user_data.get('middle_name') or '',
                                'date_of_birth': user_data.get('date_of_birth'),
                                'phone': user_data.get('phone') or '',
                                'school': school,
                                'user_type': user_data['user_type'],
                                'student_id': user_data.get('student_id') or '',
                                'enrollment_date': user_data.get('enrollment_date'),
                                'graduation_date': user_data.get('graduation_date'),
                                'employee_id': user_data.get('employee_id') or '',
                                'hire_date': user_data.get('hire_date'),
                                'department': user_data.get('department') or '',
                                'avatar': user_data.get('avatar') or '',
                                'bio': user_data.get('bio') or '',
                                'email_verified': user_data.get('email_verified', False),
                                'failed_login_attempts': user_data.get('failed_login_attempts', 0),
                                'is_active': user_data.get('is_active', True),
                                'is_staff': user_data.get('is_staff', False),
                                'is_superuser': user_data.get('is_superuser', False),
                            }
                        )
                        action = 'Created' if created else 'Updated'
                        self.stdout.write(f'  {action}: {user.email}')
                    else:
                        self.stdout.write(f'  Would sync: {user_data["email"]}')
            
            # Sync Courses
            self.stdout.write('Syncing courses...')
            courses_response = self.supabase.table('courses').select('*').execute()
            courses_data = courses_response.data
            
            with transaction.atomic():
                for course_data in courses_data:
                    if not self.dry_run:
                        try:
                            school = School.objects.get(id=course_data['school_id'])
                        except School.DoesNotExist:
                            self.stdout.write(f'  Skipping course {course_data["code"]} - school not found')
                            continue
                        
                        course, created = Course.objects.update_or_create(
                            id=course_data['id'],
                            defaults={
                                'code': course_data['code'],
                                'name': course_data['name'],
                                'description': course_data.get('description') or '',
                                'credits': course_data.get('credits') or 0,
                                'school': school,
                                'department': course_data.get('department') or '',
                                'level': course_data.get('level') or '',
                                'semester': course_data.get('semester') or '',
                                'academic_year': course_data.get('academic_year') or '',
                                'start_date': course_data.get('start_date'),
                                'end_date': course_data.get('end_date'),
                                'max_enrollment': course_data.get('max_students') or 30,
                                'is_active': course_data.get('is_active', True),
                            }
                        )
                        action = 'Created' if created else 'Updated'
                        self.stdout.write(f'  {action}: {course.code} - {course.name}')
                    else:
                        self.stdout.write(f'  Would sync: {course_data["code"]} - {course_data["name"]}')
            
            self.stdout.write(self.style.SUCCESS(f'✅ Pulled {len(schools_data)} schools, {len(users_data)} users, {len(courses_data)} courses'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error pulling from Supabase: {str(e)}'))

    def push_to_supabase(self):
        """Push data from Django SQLite to Supabase"""
        self.stdout.write(self.style.HTTP_INFO('--- Pushing data from Django to Supabase ---'))
        
        try:
            # Push Schools
            schools = School.objects.all()
            self.stdout.write(f'Pushing {schools.count()} schools...')
            
            for school in schools:
                school_data = {
                    'id': str(school.id),
                    'name': school.name,
                    'slug': school.slug,
                    'email': school.email,
                    'phone': school.phone,
                    'website': school.website,
                    'address_line_1': school.address_line_1,
                    'address_line_2': school.address_line_2,
                    'city': school.city,
                    'state_province': school.state_province,
                    'postal_code': school.postal_code,
                    'country': school.country,
                    'timezone': school.timezone,
                    'academic_year_start': school.academic_year_start.isoformat(),
                    'academic_year_end': school.academic_year_end.isoformat(),
                    'subscription_plan': school.subscription_plan,
                    'status': school.status,
                    'max_students': school.max_students,
                    'max_staff': school.max_staff,
                    'created_by': school.created_by,
                    'created_at': school.created_at.isoformat(),
                    'updated_at': school.updated_at.isoformat(),
                }
                
                if not self.dry_run:
                    # Use upsert to insert or update
                    self.supabase.table('schools').upsert(school_data).execute()
                    self.stdout.write(f'  Pushed: {school.name}')
                else:
                    self.stdout.write(f'  Would push: {school.name}')
            
            self.stdout.write(self.style.SUCCESS(f'✅ Pushed {schools.count()} schools to Supabase'))

            # Push Users
            from users.models import User
            users = User.objects.all()
            self.stdout.write(f'Pushing {users.count()} users...')

            for user in users:
                user_data = {
                    'id': str(user.id),
                    'email': user.email,
                    'password': '[SYNCED]',  # Placeholder for synced users
                    'first_name': user.first_name or '',
                    'middle_name': user.middle_name or '',
                    'last_name': user.last_name or '',
                    'user_type': user.user_type,
                    'school_id': str(user.school.id) if user.school else None,
                    'student_id': user.student_id or '',
                    'enrollment_date': user.enrollment_date.isoformat() if user.enrollment_date else None,
                    'graduation_date': user.graduation_date.isoformat() if user.graduation_date else None,
                    'employee_id': user.employee_id or '',
                    'hire_date': user.hire_date.isoformat() if user.hire_date else None,
                    'department': user.department or '',
                    'phone': user.phone or '',
                    'date_of_birth': user.date_of_birth.isoformat() if user.date_of_birth else None,
                    'gender': user.gender or '',
                    'nid': user.nid or '',
                    'bio': user.bio or '',
                    'is_active': user.is_active,
                    'is_staff': user.is_staff,
                    'is_superuser': user.is_superuser,
                    'email_verified': user.email_verified,
                    'created_at': user.created_at.isoformat(),
                    'updated_at': user.updated_at.isoformat(),
                }

                if not self.dry_run:
                    self.supabase.table('users').upsert(user_data).execute()
                    self.stdout.write(f'  Pushed: {user.get_full_name()} ({user.email})')
                else:
                    self.stdout.write(f'  Would push: {user.get_full_name()} ({user.email})')

            self.stdout.write(self.style.SUCCESS(f'✅ Pushed {users.count()} users to Supabase'))

            # Push Courses
            from courses.models import Course
            courses = Course.objects.all()
            self.stdout.write(f'Pushing {courses.count()} courses...')

            for course in courses:
                course_data = {
                    'id': str(course.id),
                    'school_id': str(course.school.id) if course.school else None,
                    'code': course.code,
                    'name': course.name,
                    'description': course.description or '',
                    'credits': course.credits,
                    'level': course.level or '',
                    'department': course.department or '',
                    'semester': course.semester,
                    'academic_year': course.academic_year,
                    'end_date': course.end_date.isoformat() if course.end_date else None,
                    'max_enrollment': course.max_enrollment,
                    'enrollment_open': course.enrollment_open,
                    'is_active': course.is_active,
                    'created_at': course.created_at.isoformat(),
                    'updated_at': course.updated_at.isoformat(),
                }

                if not self.dry_run:
                    self.supabase.table('courses').upsert(course_data).execute()
                    self.stdout.write(f'  Pushed: {course.code} - {course.name}')
                else:
                    self.stdout.write(f'  Would push: {course.code} - {course.name}')

            self.stdout.write(self.style.SUCCESS(f'✅ Pushed {courses.count()} courses to Supabase'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ Error pushing to Supabase: {str(e)}'))
