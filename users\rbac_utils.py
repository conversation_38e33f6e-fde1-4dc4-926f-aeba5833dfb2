"""
RBAC utility functions for role and permission management.
"""

from typing import List, Dict, Optional, Set
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.db.models import Q, QuerySet
from django.utils import timezone
from .models import Role, Permission, UserRole, RolePermission, RoleAuditLog

User = get_user_model()


class RBACManager:
    """
    Utility class for managing RBAC operations.
    """
    
    @staticmethod
    def get_user_permissions(user: User, school=None) -> Set[str]:
        """
        Get all permissions for a user, considering role hierarchy and school context.
        
        Args:
            user: User instance
            school: School instance for context (optional)
            
        Returns:
            Set of permission codenames
        """
        if not user.is_active:
            return set()
        
        # Get active user roles
        user_roles_query = user.user_roles.filter(
            is_active=True,
            valid_from__lte=timezone.now()
        ).filter(
            Q(valid_until__isnull=True) | Q(valid_until__gte=timezone.now())
        )
        
        # Filter by school if provided
        if school:
            user_roles_query = user_roles_query.filter(
                Q(role__school=school) | Q(role__school__isnull=True)
            )
        
        # Get all permissions from active roles
        permissions = set()
        for user_role in user_roles_query.select_related('role'):
            role_permissions = RBACManager.get_role_permissions(user_role.role)
            permissions.update(role_permissions)
        
        return permissions
    
    @staticmethod
    def get_role_permissions(role: Role) -> Set[str]:
        """
        Get all permissions for a role, including inherited permissions.
        
        Args:
            role: Role instance
            
        Returns:
            Set of permission codenames
        """
        permissions = set()
        
        # Get direct permissions
        role_perms = role.role_permissions.filter(
            granted=True
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gte=timezone.now())
        ).select_related('permission')
        
        for role_perm in role_perms:
            permissions.add(role_perm.permission.codename)
        
        # Get inherited permissions from parent role
        if role.parent_role:
            parent_permissions = RBACManager.get_role_permissions(role.parent_role)
            permissions.update(parent_permissions)
        
        return permissions
    
    @staticmethod
    def user_has_permission(user: User, permission_codename: str, school=None) -> bool:
        """
        Check if a user has a specific permission.
        
        Args:
            user: User instance
            permission_codename: Permission codename to check
            school: School context (optional)
            
        Returns:
            Boolean indicating if user has permission
        """
        user_permissions = RBACManager.get_user_permissions(user, school)
        return permission_codename in user_permissions
    
    @staticmethod
    def assign_role_to_user(
        user: User, 
        role: Role, 
        assigned_by: User,
        valid_from: Optional[timezone.datetime] = None,
        valid_until: Optional[timezone.datetime] = None,
        requires_approval: bool = False
    ) -> UserRole:
        """
        Assign a role to a user with proper audit logging.
        
        Args:
            user: User to assign role to
            role: Role to assign
            assigned_by: User performing the assignment
            valid_from: When the role becomes valid
            valid_until: When the role expires
            requires_approval: Whether assignment requires approval
            
        Returns:
            UserRole instance
        """
        # Check if user already has this role
        existing_role = UserRole.objects.filter(
            user=user,
            role=role,
            is_active=True
        ).first()
        
        if existing_role:
            raise ValueError(f"User {user} already has role {role}")
        
        # Create user role assignment
        user_role = UserRole.objects.create(
            user=user,
            role=role,
            assigned_by=assigned_by,
            valid_from=valid_from or timezone.now(),
            valid_until=valid_until,
            requires_approval=requires_approval,
            is_active=not requires_approval  # Active immediately if no approval needed
        )
        
        # Log the action
        RoleAuditLog.objects.create(
            action_type='user_role_assigned',
            description=f"Role '{role.name}' assigned to user '{user.get_full_name()}'",
            performed_by=assigned_by,
            target_user=user,
            target_role=role,
            school=role.school,
            metadata={
                'requires_approval': requires_approval,
                'valid_from': valid_from.isoformat() if valid_from else None,
                'valid_until': valid_until.isoformat() if valid_until else None,
            }
        )
        
        return user_role
    
    @staticmethod
    def remove_role_from_user(user: User, role: Role, removed_by: User) -> bool:
        """
        Remove a role from a user with proper audit logging.
        
        Args:
            user: User to remove role from
            role: Role to remove
            removed_by: User performing the removal
            
        Returns:
            Boolean indicating success
        """
        user_role = UserRole.objects.filter(
            user=user,
            role=role,
            is_active=True
        ).first()
        
        if not user_role:
            return False
        
        # Deactivate the role
        user_role.is_active = False
        user_role.deactivated_by = removed_by
        user_role.deactivated_at = timezone.now()
        user_role.save()
        
        # Log the action
        RoleAuditLog.objects.create(
            action_type='user_role_removed',
            description=f"Role '{role.name}' removed from user '{user.get_full_name()}'",
            performed_by=removed_by,
            target_user=user,
            target_role=role,
            school=role.school
        )
        
        return True
    
    @staticmethod
    def approve_role_assignment(user_role: UserRole, approved_by: User) -> bool:
        """
        Approve a pending role assignment.
        
        Args:
            user_role: UserRole instance to approve
            approved_by: User performing the approval
            
        Returns:
            Boolean indicating success
        """
        if not user_role.requires_approval or user_role.approved_at:
            return False
        
        user_role.approved_by = approved_by
        user_role.approved_at = timezone.now()
        user_role.is_active = True
        user_role.save()
        
        # Log the action
        RoleAuditLog.objects.create(
            action_type='role_approved',
            description=f"Role assignment '{user_role.role.name}' approved for user '{user_role.user.get_full_name()}'",
            performed_by=approved_by,
            target_user=user_role.user,
            target_role=user_role.role,
            school=user_role.role.school
        )
        
        return True
    
    @staticmethod
    def get_users_with_permission(permission_codename: str, school=None) -> QuerySet:
        """
        Get all users who have a specific permission.
        
        Args:
            permission_codename: Permission codename to search for
            school: School context (optional)
            
        Returns:
            QuerySet of User instances
        """
        # Get permission
        try:
            permission = Permission.objects.get(codename=permission_codename)
        except Permission.DoesNotExist:
            return User.objects.none()
        
        # Get roles that have this permission
        role_permissions = RolePermission.objects.filter(
            permission=permission,
            granted=True
        ).filter(
            Q(expires_at__isnull=True) | Q(expires_at__gte=timezone.now())
        )
        
        role_ids = role_permissions.values_list('role_id', flat=True)
        
        # Get users with these roles
        user_roles_query = UserRole.objects.filter(
            role_id__in=role_ids,
            is_active=True,
            valid_from__lte=timezone.now()
        ).filter(
            Q(valid_until__isnull=True) | Q(valid_until__gte=timezone.now())
        )
        
        # Filter by school if provided
        if school:
            user_roles_query = user_roles_query.filter(
                Q(role__school=school) | Q(role__school__isnull=True)
            )
        
        user_ids = user_roles_query.values_list('user_id', flat=True)
        return User.objects.filter(id__in=user_ids, is_active=True)
    
    @staticmethod
    def get_role_hierarchy(role: Role) -> List[Role]:
        """
        Get the complete role hierarchy for a role (including parent roles).
        
        Args:
            role: Role instance
            
        Returns:
            List of Role instances in hierarchy order
        """
        hierarchy = [role]
        current_role = role
        
        while current_role.parent_role:
            current_role = current_role.parent_role
            hierarchy.append(current_role)
        
        return hierarchy
    
    @staticmethod
    def create_role_from_template(
        template_name: str,
        role_name: str,
        school,
        created_by: User,
        description: str = ""
    ) -> Role:
        """
        Create a new role based on a role template.
        
        Args:
            template_name: Name of the role template
            role_name: Name for the new role
            school: School instance
            created_by: User creating the role
            description: Role description
            
        Returns:
            Role instance
        """
        from .models import RoleTemplate
        
        try:
            template = RoleTemplate.objects.get(name=template_name, is_active=True)
        except RoleTemplate.DoesNotExist:
            raise ValueError(f"Role template '{template_name}' not found")
        
        # Create the role
        role = Role.objects.create(
            name=role_name,
            description=description or template.description,
            school=school,
            role_type='school',
            created_by=created_by
        )
        
        # Assign permissions from template
        for permission in template.permissions.all():
            RolePermission.objects.create(
                role=role,
                permission=permission,
                granted=True,
                granted_by=created_by
            )
        
        # Update template usage count
        template.usage_count += 1
        template.save()
        
        # Log the action
        RoleAuditLog.objects.create(
            action_type='role_created',
            description=f"Role '{role_name}' created from template '{template_name}'",
            performed_by=created_by,
            target_role=role,
            school=school,
            metadata={'template_name': template_name}
        )
        
        return role
