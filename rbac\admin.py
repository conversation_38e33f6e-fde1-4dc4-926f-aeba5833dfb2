from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Permission, Role, RolePermission, UserRole, RoleTemplate, RoleAuditLog


@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    """
    Admin interface for Permission model.
    """
    list_display = [
        'name', 'codename', 'category', 'content_type',
        'is_system_permission', 'created_at'
    ]
    list_filter = [
        'category', 'is_system_permission', 'content_type', 'created_at'
    ]
    search_fields = ['name', 'codename', 'description']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'codename', 'description')
        }),
        ('Classification', {
            'fields': ('category', 'content_type', 'is_system_permission')
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('content_type', 'created_by')


class RolePermissionInline(admin.TabularInline):
    """
    Inline admin for RolePermission model.
    """
    model = RolePermission
    extra = 0
    fields = ['permission', 'granted', 'expires_at', 'granted_by']
    readonly_fields = ['granted_by', 'granted_at']
    autocomplete_fields = ['permission']


@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    """
    Admin interface for Role model with enhanced features.
    """
    list_display = [
        'name', 'school', 'role_type', 'is_active',
        'is_default', 'permission_count', 'user_count', 'created_at'
    ]
    list_filter = [
        'role_type', 'is_active', 'is_default', 'school', 'created_at'
    ]
    search_fields = ['name', 'description', 'school__name']
    readonly_fields = ['created_at', 'updated_at', 'permission_count', 'user_count']
    inlines = [RolePermissionInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'role_type')
        }),
        ('School Association', {
            'fields': ('school',),
            'description': 'Leave blank for system-wide roles'
        }),
        ('Role Configuration', {
            'fields': ('parent_role', 'is_active', 'is_default')
        }),
        ('Statistics', {
            'fields': ('permission_count', 'user_count'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('school', 'created_by')

    def permission_count(self, obj):
        """Display number of permissions assigned to this role."""
        count = obj.role_permissions.filter(granted=True).count()
        return format_html(
            '<span style="color: green; font-weight: bold;">{}</span>',
            count
        )
    permission_count.short_description = 'Permissions'

    def user_count(self, obj):
        """Display number of users assigned to this role."""
        count = obj.user_roles.filter(is_active=True).count()
        return format_html(
            '<span style="color: blue; font-weight: bold;">{}</span>',
            count
        )
    user_count.short_description = 'Users'


@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    """
    Admin interface for UserRole model.
    """
    list_display = [
        'user', 'role', 'is_active', 'expires_at',
        'requires_approval', 'approval_status', 'assigned_at'
    ]
    list_filter = [
        'is_active', 'requires_approval', 'role__school',
        'assigned_at', 'expires_at'
    ]
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name',
        'role__name', 'role__school__name'
    ]
    readonly_fields = [
        'assigned_at', 'updated_at', 'approval_status'
    ]
    autocomplete_fields = ['user', 'role', 'assigned_by', 'approved_by']

    fieldsets = (
        ('Assignment Details', {
            'fields': ('user', 'role', 'is_active')
        }),
        ('Assignment Metadata', {
            'fields': ('assigned_by', 'assigned_at', 'expires_at')
        }),
        ('Approval Workflow', {
            'fields': ('requires_approval', 'approved_by', 'approved_at'),
            'classes': ('collapse',)
        }),
        ('Deactivation', {
            'fields': ('deactivated_by', 'deactivated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'user', 'role', 'role__school', 'assigned_by', 'approved_by'
        )

    def approval_status(self, obj):
        """Display approval status with color coding."""
        if not obj.requires_approval:
            return format_html('<span style="color: gray;">N/A</span>')
        elif obj.approved_at:
            return format_html('<span style="color: green;">✓ Approved</span>')
        else:
            return format_html('<span style="color: orange;">⏳ Pending</span>')
    approval_status.short_description = 'Approval Status'


@admin.register(RoleTemplate)
class RoleTemplateAdmin(admin.ModelAdmin):
    """
    Admin interface for RoleTemplate model.
    """
    list_display = [
        'name', 'is_active', 'is_system_template',
        'usage_count', 'permission_count', 'created_at'
    ]
    list_filter = ['is_active', 'is_system_template', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['usage_count', 'created_at', 'updated_at', 'permission_count']
    filter_horizontal = ['permissions']

    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'description')
        }),
        ('Template Configuration', {
            'fields': ('permissions', 'is_active', 'is_system_template')
        }),
        ('Usage Statistics', {
            'fields': ('usage_count', 'permission_count'),
            'classes': ('collapse',)
        }),
        ('Audit Information', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def permission_count(self, obj):
        """Display number of permissions in this template."""
        count = obj.permissions.count()
        return format_html(
            '<span style="color: blue; font-weight: bold;">{}</span>',
            count
        )
    permission_count.short_description = 'Permissions'


@admin.register(RoleAuditLog)
class RoleAuditLogAdmin(admin.ModelAdmin):
    """
    Admin interface for RoleAuditLog model.
    """
    list_display = [
        'action_type', 'performed_by', 'target_user',
        'target_role', 'school', 'created_at'
    ]
    list_filter = [
        'action_type', 'school', 'created_at'
    ]
    search_fields = [
        'description', 'performed_by__email', 'target_user__email',
        'target_role__name'
    ]
    readonly_fields = [
        'action_type', 'description', 'performed_by', 'target_user',
        'target_role', 'target_permission', 'school', 'ip_address',
        'user_agent', 'metadata', 'created_at'
    ]

    fieldsets = (
        ('Action Details', {
            'fields': ('action_type', 'description', 'created_at')
        }),
        ('Actor Information', {
            'fields': ('performed_by', 'ip_address', 'user_agent')
        }),
        ('Target Information', {
            'fields': ('target_user', 'target_role', 'target_permission', 'school')
        }),
        ('Additional Data', {
            'fields': ('metadata',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'performed_by', 'target_user', 'target_role', 'target_permission', 'school'
        )

    def has_add_permission(self, request):
        """Audit logs should not be manually created."""
        return False

    def has_change_permission(self, request, obj=None):
        """Audit logs should not be modified."""
        return False

    def has_delete_permission(self, request, obj=None):
        """Audit logs should not be deleted."""
        return False
