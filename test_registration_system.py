#!/usr/bin/env python
"""
Test the student registration number system.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_backend.settings')
django.setup()

from schools.models import School
from users.models import User
from django.contrib.auth.hashers import make_password

def test_registration_system():
    """Test the complete student registration number system"""
    print("=== STUDENT REGISTRATION NUMBER SYSTEM TEST ===\n")
    
    # Test 1: Show current students and their registration numbers
    print("--- Current Students and Registration Numbers ---")
    students = User.objects.filter(user_type='student').select_related('school')
    
    schools_data = {}
    for student in students:
        school_name = student.school.name if student.school else 'No School'
        if school_name not in schools_data:
            schools_data[school_name] = []
        schools_data[school_name].append(student)
    
    for school_name, school_students in schools_data.items():
        print(f"🏫 {school_name}:")
        for student in school_students:
            print(f"  📋 {student.student_id}: {student.get_full_name()} ({student.email})")
        print()
    
    # Test 2: Show registration number patterns
    print("--- Registration Number Patterns ---")
    for school in School.objects.all():
        school_students = User.objects.filter(school=school, user_type='student')
        reg_numbers = [s.student_id for s in school_students if s.student_id]
        
        print(f"🏫 {school.name} ({school.slug}):")
        print(f"  📊 Total Students: {school_students.count()}")
        print(f"  🔢 Registration Numbers: {', '.join(sorted(reg_numbers))}")
        
        # Show pattern analysis
        if reg_numbers:
            # Extract school code
            school_code = reg_numbers[0][:2] if reg_numbers else 'N/A'
            print(f"  🏷️  School Code: {school_code}")
            
            # Extract years
            years = set()
            sequences = []
            for reg_num in reg_numbers:
                if len(reg_num) >= 7:
                    year = reg_num[2:6]
                    sequence = reg_num[6:]
                    years.add(year)
                    sequences.append(int(sequence))
            
            print(f"  📅 Years: {', '.join(sorted(years))}")
            print(f"  🔢 Sequences: {', '.join(map(str, sorted(sequences)))}")
        print()
    
    # Test 3: Test next available registration numbers
    print("--- Next Available Registration Numbers ---")
    for school in School.objects.all():
        try:
            temp_student = User(school=school, user_type='student')
            next_reg_num = temp_student.generate_student_registration_number()
            print(f"🏫 {school.name}: Next available → {next_reg_num}")
        except Exception as e:
            print(f"🏫 {school.name}: Error → {str(e)}")
    print()
    
    # Test 4: Create a new student and show auto-assignment
    print("--- Testing Auto-Assignment for New Student ---")
    try:
        greenwood = School.objects.get(slug='greenwood-high')
        
        # Create a new student
        new_student = User(
            email='<EMAIL>',
            password=make_password('test123'),
            user_type='student',
            school=greenwood,
            first_name='Test',
            last_name='Student'
        )
        
        print(f"Creating new student: {new_student.get_full_name()}")
        print(f"Before save - Registration Number: {new_student.student_id}")
        
        new_student.save()
        
        print(f"After save - Registration Number: {new_student.student_id}")
        print(f"✅ Auto-assignment successful!")
        
        # Clean up - delete the test student
        new_student.delete()
        print("🧹 Test student cleaned up")
        
    except Exception as e:
        print(f"❌ Error testing auto-assignment: {str(e)}")
    
    print()
    
    # Test 5: Show registration number validation
    print("--- Registration Number Validation ---")
    
    # Test unique constraint
    print("Testing uniqueness constraint...")
    try:
        greenwood = School.objects.get(slug='greenwood-high')
        existing_student = User.objects.filter(school=greenwood, user_type='student').first()
        
        if existing_student:
            print(f"Existing registration number: {existing_student.student_id}")
            
            # Try to create another student with the same registration number
            duplicate_student = User(
                email='<EMAIL>',
                password=make_password('test123'),
                user_type='student',
                school=greenwood,
                first_name='Duplicate',
                last_name='Student',
                student_id=existing_student.student_id  # Same registration number
            )
            
            try:
                duplicate_student.save()
                print("❌ Uniqueness constraint failed - duplicate was allowed!")
                duplicate_student.delete()
            except Exception as e:
                print(f"✅ Uniqueness constraint working: {str(e)}")
        
    except Exception as e:
        print(f"Error testing uniqueness: {str(e)}")
    
    print()
    
    # Test 6: Summary and statistics
    print("--- REGISTRATION SYSTEM SUMMARY ---")
    total_students = User.objects.filter(user_type='student').count()
    students_with_reg = User.objects.filter(user_type='student', student_id__isnull=False).exclude(student_id='').count()
    
    print(f"📊 Total Students: {total_students}")
    print(f"📋 Students with Registration Numbers: {students_with_reg}")
    print(f"📈 Coverage: {(students_with_reg/total_students*100):.1f}%" if total_students > 0 else "📈 Coverage: 0%")
    
    print("\n🎉 REGISTRATION NUMBER SYSTEM TEST COMPLETE!")
    print("\n✅ Features Verified:")
    print("  • Auto-generation of unique registration numbers")
    print("  • School-specific codes (GW, RA)")
    print("  • Year-based numbering (2025)")
    print("  • Sequential numbering within each school")
    print("  • Uniqueness constraints per school")
    print("  • Automatic assignment for new students")
    
    return True

if __name__ == '__main__':
    test_registration_system()
