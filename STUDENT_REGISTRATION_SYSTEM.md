# Student Registration Number System

## Overview

The SMS project now includes a comprehensive **Student Registration Number System** that automatically generates unique registration numbers for each student within their school. This system ensures proper student identification and maintains data integrity across the multi-tenant architecture.

## Features

### ✅ **Auto-Generation**
- Automatic registration number assignment when creating new students
- No manual intervention required
- Guaranteed uniqueness within each school

### ✅ **School-Specific Codes**
- Each school gets a unique 2-letter code
- Greenwood High School: **GW**
- Riverside Academy: **RA**
- Extensible for new schools

### ✅ **Year-Based Numbering**
- Registration numbers include the current academic year
- Format: `{SCHOOL_CODE}{YEAR}{SEQUENCE}`
- Example: `*********`, `*********`

### ✅ **Sequential Numbering**
- Sequential 3-digit numbers within each school and year
- Automatically finds the next available number
- Handles gaps in sequences intelligently

### ✅ **Database Constraints**
- Unique constraint per school ensures no duplicates
- Proper validation at the database level
- Multi-tenant data isolation

## Registration Number Format

```
{SCHOOL_CODE}{YEAR}{SEQUENCE}
     GW      2025    001
     ↓        ↓       ↓
  School   Year   Student
   Code           Number
```

### Examples
- ************* - First student at Greenwood High in 2025
- ************* - Second student at Greenwood High in 2025
- ************* - First student at Riverside Academy in 2025

## Current Students

### 🏫 Greenwood High School (GW)
- *************: Emily Davis (<EMAIL>)
- *************: John Wilson (<EMAIL>)
- *************: Sarah Thompson (<EMAIL>)

### 🏫 Riverside Academy (RA)
- *************: Alex Martinez (<EMAIL>)
- *************: Maria Garcia (<EMAIL>)

## Usage

### Creating New Students

When creating a new student, the registration number is automatically assigned:

```python
from users.models import User
from schools.models import School

# Get the school
school = School.objects.get(slug='greenwood-high')

# Create a new student
student = User.objects.create(
    email='<EMAIL>',
    user_type='student',
    school=school,
    first_name='New',
    last_name='Student'
)

# Registration number is automatically assigned
print(student.student_id)  # Output: GW2025003
```

### Manual Registration Number Generation

You can also manually generate registration numbers:

```python
# Generate a registration number without saving
student = User(school=school, user_type='student')
reg_number = student.generate_student_registration_number()
print(reg_number)  # Output: GW2025003

# Assign registration number to existing student
student.assign_registration_number()
```

## Management Commands

### Generate Registration Numbers

Use the management command to generate registration numbers for existing students:

```bash
# Generate for all students without registration numbers
python manage.py generate_registration_numbers

# Generate for a specific school
python manage.py generate_registration_numbers --school=greenwood-high

# Force regeneration for all students
python manage.py generate_registration_numbers --force

# Dry run to see what would be generated
python manage.py generate_registration_numbers --dry-run
```

### Example Output
```
=== Student Registration Number Generator ===

Processing students without registration numbers...
Found 2 students to process

🏫 Greenwood High School:
  ✅ Generated 1 new registration numbers
    - John Wilson: *********

🏫 Riverside Academy:
  ✅ Generated 1 new registration numbers
    - Maria Garcia: *********

✅ COMPLETED - 2 students processed successfully

=== NEXT AVAILABLE REGISTRATION NUMBERS ===
🏫 Greenwood High School: Next available - GW2025003
🏫 Riverside Academy: Next available - *********
```

## School Code Generation

The system automatically generates school codes based on the school's slug:

- **greenwood-high** → **GW** (Greenwood)
- **riverside-academy** → **RA** (Riverside Academy)
- **new-school-name** → **NS** (First letter of each word)

## Database Schema

The registration numbers are stored in the `student_id` field of the `users` table:

```sql
-- Unique constraint ensures no duplicates per school
CONSTRAINT unique_student_id_per_school 
UNIQUE (school_id, student_id)
```

## API Integration

Registration numbers are included in all user API responses:

```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "student_id": "*********",
  "first_name": "John",
  "last_name": "Wilson",
  "school": "greenwood-high",
  "user_type": "student"
}
```

## Supabase Sync

Registration numbers are automatically synced to Supabase:

```bash
# Sync all data including registration numbers
python manage.py sync_supabase --direction=push
```

## Testing

Run the comprehensive test to verify the registration system:

```bash
python test_registration_system.py
```

## Next Steps

1. **Frontend Integration**: Display registration numbers in student profiles
2. **Bulk Import**: Add registration number generation to bulk student import
3. **Reports**: Generate reports by registration number ranges
4. **Barcode/QR**: Generate barcodes or QR codes for student IDs
5. **ID Cards**: Integrate with ID card printing systems

## Benefits

- **🔒 Unique Identification**: Each student has a unique identifier within their school
- **📊 Easy Tracking**: Simple format for administrative tracking
- **🏫 Multi-tenant Safe**: No conflicts between different schools
- **📅 Year-based**: Easy to identify student cohorts by year
- **🔄 Automatic**: No manual work required for assignment
- **📈 Scalable**: Supports unlimited students per school
- **🛡️ Validated**: Database constraints prevent duplicates

The Student Registration Number System is now fully operational and ready for production use!
