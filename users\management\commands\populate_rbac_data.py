"""
Management command to populate initial RBAC data including permissions, roles, and role templates.
"""

from django.core.management.base import BaseCommand
from django.contrib.contenttypes.models import ContentType
from django.db import transaction
from users.models import Permission, Role, RoleTemplate, RolePermission, User
from schools.models import School
from courses.models import Course


class Command(BaseCommand):
    help = 'Populate initial RBAC data including permissions, roles, and templates'

    def add_arguments(self, parser):
        parser.add_argument(
            '--reset',
            action='store_true',
            help='Reset all RBAC data before populating',
        )

    def handle(self, *args, **options):
        if options['reset']:
            self.stdout.write('Resetting RBAC data...')
            self.reset_rbac_data()

        self.stdout.write('Populating RBAC data...')
        
        with transaction.atomic():
            self.create_permissions()
            self.create_system_roles()
            self.create_role_templates()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully populated RBAC data')
        )

    def reset_rbac_data(self):
        """Reset all RBAC data"""
        RolePermission.objects.all().delete()
        Role.objects.all().delete()
        RoleTemplate.objects.all().delete()
        Permission.objects.all().delete()
        self.stdout.write('RBAC data reset complete')

    def create_permissions(self):
        """Create system permissions"""
        self.stdout.write('Creating permissions...')
        
        # Get content types
        user_ct = ContentType.objects.get_for_model(User)
        school_ct = ContentType.objects.get_for_model(School)
        course_ct = ContentType.objects.get_for_model(Course)
        
        permissions_data = [
            # Academic Management Permissions
            {
                'codename': 'can_view_grades',
                'name': 'Can View Grades',
                'description': 'Permission to view student grades',
                'category': 'academic',
                'content_type': course_ct,
            },
            {
                'codename': 'can_edit_grades',
                'name': 'Can Edit Grades',
                'description': 'Permission to edit and update student grades',
                'category': 'academic',
                'content_type': course_ct,
            },
            {
                'codename': 'can_upload_materials',
                'name': 'Can Upload Course Materials',
                'description': 'Permission to upload course materials and resources',
                'category': 'academic',
                'content_type': course_ct,
            },
            {
                'codename': 'can_delete_materials',
                'name': 'Can Delete Course Materials',
                'description': 'Permission to delete course materials',
                'category': 'academic',
                'content_type': course_ct,
            },
            {
                'codename': 'can_create_courses',
                'name': 'Can Create Courses',
                'description': 'Permission to create new courses',
                'category': 'academic',
                'content_type': course_ct,
            },
            {
                'codename': 'can_manage_enrollments',
                'name': 'Can Manage Course Enrollments',
                'description': 'Permission to enroll/unenroll students in courses',
                'category': 'academic',
                'content_type': course_ct,
            },
            
            # User Management Permissions
            {
                'codename': 'can_view_users',
                'name': 'Can View Users',
                'description': 'Permission to view user profiles and information',
                'category': 'users',
                'content_type': user_ct,
            },
            {
                'codename': 'can_create_users',
                'name': 'Can Create Users',
                'description': 'Permission to create new user accounts',
                'category': 'users',
                'content_type': user_ct,
            },
            {
                'codename': 'can_edit_users',
                'name': 'Can Edit Users',
                'description': 'Permission to edit user profiles and information',
                'category': 'users',
                'content_type': user_ct,
            },
            {
                'codename': 'can_delete_users',
                'name': 'Can Delete Users',
                'description': 'Permission to delete user accounts',
                'category': 'users',
                'content_type': user_ct,
            },
            {
                'codename': 'can_assign_roles',
                'name': 'Can Assign Roles',
                'description': 'Permission to assign roles to users',
                'category': 'users',
                'content_type': user_ct,
            },
            {
                'codename': 'can_manage_permissions',
                'name': 'Can Manage Permissions',
                'description': 'Permission to manage user permissions and roles',
                'category': 'users',
                'content_type': user_ct,
            },
            
            # Administrative Permissions
            {
                'codename': 'can_view_reports',
                'name': 'Can View Reports',
                'description': 'Permission to view administrative reports',
                'category': 'admin',
                'content_type': school_ct,
            },
            {
                'codename': 'can_export_data',
                'name': 'Can Export Data',
                'description': 'Permission to export school data',
                'category': 'admin',
                'content_type': school_ct,
            },
            {
                'codename': 'can_manage_school_settings',
                'name': 'Can Manage School Settings',
                'description': 'Permission to manage school configuration and settings',
                'category': 'admin',
                'content_type': school_ct,
            },
            {
                'codename': 'can_view_audit_logs',
                'name': 'Can View Audit Logs',
                'description': 'Permission to view system audit logs',
                'category': 'admin',
                'content_type': None,
            },
            {
                'codename': 'can_manage_notifications',
                'name': 'Can Manage Notifications',
                'description': 'Permission to send and manage notifications',
                'category': 'communication',
                'content_type': None,
            },
            
            # System Permissions
            {
                'codename': 'can_access_admin',
                'name': 'Can Access Admin Panel',
                'description': 'Permission to access Django admin interface',
                'category': 'system',
                'content_type': None,
            },
            {
                'codename': 'can_manage_schools',
                'name': 'Can Manage Schools',
                'description': 'Permission to manage multiple schools (super admin)',
                'category': 'system',
                'content_type': school_ct,
            },
            {
                'codename': 'can_view_system_logs',
                'name': 'Can View System Logs',
                'description': 'Permission to view system-wide logs',
                'category': 'system',
                'content_type': None,
            },
            {
                'codename': 'can_manage_system_settings',
                'name': 'Can Manage System Settings',
                'description': 'Permission to manage system-wide settings',
                'category': 'system',
                'content_type': None,
            },
            
            # File Management Permissions
            {
                'codename': 'can_upload_files',
                'name': 'Can Upload Files',
                'description': 'Permission to upload files to the system',
                'category': 'files',
                'content_type': None,
            },
            {
                'codename': 'can_delete_files',
                'name': 'Can Delete Files',
                'description': 'Permission to delete files from the system',
                'category': 'files',
                'content_type': None,
            },
            {
                'codename': 'can_manage_file_storage',
                'name': 'Can Manage File Storage',
                'description': 'Permission to manage file storage and quotas',
                'category': 'files',
                'content_type': None,
            },
            
            # Reports & Analytics Permissions
            {
                'codename': 'can_view_analytics',
                'name': 'Can View Analytics',
                'description': 'Permission to view system analytics and statistics',
                'category': 'reports',
                'content_type': None,
            },
            {
                'codename': 'can_generate_reports',
                'name': 'Can Generate Reports',
                'description': 'Permission to generate custom reports',
                'category': 'reports',
                'content_type': None,
            },
            {
                'codename': 'can_schedule_reports',
                'name': 'Can Schedule Reports',
                'description': 'Permission to schedule automated reports',
                'category': 'reports',
                'content_type': None,
            },
        ]
        
        created_count = 0
        for perm_data in permissions_data:
            permission, created = Permission.objects.get_or_create(
                codename=perm_data['codename'],
                defaults=perm_data
            )
            if created:
                created_count += 1
        
        self.stdout.write(f'Created {created_count} permissions')

    def create_system_roles(self):
        """Create system-wide roles"""
        self.stdout.write('Creating system roles...')
        
        # Super Admin Role
        super_admin_role, created = Role.objects.get_or_create(
            name='Super Administrator',
            school=None,
            defaults={
                'description': 'System-wide administrator with full access',
                'role_type': 'system',
                'is_system_role': True,
                'is_active': True,
            }
        )
        
        if created:
            # Assign all permissions to super admin
            all_permissions = Permission.objects.all()
            for permission in all_permissions:
                RolePermission.objects.get_or_create(
                    role=super_admin_role,
                    permission=permission,
                    defaults={'granted': True}
                )
            self.stdout.write('Created Super Administrator role with all permissions')

    def create_role_templates(self):
        """Create role templates for common positions"""
        self.stdout.write('Creating role templates...')
        
        templates_data = [
            {
                'name': 'Standard Lecturer',
                'description': 'Template for regular teaching staff',
                'permissions': [
                    'can_view_grades', 'can_edit_grades', 'can_upload_materials',
                    'can_view_users', 'can_manage_enrollments'
                ]
            },
            {
                'name': 'Head of Department',
                'description': 'Template for department heads',
                'permissions': [
                    'can_view_grades', 'can_edit_grades', 'can_upload_materials',
                    'can_delete_materials', 'can_create_courses', 'can_manage_enrollments',
                    'can_view_users', 'can_edit_users', 'can_view_reports'
                ]
            },
            {
                'name': 'School Administrator',
                'description': 'Template for school-level administrators',
                'permissions': [
                    'can_view_users', 'can_create_users', 'can_edit_users',
                    'can_assign_roles', 'can_view_reports', 'can_export_data',
                    'can_manage_school_settings', 'can_view_audit_logs',
                    'can_access_admin'
                ]
            },
            {
                'name': 'Student',
                'description': 'Template for student users',
                'permissions': [
                    'can_view_grades', 'can_upload_files'
                ]
            },
        ]
        
        created_count = 0
        for template_data in templates_data:
            template, created = RoleTemplate.objects.get_or_create(
                name=template_data['name'],
                defaults={
                    'description': template_data['description'],
                    'is_system_template': True,
                }
            )
            
            if created:
                # Add permissions to template
                permissions = Permission.objects.filter(
                    codename__in=template_data['permissions']
                )
                template.permissions.set(permissions)
                created_count += 1
        
        self.stdout.write(f'Created {created_count} role templates')
