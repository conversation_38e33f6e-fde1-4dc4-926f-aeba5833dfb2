{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}{{ title }} | {{ site_title|default:"Django site admin" }}{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
.role-hierarchy {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.role-node {
    margin: 10px 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
    position: relative;
}

.role-node.level-0 {
    background: #e3f2fd;
    border-color: #2196f3;
}

.role-node.level-1 {
    background: #f3e5f5;
    border-color: #9c27b0;
    margin-left: 30px;
}

.role-node.level-2 {
    background: #e8f5e8;
    border-color: #4caf50;
    margin-left: 60px;
}

.role-node.level-3 {
    background: #fff3e0;
    border-color: #ff9800;
    margin-left: 90px;
}

.role-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.role-name {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.role-type {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.role-type.system {
    background: #ff5722;
    color: white;
}

.role-type.school {
    background: #2196f3;
    color: white;
}

.role-type.custom {
    background: #4caf50;
    color: white;
}

.role-stats {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    background: white;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.stat-number {
    font-weight: bold;
    color: #2196f3;
}

.role-description {
    margin-top: 10px;
    color: #666;
    font-style: italic;
}

.school-info {
    color: #666;
    font-size: 14px;
}

.hierarchy-summary {
    background: #f5f5f5;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.summary-stats {
    display: flex;
    gap: 30px;
    justify-content: center;
}

.summary-stat {
    text-align: center;
}

.summary-number {
    font-size: 24px;
    font-weight: bold;
    color: #2196f3;
}

.summary-label {
    font-size: 14px;
    color: #666;
}

.expand-toggle {
    cursor: pointer;
    color: #2196f3;
    font-size: 12px;
    margin-left: 10px;
}

.children-container {
    margin-top: 15px;
    border-left: 2px solid #ddd;
    padding-left: 15px;
}
</style>
{% endblock %}

{% block content %}
<div class="role-hierarchy">
    <h1>{{ title }}</h1>
    
    <div class="hierarchy-summary">
        <div class="summary-stats">
            <div class="summary-stat">
                <div class="summary-number">{{ total_roles }}</div>
                <div class="summary-label">Total Roles</div>
            </div>
        </div>
    </div>
    
    <div class="hierarchy-tree">
        {% for node in hierarchy %}
            {% include "admin/users/role_node.html" with node=node %}
        {% empty %}
            <p>No roles found.</p>
        {% endfor %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add expand/collapse functionality
    document.querySelectorAll('.expand-toggle').forEach(function(toggle) {
        toggle.addEventListener('click', function() {
            const container = this.parentElement.nextElementSibling;
            if (container && container.classList.contains('children-container')) {
                if (container.style.display === 'none') {
                    container.style.display = 'block';
                    this.textContent = '[-]';
                } else {
                    container.style.display = 'none';
                    this.textContent = '[+]';
                }
            }
        });
    });
});
</script>
{% endblock %}
