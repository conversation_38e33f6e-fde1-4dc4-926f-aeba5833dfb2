"""
Custom admin views for enhanced RBAC management.
"""

from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.db.models import Count, Q, Prefetch
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView
from django.contrib import messages
from django.urls import reverse
from django.utils.html import format_html
import json
from .models import Role, Permission, User, UserRole, RolePermission, RoleAuditLog
from .rbac_utils import RBACManager


@method_decorator(staff_member_required, name='dispatch')
class RoleHierarchyView(TemplateView):
    """
    View for displaying role hierarchy in a tree structure.
    """
    template_name = 'admin/users/role_hierarchy.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get all roles with their relationships
        roles = Role.objects.filter(is_active=True).select_related(
            'parent_role', 'school', 'created_by'
        ).prefetch_related('child_roles')
        
        # Build hierarchy tree
        hierarchy = self.build_hierarchy_tree(roles)
        
        context.update({
            'title': 'Role Hierarchy',
            'hierarchy': hierarchy,
            'total_roles': roles.count(),
        })
        
        return context
    
    def build_hierarchy_tree(self, roles):
        """Build a hierarchical tree structure from roles."""
        role_dict = {role.id: role for role in roles}
        tree = []
        
        # Find root roles (no parent)
        for role in roles:
            if not role.parent_role:
                tree.append(self.build_role_node(role, role_dict))
        
        return tree
    
    def build_role_node(self, role, role_dict, level=0):
        """Build a single node in the hierarchy tree."""
        children = []
        for child_role in role.child_roles.filter(is_active=True):
            children.append(self.build_role_node(child_role, role_dict, level + 1))
        
        return {
            'role': role,
            'children': children,
            'level': level,
            'user_count': role.user_roles.filter(is_active=True).count(),
            'permission_count': role.role_permissions.filter(granted=True).count(),
        }


@method_decorator(staff_member_required, name='dispatch')
class PermissionMatrixView(TemplateView):
    """
    View for displaying permission matrix (roles vs permissions).
    """
    template_name = 'admin/users/permission_matrix.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get all active roles and permissions
        roles = Role.objects.filter(is_active=True).select_related('school')
        permissions = Permission.objects.all().select_related('content_type')
        
        # Get role permissions mapping
        role_permissions = RolePermission.objects.filter(
            role__in=roles,
            granted=True
        ).select_related('role', 'permission')
        
        # Build permission matrix
        matrix = self.build_permission_matrix(roles, permissions, role_permissions)
        
        # Group permissions by category
        permission_categories = {}
        for permission in permissions:
            category = permission.category
            if category not in permission_categories:
                permission_categories[category] = []
            permission_categories[category].append(permission)
        
        context.update({
            'title': 'Permission Matrix',
            'roles': roles,
            'permissions': permissions,
            'permission_categories': permission_categories,
            'matrix': matrix,
        })
        
        return context
    
    def build_permission_matrix(self, roles, permissions, role_permissions):
        """Build the permission matrix data structure."""
        # Create mapping for quick lookup
        rp_map = {}
        for rp in role_permissions:
            key = f"{rp.role_id}_{rp.permission_id}"
            rp_map[key] = rp
        
        matrix = {}
        for role in roles:
            matrix[role.id] = {}
            for permission in permissions:
                key = f"{role.id}_{permission.id}"
                matrix[role.id][permission.id] = rp_map.get(key)
        
        return matrix


@method_decorator(staff_member_required, name='dispatch')
class RoleAnalyticsView(TemplateView):
    """
    View for role usage analytics and statistics.
    """
    template_name = 'admin/users/role_analytics.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Role usage statistics
        role_stats = Role.objects.filter(is_active=True).annotate(
            user_count=Count('user_roles', filter=Q(user_roles__is_active=True)),
            permission_count=Count('role_permissions', filter=Q(role_permissions__granted=True))
        ).order_by('-user_count')
        
        # Permission usage statistics
        permission_stats = Permission.objects.annotate(
            role_count=Count('role_permissions', filter=Q(role_permissions__granted=True))
        ).order_by('-role_count')
        
        # User type distribution
        user_type_stats = User.objects.filter(is_active=True).values('user_type').annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Recent activity trends
        recent_activity = RoleAuditLog.objects.values('action_type').annotate(
            count=Count('id')
        ).order_by('-count')[:10]
        
        # School-wise role distribution
        school_role_stats = Role.objects.filter(
            is_active=True,
            school__isnull=False
        ).values('school__name').annotate(
            role_count=Count('id'),
            user_count=Count('user_roles', filter=Q(user_roles__is_active=True))
        ).order_by('-role_count')
        
        context.update({
            'title': 'Role Analytics',
            'role_stats': role_stats[:20],  # Top 20 roles
            'permission_stats': permission_stats[:20],  # Top 20 permissions
            'user_type_stats': user_type_stats,
            'recent_activity': recent_activity,
            'school_role_stats': school_role_stats,
            'total_users': User.objects.filter(is_active=True).count(),
            'total_roles': Role.objects.filter(is_active=True).count(),
            'total_permissions': Permission.objects.count(),
        })
        
        return context


@staff_member_required
def role_hierarchy_json(request):
    """
    JSON endpoint for role hierarchy data (for JavaScript tree widgets).
    """
    roles = Role.objects.filter(is_active=True).select_related(
        'parent_role', 'school'
    ).prefetch_related('child_roles')
    
    def build_json_node(role):
        return {
            'id': str(role.id),
            'text': role.name,
            'school': role.school.name if role.school else 'System',
            'user_count': role.user_roles.filter(is_active=True).count(),
            'permission_count': role.role_permissions.filter(granted=True).count(),
            'children': [build_json_node(child) for child in role.child_roles.filter(is_active=True)]
        }
    
    # Get root roles
    root_roles = roles.filter(parent_role__isnull=True)
    hierarchy = [build_json_node(role) for role in root_roles]
    
    return JsonResponse(hierarchy, safe=False)


@staff_member_required
def permission_matrix_json(request):
    """
    JSON endpoint for permission matrix data.
    """
    roles = Role.objects.filter(is_active=True).select_related('school')
    permissions = Permission.objects.all().select_related('content_type')
    
    role_permissions = RolePermission.objects.filter(
        role__in=roles,
        granted=True
    ).values_list('role_id', 'permission_id')
    
    # Build matrix
    rp_set = set(role_permissions)
    matrix = []
    
    for role in roles:
        role_data = {
            'role_id': str(role.id),
            'role_name': role.name,
            'school': role.school.name if role.school else 'System',
            'permissions': []
        }
        
        for permission in permissions:
            has_permission = (role.id, permission.id) in rp_set
            role_data['permissions'].append({
                'permission_id': str(permission.id),
                'permission_name': permission.name,
                'category': permission.category,
                'granted': has_permission
            })
        
        matrix.append(role_data)
    
    return JsonResponse({
        'roles': matrix,
        'permission_categories': list(permissions.values_list('category', flat=True).distinct())
    })


@staff_member_required
def bulk_role_assignment(request):
    """
    View for bulk role assignment operations.
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            user_ids = data.get('user_ids', [])
            role_id = data.get('role_id')
            action = data.get('action', 'assign')  # assign or remove
            
            if not user_ids or not role_id:
                return JsonResponse({'error': 'Missing required parameters'}, status=400)
            
            role = get_object_or_404(Role, id=role_id)
            users = User.objects.filter(id__in=user_ids)
            
            results = []
            for user in users:
                try:
                    if action == 'assign':
                        user_role = RBACManager.assign_role_to_user(
                            user=user,
                            role=role,
                            assigned_by=request.user
                        )
                        results.append({
                            'user_id': str(user.id),
                            'status': 'success',
                            'message': f'Role assigned to {user.get_full_name()}'
                        })
                    elif action == 'remove':
                        success = RBACManager.remove_role_from_user(
                            user=user,
                            role=role,
                            removed_by=request.user
                        )
                        if success:
                            results.append({
                                'user_id': str(user.id),
                                'status': 'success',
                                'message': f'Role removed from {user.get_full_name()}'
                            })
                        else:
                            results.append({
                                'user_id': str(user.id),
                                'status': 'error',
                                'message': f'User does not have this role'
                            })
                except Exception as e:
                    results.append({
                        'user_id': str(user.id),
                        'status': 'error',
                        'message': str(e)
                    })
            
            return JsonResponse({'results': results})
            
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    
    # GET request - show bulk assignment form
    roles = Role.objects.filter(is_active=True).select_related('school')
    users = User.objects.filter(is_active=True).select_related('school')
    
    return render(request, 'admin/users/bulk_role_assignment.html', {
        'title': 'Bulk Role Assignment',
        'roles': roles,
        'users': users,
    })


@staff_member_required
def role_comparison(request):
    """
    View for comparing roles and their permissions.
    """
    role_ids = request.GET.getlist('roles')
    
    if not role_ids:
        # Show role selection form
        roles = Role.objects.filter(is_active=True).select_related('school')
        return render(request, 'admin/users/role_comparison_select.html', {
            'title': 'Role Comparison',
            'roles': roles,
        })
    
    # Get selected roles with their permissions
    roles = Role.objects.filter(
        id__in=role_ids,
        is_active=True
    ).select_related('school').prefetch_related(
        Prefetch(
            'role_permissions',
            queryset=RolePermission.objects.filter(granted=True).select_related('permission')
        )
    )
    
    # Build comparison matrix
    all_permissions = set()
    role_permissions = {}
    
    for role in roles:
        role_perms = set()
        for rp in role.role_permissions.all():
            role_perms.add(rp.permission)
            all_permissions.add(rp.permission)
        role_permissions[role.id] = role_perms
    
    # Sort permissions by category
    sorted_permissions = sorted(all_permissions, key=lambda p: (p.category, p.name))
    
    return render(request, 'admin/users/role_comparison.html', {
        'title': 'Role Comparison',
        'roles': roles,
        'permissions': sorted_permissions,
        'role_permissions': role_permissions,
    })
