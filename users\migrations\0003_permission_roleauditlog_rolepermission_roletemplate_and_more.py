# Generated by Django 4.2.7 on 2025-07-25 02:55

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("schools", "0001_initial"),
        ("contenttypes", "0002_remove_content_type_name"),
        ("users", "0002_user_gender_user_nid_user_users_nid_21ddb9_idx_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="Permission",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "codename",
                    models.CharField(
                        help_text="Unique permission identifier (e.g., 'can_upload_materials')",
                        max_length=100,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message="Codename must contain only lowercase letters and underscores",
                                regex="^[a-z_]+$",
                            )
                        ],
                    ),
                ),
                (
                    "name",
                    models.Char<PERSON>ield(
                        help_text="Human-readable permission name", max_length=255
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        help_text="Detailed description of what this permission allows",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("academic", "Academic Management"),
                            ("users", "User Management"),
                            ("admin", "Administrative"),
                            ("system", "System Management"),
                            ("reports", "Reports & Analytics"),
                            ("communication", "Communication"),
                            ("files", "File Management"),
                        ],
                        help_text="Permission category for organization",
                        max_length=20,
                    ),
                ),
                (
                    "is_system_permission",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this is a system-defined permission",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "permissions",
                "ordering": ["category", "name"],
            },
        ),
        migrations.CreateModel(
            name="RoleAuditLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("role_created", "Role Created"),
                            ("role_updated", "Role Updated"),
                            ("role_deleted", "Role Deleted"),
                            ("permission_granted", "Permission Granted"),
                            ("permission_revoked", "Permission Revoked"),
                            ("user_role_assigned", "User Role Assigned"),
                            ("user_role_removed", "User Role Removed"),
                            ("role_approved", "Role Assignment Approved"),
                            ("role_denied", "Role Assignment Denied"),
                        ],
                        help_text="Type of action performed",
                        max_length=20,
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Detailed description of the action"),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True,
                        help_text="IP address of the user performing the action",
                        null=True,
                    ),
                ),
                (
                    "user_agent",
                    models.TextField(blank=True, help_text="User agent of the client"),
                ),
                (
                    "metadata",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional metadata about the action",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "role_audit_logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="RolePermission",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "granted",
                    models.BooleanField(
                        default=True,
                        help_text="Whether permission is granted (True) or denied (False)",
                    ),
                ),
                ("granted_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="When this permission grant expires",
                        null=True,
                    ),
                ),
            ],
            options={
                "db_table": "role_permissions",
            },
        ),
        migrations.CreateModel(
            name="RoleTemplate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        help_text="Template name (e.g., 'Standard Lecturer')",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "description",
                    models.TextField(help_text="Description of the role template"),
                ),
                (
                    "is_active",
                    models.BooleanField(
                        default=True, help_text="Whether this template is active"
                    ),
                ),
                (
                    "is_system_template",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this is a system-provided template",
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Number of times this template has been used",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "db_table": "role_templates",
                "ordering": ["name"],
            },
        ),
        migrations.AddField(
            model_name="role",
            name="is_default",
            field=models.BooleanField(
                default=False, help_text="Whether this is a default role for new users"
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="parent_role",
            field=models.ForeignKey(
                blank=True,
                help_text="Parent role for hierarchy",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="child_roles",
                to="users.role",
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="role_type",
            field=models.CharField(
                choices=[
                    ("system", "System Role"),
                    ("school", "School Role"),
                    ("custom", "Custom Role"),
                ],
                default="school",
                help_text="Type of role",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="approved_at",
            field=models.DateTimeField(
                blank=True,
                help_text="When this role assignment was approved",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="approved_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who approved this role assignment",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="approved_user_roles",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="deactivated_at",
            field=models.DateTimeField(
                blank=True,
                help_text="When this role assignment was deactivated",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="deactivated_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who deactivated this role assignment",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="deactivated_user_roles",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="requires_approval",
            field=models.BooleanField(
                default=False,
                help_text="Whether this role assignment requires approval",
            ),
        ),
        migrations.AddField(
            model_name="userrole",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.RemoveField(
            model_name="role",
            name="permissions",
        ),
        migrations.AddIndex(
            model_name="userrole",
            index=models.Index(
                fields=["requires_approval", "approved_at"],
                name="user_roles_require_3b5f00_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="userrole",
            index=models.Index(
                fields=["valid_until"], name="user_roles_valid_u_6fdd0e_idx"
            ),
        ),
        migrations.AddField(
            model_name="roletemplate",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who created this template",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_role_templates",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="roletemplate",
            name="permissions",
            field=models.ManyToManyField(
                help_text="Permissions included in this template",
                related_name="role_templates",
                to="users.permission",
            ),
        ),
        migrations.AddField(
            model_name="rolepermission",
            name="granted_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who granted/denied this permission",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="granted_role_permissions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="rolepermission",
            name="permission",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="role_permissions",
                to="users.permission",
            ),
        ),
        migrations.AddField(
            model_name="rolepermission",
            name="role",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="role_permissions",
                to="users.role",
            ),
        ),
        migrations.AddField(
            model_name="roleauditlog",
            name="performed_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who performed the action",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="performed_role_actions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="roleauditlog",
            name="school",
            field=models.ForeignKey(
                blank=True,
                help_text="School context for the action",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="role_audit_logs",
                to="schools.school",
            ),
        ),
        migrations.AddField(
            model_name="roleauditlog",
            name="target_permission",
            field=models.ForeignKey(
                blank=True,
                help_text="Permission affected by the action",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="audit_logs",
                to="users.permission",
            ),
        ),
        migrations.AddField(
            model_name="roleauditlog",
            name="target_role",
            field=models.ForeignKey(
                blank=True,
                help_text="Role affected by the action",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="audit_logs",
                to="users.role",
            ),
        ),
        migrations.AddField(
            model_name="roleauditlog",
            name="target_user",
            field=models.ForeignKey(
                blank=True,
                help_text="User affected by the action",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="role_audit_logs",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="permission",
            name="content_type",
            field=models.ForeignKey(
                blank=True,
                help_text="Content type this permission applies to",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="custom_permissions",
                to="contenttypes.contenttype",
            ),
        ),
        migrations.AddField(
            model_name="permission",
            name="created_by",
            field=models.ForeignKey(
                blank=True,
                help_text="User who created this permission",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_permissions",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="role",
            name="permissions",
            field=models.ManyToManyField(
                blank=True,
                help_text="Permissions assigned to this role",
                related_name="assigned_roles",
                through="users.RolePermission",
                to="users.permission",
            ),
        ),
        migrations.AddIndex(
            model_name="roletemplate",
            index=models.Index(
                fields=["is_active"], name="role_templa_is_acti_5e640f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="roletemplate",
            index=models.Index(
                fields=["is_system_template"], name="role_templa_is_syst_5b7e8f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="roletemplate",
            index=models.Index(
                fields=["usage_count"], name="role_templa_usage_c_e42d93_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="rolepermission",
            index=models.Index(
                fields=["role", "granted"], name="role_permis_role_id_e5e6e3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="rolepermission",
            index=models.Index(
                fields=["permission", "granted"], name="role_permis_permiss_710d14_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="rolepermission",
            index=models.Index(
                fields=["expires_at"], name="role_permis_expires_19ad58_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="rolepermission",
            unique_together={("role", "permission")},
        ),
        migrations.AddIndex(
            model_name="roleauditlog",
            index=models.Index(
                fields=["action_type", "created_at"],
                name="role_audit__action__796107_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="roleauditlog",
            index=models.Index(
                fields=["performed_by", "created_at"],
                name="role_audit__perform_686758_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="roleauditlog",
            index=models.Index(
                fields=["target_user", "created_at"],
                name="role_audit__target__f44da4_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="roleauditlog",
            index=models.Index(
                fields=["school", "created_at"], name="role_audit__school__665e71_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="permission",
            index=models.Index(
                fields=["codename"], name="permissions_codenam_5f0327_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="permission",
            index=models.Index(
                fields=["category"], name="permissions_categor_b3ddb9_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="permission",
            index=models.Index(
                fields=["content_type"], name="permissions_content_889a3f_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="permission",
            index=models.Index(
                fields=["is_system_permission"], name="permissions_is_syst_88c09d_idx"
            ),
        ),
    ]
