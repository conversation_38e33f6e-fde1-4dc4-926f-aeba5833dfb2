"""
Multi-tenant API views with enhanced school isolation and cross-school functionality.
"""

import logging
from django.core.exceptions import PermissionDenied, ValidationError
from django.db.models import Q
from rest_framework import status, viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from .models import Role, UserRole, Permission
from .serializers import RoleSerializer, UserRoleSerializer, PermissionSerializer
from .decorators import api_permission_required, school_isolation_required
from .multi_tenant_managers import SchoolContextValidator
from .cross_school_utils import CrossSchoolRoleComparator
from .rbac_utils import RBACManager

logger = logging.getLogger(__name__)


class MultiTenantRoleViewSet(viewsets.ModelViewSet):
    """
    Enhanced Role ViewSet with multi-tenant isolation and cross-school functionality.
    """
    serializer_class = RoleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Return roles based on user's school context and permissions.
        """
        user = self.request.user
        
        # Super users can see all roles
        if user.is_superuser:
            return Role.objects.all().select_related('school')
        
        # Use multi-tenant manager for school-based filtering
        return Role.mt_objects.for_user_school(user).select_related('school')
    
    @school_isolation_required
    def list(self, request, *args, **kwargs):
        """List roles with school context validation."""
        return super().list(request, *args, **kwargs)
    
    @school_isolation_required
    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific role with school context validation."""
        role = self.get_object()
        
        # Validate user can access this role
        if not SchoolContextValidator.validate_role_school_access(request.user, role):
            raise PermissionDenied("Cannot access role from different school")
        
        return super().retrieve(request, *args, **kwargs)
    
    @api_permission_required('can_assign_roles')
    def create(self, request, *args, **kwargs):
        """Create a new role with multi-tenant validation."""
        user = request.user
        school_id = request.data.get('school')
        
        # Validate school context
        if school_id:
            from schools.models import School
            try:
                school = School.objects.get(id=school_id)
                
                # Non-super users can only create roles in their school
                if not user.is_superuser:
                    user_school = getattr(user, 'school', None)
                    if school != user_school:
                        raise PermissionDenied("Cannot create role for different school")
                
                # Use multi-tenant manager for creation
                role = Role.mt_objects.create_school_role(
                    name=request.data.get('name'),
                    school=school,
                    created_by=user,
                    description=request.data.get('description', ''),
                    role_type=request.data.get('role_type', 'school')
                )
                
                serializer = self.get_serializer(role)
                return Response(serializer.data, status=status.HTTP_201_CREATED)
                
            except School.DoesNotExist:
                return Response(
                    {'error': 'School not found'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            # Creating system role
            if not user.is_superuser:
                if not RBACManager.user_has_permission(user, 'can_manage_schools'):
                    raise PermissionDenied("Cannot create system roles")
            
            role = Role.mt_objects.create_system_role(
                name=request.data.get('name'),
                created_by=user,
                description=request.data.get('description', '')
            )
            
            serializer = self.get_serializer(role)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @api_permission_required('can_assign_roles')
    def update(self, request, *args, **kwargs):
        """Update role with multi-tenant validation."""
        role = self.get_object()
        
        # Validate user can modify this role
        if not SchoolContextValidator.validate_role_school_access(request.user, role):
            raise PermissionDenied("Cannot modify role from different school")
        
        return super().update(request, *args, **kwargs)
    
    @api_permission_required('can_assign_roles')
    def destroy(self, request, *args, **kwargs):
        """Delete role with multi-tenant validation."""
        role = self.get_object()
        
        # Validate user can delete this role
        if not SchoolContextValidator.validate_role_school_access(request.user, role):
            raise PermissionDenied("Cannot delete role from different school")
        
        # Check if role is in use
        active_assignments = UserRole.objects.filter(role=role, is_active=True).count()
        if active_assignments > 0:
            return Response(
                {
                    'error': 'Cannot delete role with active assignments',
                    'active_assignments': active_assignments
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)
    
    @action(detail=False, methods=['get'])
    @api_permission_required('can_view_all_schools')
    def cross_school_comparison(self, request):
        """
        Compare roles across schools (super admin only).
        """
        try:
            role_names = request.query_params.getlist('role_names[]')
            school_ids = request.query_params.getlist('school_ids[]')
            
            comparison_data = CrossSchoolRoleComparator.compare_roles_across_schools(
                user=request.user,
                role_names=role_names if role_names else None,
                school_ids=school_ids if school_ids else None
            )
            
            return Response(comparison_data, status=status.HTTP_200_OK)
            
        except PermissionDenied as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"Cross-school comparison error: {e}")
            return Response(
                {'error': 'Failed to generate comparison'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    @api_permission_required('can_view_all_schools')
    def school_distribution(self, request):
        """
        Get role distribution across schools (super admin only).
        """
        try:
            distribution_data = CrossSchoolRoleComparator.get_role_distribution_by_school(
                user=request.user
            )
            
            return Response(distribution_data, status=status.HTTP_200_OK)
            
        except PermissionDenied as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"School distribution error: {e}")
            return Response(
                {'error': 'Failed to get distribution data'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    @api_permission_required('can_view_all_schools')
    def permission_matrix(self, request):
        """
        Get permission matrix for a role across schools (super admin only).
        """
        try:
            role_name = request.query_params.get('role_name')
            if not role_name:
                return Response(
                    {'error': 'role_name parameter is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            school_ids = request.query_params.getlist('school_ids[]')
            
            matrix_data = CrossSchoolRoleComparator.get_role_permission_matrix(
                user=request.user,
                role_name=role_name,
                school_ids=school_ids if school_ids else None
            )
            
            return Response(matrix_data, status=status.HTTP_200_OK)
            
        except PermissionDenied as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"Permission matrix error: {e}")
            return Response(
                {'error': 'Failed to generate permission matrix'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    @api_permission_required('can_view_all_schools')
    def standardization_suggestions(self, request):
        """
        Get role standardization suggestions (super admin only).
        """
        try:
            role_name = request.query_params.get('role_name')
            if not role_name:
                return Response(
                    {'error': 'role_name parameter is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            suggestions = CrossSchoolRoleComparator.suggest_role_standardization(
                user=request.user,
                role_name=role_name
            )
            
            return Response(suggestions, status=status.HTTP_200_OK)
            
        except PermissionDenied as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"Standardization suggestions error: {e}")
            return Response(
                {'error': 'Failed to generate suggestions'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class MultiTenantUserRoleViewSet(viewsets.ModelViewSet):
    """
    Enhanced UserRole ViewSet with multi-tenant isolation.
    """
    serializer_class = UserRoleSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Return user role assignments based on school context.
        """
        user = self.request.user
        user_school = getattr(user, 'school', None)
        
        # Use multi-tenant manager for school-based filtering
        return UserRole.mt_objects.for_school_context(user, user_school)
    
    @school_isolation_required
    def list(self, request, *args, **kwargs):
        """List user role assignments with school context validation."""
        return super().list(request, *args, **kwargs)
    
    @api_permission_required('can_assign_roles')
    def create(self, request, *args, **kwargs):
        """Create user role assignment with multi-tenant validation."""
        try:
            user_id = request.data.get('user')
            role_id = request.data.get('role')
            
            from django.contrib.auth import get_user_model
            User = get_user_model()
            
            target_user = User.objects.get(id=user_id)
            role = Role.objects.get(id=role_id)
            
            # Use multi-tenant manager for assignment
            user_role = UserRole.mt_objects.assign_role(
                user=target_user,
                role=role,
                assigned_by=request.user,
                requires_approval=request.data.get('requires_approval', False),
                valid_until=request.data.get('valid_until'),
                notes=request.data.get('notes', '')
            )
            
            serializer = self.get_serializer(user_role)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except (User.DoesNotExist, Role.DoesNotExist) as e:
            return Response(
                {'error': f'User or Role not found: {e}'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        except (PermissionDenied, ValidationError) as e:
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_403_FORBIDDEN
            )
        except Exception as e:
            logger.error(f"User role assignment error: {e}")
            return Response(
                {'error': 'Failed to assign role'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    @api_permission_required('can_assign_roles')
    def approve(self, request, pk=None):
        """Approve a pending role assignment."""
        user_role = self.get_object()
        
        # Validate user can approve this assignment
        if not SchoolContextValidator.validate_role_school_access(request.user, user_role.role):
            raise PermissionDenied("Cannot approve role assignment from different school")
        
        if not user_role.requires_approval:
            return Response(
                {'error': 'Role assignment does not require approval'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if user_role.approved_at:
            return Response(
                {'error': 'Role assignment already approved'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Approve the assignment
        from django.utils import timezone
        user_role.approved_at = timezone.now()
        user_role.approved_by = request.user
        user_role.save()
        
        serializer = self.get_serializer(user_role)
        return Response(serializer.data, status=status.HTTP_200_OK)
    
    @action(detail=True, methods=['post'])
    @api_permission_required('can_assign_roles')
    def revoke(self, request, pk=None):
        """Revoke a role assignment."""
        user_role = self.get_object()
        
        # Validate user can revoke this assignment
        if not SchoolContextValidator.validate_role_school_access(request.user, user_role.role):
            raise PermissionDenied("Cannot revoke role assignment from different school")
        
        # Deactivate the assignment
        user_role.is_active = False
        user_role.save()
        
        serializer = self.get_serializer(user_role)
        return Response(serializer.data, status=status.HTTP_200_OK)
